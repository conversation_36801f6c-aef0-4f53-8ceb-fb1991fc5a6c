import { baseRequestClient, requestClient } from '~/utils/request'

/**
 * 获取用户列表 (需要认证)
 */
export function getUserListApi(params?: {
  page?: number
  size?: number
  keyword?: string
}) {
  return requestClient.get('/users', { params })
}

/**
 * 创建用户 (需要认证)
 */
export function createUserApi(data: {
  username: string
  email: string
  password: string
}) {
  return requestClient.post('/users', data)
}

/**
 * 更新用户 (需要认证)
 */
export function updateUserApi(id: string, data: Partial<{
  username: string
  email: string
  avatar: string
}>) {
  return requestClient.put(`/users/${id}`, data)
}

/**
 * 删除用户 (需要认证)
 */
export function deleteUserApi(id: string) {
  return requestClient.delete(`/users/${id}`)
}

/**
 * 获取公开数据 (不需要认证)
 */
export function getPublicDataApi() {
  return baseRequestClient.get('/public/data')
}

/**
 * 上传文件 (需要认证)
 */
export function uploadFileApi(file: File) {
  const formData = new FormData()
  formData.append('file', file)

  return requestClient.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 批量操作示例 (需要认证)
 */
export function batchOperationApi(data: {
  action: 'delete' | 'update' | 'export'
  ids: string[]
  params?: Record<string, any>
}) {
  return requestClient.post('/batch', data)
}
