import { requestClient } from '~/utils/request'

export interface UploadParams {
  file: File
  uid: string
}

export interface UploadResponse {
  uid: string
  url: string
  name: string
  type: string
  size?: number
}

export async function uploadFile(params: UploadParams): Promise<UploadResponse> {
  const formData = new FormData()
  formData.append('file', params.file)
  formData.append('uid', params.uid)

  return requestClient.post('/feedback/upload/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

export function generateUID(): string {
  return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}
