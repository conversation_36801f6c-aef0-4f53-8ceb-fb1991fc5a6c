import type { Ticket } from '~/types/ticket'

import { requestClient } from '~/utils/request'

/**
 * 工单API路径枚举
 */
export enum TicketApi {
  // 创建工单
  createTicket = '/feedback/createTicket/',
  // 删除工单
  deleteTicket = '/feedback/deleteTicket/',
  // 编辑工单
  editTicket = '/feedback/editTicket/',
  // 工单列表
  getTicketList = '/feedback/selectTicket/',
  // 功能类型列表
  getCategoryList = '/ResponsiblePersons/categoryTree/',
  // 人员列表
  getUserList = '/user/auth/userlist/',
  // 认领工单
  handleTicket = '/feedback/handleTicket/',
  // 指派工单
  assignTicket = '/feedback/assignTicket/',
  // 完成工单
  completeTicket = '/feedback/completeTicket/',
  // 归档工单
  checkTicket = '/feedback/checkTicket/',
  // 驳回工单
  rejectTicket = '/feedback/rejectTicket/',
  // 加急工单
  urgeTicket = '/feedback/urgeTicket/',
}

/**
 * 工单API相关类型定义
 */
export namespace TicketApiTypes {
  /** 工单列表查询参数 */
  export interface GetTicketListParams {
    /** 页码 */
    page?: number
    /** 每页数量 */
    limit?: number
    /** 状态筛选 */
    status?: string
    /** 处理人筛选 */
    handler?: string
    /** 创建人筛选 */
    creator?: string
    /** 标签筛选 */
    labels?: string[]
    /** 搜索关键词 */
    keyword?: string
    /** 开始时间 */
    startTime?: string
    /** 结束时间 */
    endTime?: string
    /** 工单类型 */
    ticketType?: string
  }

  /** 工单列表请求体格式 */
  export interface GetTicketListRequestBody {
    data: {
      limit: number
      page: number
      query: {
        creator?: string
        endTime?: string
        handler?: string
        keyword?: string
        labels?: string[]
        startTime?: string
        status?: string
        ticketType: string
      }
    }
  }

  /** 工单列表响应 */
  export interface GetTicketListResult {
    /** 工单列表 */
    list: Ticket[]
    /** 总数 */
    total: number
    /** 当前页 */
    page: number
    /** 每页数量 */
    pageSize?: number
    /** 每页数量（新格式） */
    limit?: number
  }

  /** 创建工单参数 */
  export interface CreateTicketParams {
    data: {
      /** 问题描述 */
      problemDescription: string
      /** 功能类型 */
      functionType: string[]
      /** 问题类型 */
      endType: string[]
      /** 发生时间 */
      startTime: string
      /** 紧急程度 */
      severityLevel: string
      /** 产品类型 */
      apps: string[]
      /** 产品版本 */
      appVersion: string[]
      /** 设备系统 */
      osType: string[]
      /** 设备型号 */
      mobileType: string[]
      /** TT ID */
      userTtid: string[]
      /** 用户是否编辑内容反馈 */
      isUploadLogs: string
      /** 附件 */
      attachments: Array<{
        uid: string
        url: string
        name: string
        type: string
      }>
    }
  }

  /** 创建工单响应 */
  export interface CreateTicketResponse {
    code: number
    msg: string
    data: {
      problemDescription: string
      functionType: string[]
      endType: string[]
      startTime: string
      severityLevel: string
      apps: string[]
      appVersion: string[]
      osType: string[]
      mobileType: string[]
      userTtid: string[]
      isUploadLogs: string
      attachments: Array<{
        uid: string
        url: string
        name: string
        type: string
      }>
      firstLevelCategory: string
      secondLevelCategory: string
      thirdLevelCategory: string
      feishuGroup: string
      feishuGroupId: string
      businessOwner: string[]
      devOwner: string[]
      clientIOSOwner: string[]
      clientAndroidOwner: string[]
      owner: string[]
      feedbackPerson: string
    }
    status: string
  }

  /** 编辑工单参数 */
  export interface EditTicketParams {
    /** 工单ID */
    id: string
    /** 工单标题 */
    title?: string
    /** 问题描述 */
    text?: string
    /** 状态 */
    status?: string
    /** 处理人 */
    handler?: string
    /** 严重程度 */
    severityLevel?: string
    /** 截止日期 */
    dueDate?: string
    /** 标签 */
    labels?: string[]
    /** 问题原因 */
    cause?: string
    /** 处理结果 */
    result?: string
    /** 附件 */
    attachments?: Array<{
      name: string
      size?: string
      type: string
      url: string
    }>
  }

  /** 删除工单参数 */
  export interface DeleteTicketParams {
    /** 工单ID */
    id: string
  }

  /** 认领工单参数 */
  export interface HandleTicketParams {
    /** 工单ID */
    id: string
    /** 处理人 */
    handler?: string
  }

  /** 指派工单参数 */
  export interface AssignTicketParams {
    /** 工单ID */
    id: string
    /** 指派给的用户 */
    assignee: string

  }

  /** 完成工单参数 */
  export interface CompleteTicketParams {
    /** 工单ID */
    id: string
    /** 处理结果 */
    result?: string
    /** 解决方案 */
    solution?: string
  }

  /** 归档工单参数 */
  export interface CheckTicketParams {
    /** 工单ID */
    id: string
    /** 归档原因 */
    reason?: string
  }

  /** 驳回工单参数 */
  export interface RejectTicketParams {
    /** 工单ID */
    id: string
    /** 驳回原因 */
    reason: string
  }

  /** 加急工单参数 */
  export interface UrgeTicketParams {
    /** 工单ID */
    id: string
    /** 加急原因 */
    reason?: string
    /** 优先级 */
    priority?: string
  }

  /** 用户信息 */
  export interface UserInfo {
    /** 用户ID */
    user_id: string
    /** 用户名 */
    user_name: string
    /** 用户名和ID组合 */
    user_name_id: string
    /** 邮箱 */
    email?: string
    /** 部门名称 */
    deptname?: string
    /** TT ID */
    TT_id?: string
    /** 角色 */
    role?: string
  }

  /** 用户列表查询参数 */
  export interface GetUserListParams {
    /** 搜索关键词 */
    keyword?: string
    /** 页码 */
    page?: number
    /** 每页数量 */
    size?: number
  }

  /** 用户列表响应 */
  export interface GetUserListResult {
    /** 用户列表 */
    list: UserInfo[]
    /** 总数 */
    total?: number
  }

  /** 通用API响应 */
  export interface ApiResponse<T = any> {
    /** 状态码 */
    code: number
    /** 响应消息 */
    message: string
    /** 响应数据 */
    data: T
    /** 是否成功 */
    success: boolean
  }
}

/**
 * 获取工单列表
 * @param params 查询参数
 */
export async function getTicketList(
  params?: TicketApiTypes.GetTicketListParams,
) {
  // 构建新的请求体格式
  const requestBody: TicketApiTypes.GetTicketListRequestBody = {
    data: {
      page: params?.page || 1,
      limit: params?.limit || 10,
      query: {
        ticketType: params?.ticketType || 'allTicket',
        ...(params?.status && { status: params.status }),
        ...(params?.handler && { handler: params.handler }),
        ...(params?.creator && { creator: params.creator }),
        ...(params?.labels && { labels: params.labels }),
        ...(params?.keyword && { keyword: params.keyword }),
        ...(params?.startTime && { startTime: params.startTime }),
        ...(params?.endTime && { endTime: params.endTime }),
      },
    },
  }

  return requestClient.post<TicketApiTypes.GetTicketListResult>(
    TicketApi.getTicketList,
    requestBody,
  )
}

/**
 * 创建工单
 * @param data 工单数据
 */
export async function createTicket(data: TicketApiTypes.CreateTicketParams) {
  return requestClient.post<TicketApiTypes.CreateTicketResponse>(
    TicketApi.createTicket,
    data,
  )
}

/**
 * 编辑工单
 * @param data 工单数据
 */
export async function editTicket(data: TicketApiTypes.EditTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<Ticket>>(
    TicketApi.editTicket,
    data,
  )
}

/**
 * 删除工单
 * @param params 删除参数
 */
export async function deleteTicket(params: any) {
  return requestClient.post(
    TicketApi.deleteTicket,
    { data: params },
  )
}

/**
 * 认领工单
 * @param params 认领参数
 */
export async function handleTicket(params: any) {
  return requestClient.post(
    TicketApi.handleTicket,
    { data: params },
  )
}

/**
 * 指派工单
 * @param params 指派参数
 */
export async function assignTicket(params: any) {
  return requestClient.post(
    TicketApi.assignTicket,
    { data: params },
  )
}

/**
 * 完成工单
 * @param params 完成参数
 */
export async function completeTicket(params: any) {
  return requestClient.post(
    TicketApi.completeTicket,
    { data: params },
  )
}

/**
 * 归档工单
 * @param params 归档参数
 */
export async function checkTicket(params: any) {
  return requestClient.post(
    TicketApi.checkTicket,
    { data: params },
  )
}

/**
 * 驳回工单
 * @param params 驳回参数
 */
export async function rejectTicket(params: any) {
  return requestClient.post(
    TicketApi.rejectTicket,
    { data: params },
  )
}

/**
 * 加急工单
 * @param params 加急参数
 */
export async function urgeTicket(params: any) {
  return requestClient.post(
    TicketApi.urgeTicket,
    { data: params },
  )
}

/**
 * 获取功能类型列表
 */
export async function getCategoryList() {
  return requestClient.get<any[]>(TicketApi.getCategoryList)
}

/**
 * 获取用户列表
 * @param params 查询参数
 */
export async function getUserList(params?: TicketApiTypes.GetUserListParams) {
  return requestClient.get<TicketApiTypes.UserInfo[]>(TicketApi.getUserList, { params })
}
