import type { ApiUserInfo, LoginParams, LoginResult, RefreshTokenResult, UserInfo } from '~/types/auth'
import { baseRequestClient, requestClient } from '~/utils/request'

/**
 * 用户登录
 */
export function loginApi(data: LoginParams) {
  return baseRequestClient.post<LoginResult>('/user/login', data, {
    withCredentials: true,
  })
}

/**
 * 刷新访问令牌
 */
export function refreshTokenApi() {
  return baseRequestClient.post<RefreshTokenResult>('/user/refresh', null, {
    withCredentials: true,
  })
}

/**
 * 用户登出
 */
export function logoutApi() {
  return baseRequestClient.post('/user/logout', null, {
    withCredentials: true,
  })
}

/**
 * 获取用户信息
 */
export async function getUserInfoApi(): Promise<UserInfo> {
  const response = await requestClient.get<ApiUserInfo>('/user/info')

  // 转换API响应格式为内部UserInfo格式
  const roles = response.roles?.map(r => r.value) || ['User']
  const primaryRole = roles[0] || 'User'

  return {
    id: response.user_id,
    username: response.user_name,
    email: response.email,
    avatar: '',
    roles,
    permissions: getPermissionsByRole(primaryRole),
    realName: response.user_name,
    homePath: getHomePathByRole(primaryRole),
    // 保留原始字段以兼容
    user_id: response.user_id,
    TT_id: response.TT_id,
    deptname: response.deptname,
    role: primaryRole,
  }
}

/**
 * 根据角色获取权限码
 */
function getPermissionsByRole(role: string): string[] {
  const roleAccessMap: Record<string, string[]> = {
    Super: ['*'],
    Admin: ['admin:*', 'user:read', 'user:write'],
    Manager: ['user:read', 'user:write', 'report:read'],
    User: ['user:read'],
  }
  return roleAccessMap[role] || ['user:read']
}

/**
 * 根据角色获取首页路径
 */
function getHomePathByRole(role: string): string {
  const roleHomeMap: Record<string, string> = {
    Super: '/',
    Admin: '/dashboard',
    User: '/',
    Manager: '/dashboard',
  }
  return roleHomeMap[role] || '/'
}

/**
 * 获取用户权限码
 * 注意：此接口可能在真实API中不存在，权限码通过角色映射获取
 */
export function getAccessCodesApi() {
  // 如果真实API中没有此接口，可以基于用户角色返回权限码
  throw new Error('此接口在真实API中不存在，权限码通过角色映射获取')
}

/**
 * 获取用户菜单
 * 注意：此接口可能在真实API中不存在，菜单通过角色映射获取
 */
export function getUserMenusApi() {
  // 如果真实API中没有此接口，可以基于用户角色返回菜单
  throw new Error('此接口在真实API中不存在，菜单通过角色映射获取')
}
