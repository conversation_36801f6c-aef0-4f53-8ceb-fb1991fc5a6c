export default defineNuxtRouteMiddleware(async (to) => {
  const authStore = useAuthStore()
  const accessStore = useAccessStore()

  // 检查是否有 token
  if (!accessStore.accessToken) {
    const redirectPath = to.fullPath !== '/' ? to.fullPath : undefined
    return navigateTo({
      path: '/login',
      query: redirectPath ? { redirect: redirectPath } : {},
    })
  }

  // 检查认证状态
  if (!authStore.isAuthenticated) {
    return navigateTo({
      path: '/login',
      query: { redirect: to.fullPath },
    })
  }
})
