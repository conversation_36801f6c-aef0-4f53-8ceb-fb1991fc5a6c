import type { RouteRecordRaw } from 'vue-router'
import { defineStore } from 'pinia'

interface AccessState {
  accessToken: string | null
  refreshToken: string | null
  accessCodes: string[]
  accessMenus: any[]
  accessRoutes: RouteRecordRaw[]
  isAccessChecked: boolean
  loginExpired: boolean
}

export const useAccessStore = defineStore('access', {
  state: (): AccessState => ({
    accessToken: null,
    refreshToken: null,
    accessCodes: [],
    accessMenus: [],
    accessRoutes: [],
    isAccessChecked: false,
    loginExpired: false,
  }),

  getters: {
    isAuthenticated: state => !!state.accessToken,
    hasAccessCodes: state => state.accessCodes.length > 0,
  },

  actions: {
    /**
     * 设置访问令牌
     */
    setAccessToken(token: string | null) {
      this.accessToken = token

      // 手动保存到localStorage
      if (import.meta.client) {
        if (token) {
          localStorage.setItem('access-token', token)
        }
        else {
          localStorage.removeItem('access-token')
        }
      }
    },

    /**
     * 设置刷新令牌
     */
    setRefreshToken(token: string | null) {
      this.refreshToken = token
    },

    /**
     * 设置权限码
     */
    setAccessCodes(codes: string[]) {
      this.accessCodes = codes
    },

    /**
     * 设置访问菜单
     */
    setAccessMenus(menus: any[]) {
      this.accessMenus = menus
    },

    /**
     * 设置访问路由
     */
    setAccessRoutes(routes: RouteRecordRaw[]) {
      this.accessRoutes = routes
    },

    /**
     * 设置访问检查状态
     */
    setIsAccessChecked(checked: boolean) {
      this.isAccessChecked = checked
    },

    /**
     * 设置登录过期状态
     */
    setLoginExpired(expired: boolean) {
      this.loginExpired = expired
    },

    /**
     * 根据路径获取菜单
     */
    getMenuByPath(path: string) {
      function findMenu(menus: any[], targetPath: string): any {
        for (const menu of menus) {
          if (menu.path === targetPath) {
            return menu
          }
          if (menu.children) {
            const found = findMenu(menu.children, targetPath)
            if (found)
              return found
          }
        }
        return null
      }
      return findMenu(this.accessMenus, path)
    },

    /**
     * 检查是否有权限码
     */
    hasPermission(codes: string | string[]) {
      const targetCodes = Array.isArray(codes) ? codes : [codes]
      return targetCodes.some(code => this.accessCodes.includes(code))
    },

    /**
     * 从localStorage恢复数据
     */
    restoreFromStorage() {
      if (import.meta.client) {
        try {
          const savedToken = localStorage.getItem('access-token')
          if (savedToken) {
            this.accessToken = savedToken
          }
        }
        catch (error) {
          console.error('Failed to restore access data:', error)
        }
      }
    },

    /**
     * 清除所有数据
     */
    clearAll() {
      this.accessToken = null
      this.refreshToken = null
      this.accessCodes = []
      this.accessMenus = []
      this.accessRoutes = []
      this.isAccessChecked = false
      this.loginExpired = false

      // 清除localStorage
      if (import.meta.client) {
        localStorage.removeItem('access-token')
      }
    },

    /**
     * 重置状态
     */
    $reset() {
      this.clearAll()
    },
  },

  persist: true,
})
