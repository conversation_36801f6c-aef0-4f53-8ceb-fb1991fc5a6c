import type { LoginParams, UserInfo } from '~/types/auth'
import { defineStore } from 'pinia'
import { getUserInfoApi, loginApi, logoutApi, refreshTokenApi } from '~/api/auth'

interface AuthState {
  user: UserInfo | null
  isLoggedIn: boolean
  loginLoading: boolean
  loginExpired: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    isLoggedIn: false,
    loginLoading: false,
    loginExpired: false,
  }),

  getters: {
    userInfo: state => state.user,
    userRoles: state => state.user?.roles || [],
    isAuthenticated: state => state.isLoggedIn && !!state.user && !state.loginExpired,
    homePath: state => state.user?.homePath || '/',
  },

  actions: {
    /**
     * 用户登录 - 参考 itmp-vben 项目实现
     */
    async login(params: LoginParams, onSuccess?: () => Promise<void>) {
      this.loginLoading = true
      let userInfo: UserInfo | null = null

      try {
        const result = await loginApi(params)

        if (result.token) {
          // 存储 token
          const accessStore = useAccessStore()
          accessStore.setAccessToken(result.token)

          // 转换API响应为UserInfo格式
          userInfo = {
            id: result.user_id,
            username: result.user_name,
            email: result.email,
            realName: result.user_name,
            roles: [result.role],
            permissions: result.role === 'Super' ? ['*'] : [],
            homePath: this.getHomePathByRole(result.role),
            // 保留原始字段
            user_id: result.user_id,
            TT_id: result.TT_id,
            deptname: result.deptname,
            role: result.role,
          }

          // 设置用户信息
          this.setUserInfo(userInfo)
          this.isLoggedIn = true
          this.loginExpired = false

          // 设置权限码
          const accessCodes = this.getAccessCodesByRole(result.role)
          accessStore.setAccessCodes(accessCodes)
          accessStore.setIsAccessChecked(true)

          // 处理登录成功后的跳转
          // 清除登录过期状态
          if (accessStore.loginExpired) {
            accessStore.setLoginExpired(false)
          }

          // 总是执行跳转逻辑，无论是否之前过期
          if (onSuccess) {
            await onSuccess()
          }
          else {
            await this.handleLoginSuccess(userInfo)
          }

          // 显示登录成功提示
          this.showLoginSuccessNotification(userInfo)

          return { userInfo, result }
        }
      }
      catch (error: any) {
        // 登录失败处理
        this.handleLoginError(error)
        throw error
      }
      finally {
        this.loginLoading = false
      }

      return { userInfo }
    },

    /**
     * 用户登出
     */
    async logout(redirect = true) {
      try {
        await logoutApi()
      }
      catch (_error) {
        console.error('Logout API failed:', _error)
      }

      // 清除本地状态
      this.clearAuthData()

      // 重定向到登录页
      if (redirect && import.meta.client) {
        const route = useRoute()
        const redirectPath = route.fullPath !== '/' ? route.fullPath : undefined

        await navigateTo({
          path: '/login',
          query: redirectPath ? { redirect: redirectPath } : {},
        })
      }
    },

    /**
     * 获取用户信息
     */
    async fetchUserInfo() {
      try {
        const userInfo = await getUserInfoApi()
        this.setUserInfo(userInfo)
        this.isLoggedIn = true
        this.loginExpired = false

        // 确保权限状态也被正确设置
        const accessStore = useAccessStore()
        if (!accessStore.isAccessChecked && userInfo.roles) {
          const accessCodes = this.getAccessCodesByRole(userInfo.roles[0] || 'User')
          accessStore.setAccessCodes(accessCodes)
          accessStore.setIsAccessChecked(true)
        }

        return userInfo
      }
      catch (error) {
        this.clearAuthData()
        throw error
      }
    },

    /**
     * 刷新 token
     */
    async refreshToken(): Promise<string> {
      try {
        const result = await refreshTokenApi()
        const accessStore = useAccessStore()
        // 根据新的API响应格式处理token
        const token = result.token || result.accessToken
        if (!token) {
          throw new Error('No token received from refresh API')
        }
        accessStore.setAccessToken(token)
        return token
      }
      catch (error) {
        this.clearAuthData()
        throw error
      }
    },

    /**
     * 设置用户信息
     */
    setUserInfo(userInfo: UserInfo) {
      this.user = userInfo
      this.isLoggedIn = true

      // 手动保存到localStorage
      if (import.meta.client) {
        localStorage.setItem('auth-user', JSON.stringify(userInfo))
        localStorage.setItem('auth-isLoggedIn', 'true')
      }

      // 同步到用户 store
      const userStore = useUserStore()
      userStore.setUserInfo(userInfo)
    },

    /**
     * 从localStorage恢复认证数据
     */
    restoreFromStorage() {
      if (import.meta.client) {
        try {
          const savedUser = localStorage.getItem('auth-user')
          const savedIsLoggedIn = localStorage.getItem('auth-isLoggedIn')

          if (savedUser && savedIsLoggedIn === 'true') {
            this.user = JSON.parse(savedUser)
            this.isLoggedIn = true
          }
        }
        catch (error) {
          console.error('Failed to restore auth data:', error)
          this.clearAuthData()
        }
      }
    },

    /**
     * 清除认证数据
     */
    clearAuthData() {
      this.user = null
      this.isLoggedIn = false

      // 清除localStorage
      if (import.meta.client) {
        localStorage.removeItem('auth-user')
        localStorage.removeItem('auth-isLoggedIn')
      }

      // 清除 access store
      const accessStore = useAccessStore()
      accessStore.clearAll()

      // 清除 user store
      const userStore = useUserStore()
      userStore.clearUserInfo()
    },

    /**
     * 检查认证状态
     */
    async checkAuth() {
      const accessStore = useAccessStore()

      if (!accessStore.accessToken) {
        return false
      }

      try {
        await this.fetchUserInfo()
        return true
      }
      catch {
        this.clearAuthData()
        return false
      }
    },

    /**
     * 根据角色获取首页路径
     */
    getHomePathByRole(role: string): string {
      const roleHomeMap: Record<string, string> = {
        Super: '/',
        Admin: '/dashboard',
        User: '/',
        Manager: '/dashboard',
      }
      return roleHomeMap[role] || '/'
    },

    /**
     * 根据角色获取权限码
     */
    getAccessCodesByRole(role: string): string[] {
      const roleAccessMap: Record<string, string[]> = {
        Super: ['*'], // 超级管理员拥有所有权限
        Admin: ['admin:*', 'user:read', 'user:write'],
        Manager: ['user:read', 'user:write', 'report:read'],
        User: ['user:read'],
      }
      return roleAccessMap[role] || []
    },

    /**
     * 处理登录成功后的跳转
     */
    async handleLoginSuccess(userInfo: UserInfo) {
      const route = useRoute()
      const router = useRouter()

      // 获取重定向路径
      const redirectPath = (route.query.redirect as string) || userInfo.homePath || '/'

      // 跳转到目标页面
      await router.push(redirectPath)
    },

    /**
     * 显示登录成功通知
     */
    showLoginSuccessNotification(userInfo: UserInfo) {
      if (import.meta.client) {
        const { success } = useNotification()
        success(
          `欢迎回来，${userInfo.realName || userInfo.username}！`,
          '登录成功',
        )
      }
    },

    /**
     * 处理登录错误
     */
    handleLoginError(error: any) {
      // 清除认证状态
      this.isLoggedIn = false
      this.user = null
      this.loginExpired = false

      // 清除存储的token
      const accessStore = useAccessStore()
      accessStore.clearAll()

      // 记录错误日志
      console.error('Login failed:', error)

      // 显示错误通知
      if (import.meta.client) {
        const { error: showError } = useNotification()

        let errorMessage = '登录失败，请稍后重试'

        // 根据错误类型显示不同的错误信息
        if (error.statusCode === 401) {
          errorMessage = '用户名或密码错误'
        }
        else if (error.statusCode === 403) {
          errorMessage = '账户已被锁定，请联系管理员'
        }
        else if (error.statusCode === 429) {
          errorMessage = '登录尝试次数过多，请稍后再试'
        }
        else if (error.message) {
          errorMessage = error.message
        }

        showError(errorMessage, '登录失败')
      }
    },

    /**
     * 设置登录过期状态
     */
    setLoginExpired(expired: boolean) {
      this.loginExpired = expired
      if (expired) {
        const accessStore = useAccessStore()
        accessStore.setLoginExpired(true)
      }
    },

    /**
     * 重置状态
     */
    $reset() {
      this.user = null
      this.isLoggedIn = false
      this.loginLoading = false
      this.loginExpired = false
    },
  },

  persist: true,
})
