import type { UserInfo } from '~/types/auth'
import { defineStore } from 'pinia'

interface UserState {
  userInfo: UserInfo | null
  userRoles: string[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    userRoles: [],
  }),

  getters: {
    userId: state => state.userInfo?.id,
    username: state => state.userInfo?.username,
    email: state => state.userInfo?.email,
    avatar: state => state.userInfo?.avatar,
    roles: state => state.userRoles,
    homePath: state => state.userInfo?.homePath || '/',
  },

  actions: {
    /**
     * 设置用户信息
     */
    setUserInfo(userInfo: UserInfo | null) {
      this.userInfo = userInfo
      this.userRoles = userInfo?.roles || []
    },

    /**
     * 设置用户角色
     */
    setUserRoles(roles: string[]) {
      this.userRoles = roles
      if (this.userInfo) {
        this.userInfo.roles = roles
      }
    },

    /**
     * 更新用户信息
     */
    updateUserInfo(updates: Partial<UserInfo>) {
      if (this.userInfo) {
        this.userInfo = { ...this.userInfo, ...updates }
      }
    },

    /**
     * 检查用户是否有指定角色
     */
    hasRole(role: string | string[]) {
      const targetRoles = Array.isArray(role) ? role : [role]
      return targetRoles.some(r => this.userRoles.includes(r))
    },

    /**
     * 检查用户是否有任意一个角色
     */
    hasAnyRole(roles: string[]) {
      return roles.some(role => this.userRoles.includes(role))
    },

    /**
     * 检查用户是否有所有角色
     */
    hasAllRoles(roles: string[]) {
      return roles.every(role => this.userRoles.includes(role))
    },

    /**
     * 清除用户信息
     */
    clearUserInfo() {
      this.userInfo = null
      this.userRoles = []
    },

    /**
     * 重置状态
     */
    $reset() {
      this.clearUserInfo()
    },
  },

  persist: {
    pick: ['userInfo', 'userRoles'],
  },
})
