#!/usr/bin/env node

const { spawn } = require('node:child_process')
const process = require('node:process')
const killPort = require('kill-port')

const PORT = 3456
const HOST = 'localhost'

/**
 * 终止占用端口的进程
 */
async function killProcessOnPort(port) {
  try {
    console.log(`🔧 检查端口 ${port} 是否被占用...`)
    await killPort(port)
    console.log(`✅ 端口 ${port} 已清理完成`)
  }
  catch (error) {
    // 如果端口没有被占用，kill-port 会抛出错误，这是正常的
    console.log(`ℹ️  端口 ${port} 未被占用或已清理`)
  }
}

/**
 * 启动开发服务器
 */
function startDevServer() {
  console.log(`🚀 启动开发服务器在 http://${HOST}:${PORT}`)

  const child = spawn('pnpm', ['dev:nuxt'], {
    stdio: 'inherit',
    shell: true,
  })

  // 处理进程退出
  child.on('close', (code) => {
    if (code !== 0) {
      console.log(`❌ 开发服务器退出，退出码: ${code}`)
    }
  })

  // 处理 Ctrl+C 等信号
  process.on('SIGINT', () => {
    console.log('\n🛑 正在停止开发服务器...')
    child.kill('SIGINT')
    process.exit(0)
  })

  process.on('SIGTERM', () => {
    console.log('\n🛑 正在停止开发服务器...')
    child.kill('SIGTERM')
    process.exit(0)
  })
}

/**
 * 主函数
 */
async function main() {
  console.log(`🚀 启动开发服务器...`)

  // 检查并终止占用端口的进程
  await killProcessOnPort(PORT)

  // 启动开发服务器
  startDevServer()
}

// 运行主函数
main().catch(console.error)
