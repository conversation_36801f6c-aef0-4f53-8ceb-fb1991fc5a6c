<script setup lang="ts">
import { MessageCircle, Minimize2, Send, X } from 'lucide-vue-next'

interface Props {
  userId?: number
  userName?: string
  userAvatar?: string
  minimized?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  userId: 1,
  userName: '客服',
  userAvatar: '/avatars/01.png',
  minimized: false,
})

const emit = defineEmits<{
  close: []
  minimize: []
  maximize: []
}>()

// 聊天状态
const isMinimized = ref(props.minimized)
const messages = ref<any[]>([
  {
    id: 1,
    senderId: props.userId,
    senderName: props.userName,
    content: '您好！有什么可以帮助您的吗？',
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'text',
  },
])
const newMessage = ref('')

// 方法
function sendMessage() {
  if (!newMessage.value.trim())
    return

  const message = {
    id: messages.value.length + 1,
    senderId: 'me',
    senderName: '我',
    content: newMessage.value,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'text',
  }

  messages.value.push(message)
  newMessage.value = ''

  nextTick(() => {
    scrollToBottom()
  })

  // 模拟自动回复
  simulateAutoReply()
}

function simulateAutoReply() {
  const responses = [
    '好的，我明白了',
    '让我为您查询一下',
    '请稍等，我来帮您处理',
    '这个问题我需要确认一下',
    '感谢您的反馈',
    '还有其他需要帮助的吗？',
  ]

  const randomResponse = responses[Math.floor(Math.random() * responses.length)]

  setTimeout(() => {
    const replyMessage = {
      id: messages.value.length + 1,
      senderId: props.userId,
      senderName: props.userName,
      content: randomResponse,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      type: 'text',
    }

    messages.value.push(replyMessage)

    nextTick(() => {
      scrollToBottom()
    })
  }, 1000 + Math.random() * 2000)
}

function scrollToBottom() {
  const messagesContainer = document.querySelector('.chat-widget-messages')
  if (messagesContainer) {
    messagesContainer.scrollTop = messagesContainer.scrollHeight
  }
}

function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

function toggleMinimize() {
  isMinimized.value = !isMinimized.value
  if (isMinimized.value) {
    emit('minimize')
  }
  else {
    emit('maximize')
  }
}

function closeChat() {
  emit('close')
}

// 监听 props 变化
watch(() => props.minimized, (newVal) => {
  isMinimized.value = newVal
})
</script>

<template>
  <div class="fixed bottom-4 right-4 z-50">
    <!-- 最小化状态 -->
    <div v-if="isMinimized" class="cursor-pointer rounded-full bg-primary p-3 text-primary-foreground shadow-lg transition-transform hover:scale-105" @click="toggleMinimize">
      <MessageCircle class="h-6 w-6" />
      <div v-if="messages.length > 1" class="absolute h-5 w-5 flex items-center justify-center rounded-full bg-destructive text-xs text-destructive-foreground -right-1 -top-1">
        {{ messages.length - 1 }}
      </div>
    </div>

    <!-- 展开状态 -->
    <div v-else class="h-96 w-80 flex flex-col border rounded-lg bg-background shadow-xl">
      <!-- 头部 -->
      <div class="flex items-center justify-between border-b rounded-t-lg bg-primary p-3 text-primary-foreground">
        <div class="flex items-center gap-2">
          <Avatar class="h-6 w-6">
            <AvatarImage :src="userAvatar" :alt="userName" />
            <AvatarFallback class="text-xs">
              {{ userName?.charAt(0) }}
            </AvatarFallback>
          </Avatar>
          <span class="text-sm font-medium">{{ userName }}</span>
        </div>
        <div class="flex items-center gap-1">
          <Button size="icon" variant="ghost" class="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20" @click="toggleMinimize">
            <Minimize2 class="h-3 w-3" />
          </Button>
          <Button size="icon" variant="ghost" class="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20" @click="closeChat">
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>

      <!-- 消息列表 -->
      <ScrollArea class="chat-widget-messages flex-1">
        <div class="p-3 space-y-3">
          <div
            v-for="message in messages"
            :key="message.id"
            class="flex"
            :class="message.senderId === 'me' ? 'justify-end' : 'justify-start'"
          >
            <div
              class="max-w-[80%] rounded-lg px-3 py-2 text-sm"
              :class="message.senderId === 'me'
                ? 'bg-primary text-primary-foreground'
                : 'bg-muted'"
            >
              <p>{{ message.content }}</p>
              <p class="mt-1 text-xs opacity-70">
                {{ message.time }}
              </p>
            </div>
          </div>
        </div>
      </ScrollArea>

      <!-- 输入框 -->
      <div class="border-t p-3">
        <div class="flex gap-2">
          <Input
            v-model="newMessage"
            placeholder="输入消息..."
            class="flex-1 text-sm"
            @keydown="handleKeyPress"
          />
          <Button size="sm" :disabled="!newMessage.trim()" @click="sendMessage">
            <Send class="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
