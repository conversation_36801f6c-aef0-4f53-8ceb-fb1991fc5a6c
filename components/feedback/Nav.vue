<script lang="ts" setup>
import { buttonVariants } from '~/components/ui/button'

export interface LinkProp {
  title: string
  label?: string
  icon: string
  variant: 'default' | 'ghost'
  key?: string // 添加key用于标识菜单项
}

interface NavProps {
  isCollapsed: boolean
  links: LinkProp[]
  activeMenu?: string // 当前激活的菜单
}

defineProps<NavProps>()

// 定义emit事件
const emit = defineEmits<{
  (e: 'menuClick', menuKey: string): void
}>()

// 处理菜单点击
function handleMenuClick(link: LinkProp) {
  if (link.key) {
    emit('menuClick', link.key)
  }
}
</script>

<template>
  <div
    :data-collapsed="isCollapsed"
    class="group h-full flex flex-col gap-4 py-2 data-[collapsed=true]:py-2"
  >
    <nav class="grid gap-1 px-2 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2">
      <template v-for="(link, index) of links">
        <Tooltip v-if="isCollapsed" :key="`1-${index}`" :delay-duration="0">
          <TooltipTrigger as-child>
            <button
              :class="cn(
                buttonVariants({
                  variant: link.key === activeMenu ? 'default' : 'ghost',
                  size: 'icon',
                }),
                'h-9 w-9',
                link.key === activeMenu
                  && 'dark:bg-muted dark:text-muted-foreground dark:hover:bg-muted dark:hover:text-white',
              )"
              @click="handleMenuClick(link)"
            >
              <Icon :name="link.icon" class="size-4" />
              <span class="sr-only">{{ link.title }}</span>
            </button>
          </TooltipTrigger>
          <TooltipContent side="right" class="flex items-center gap-4">
            {{ link.title }}
            <span v-if="link.label" class="ml-auto text-muted-foreground">
              {{ link.label }}
            </span>
          </TooltipContent>
        </Tooltip>

        <button
          v-else
          :key="`2-${index}`"
          :class="cn(
            buttonVariants({
              variant: link.key === activeMenu ? 'default' : 'ghost',
              size: 'sm',
            }),
            link.key === activeMenu
              && 'dark:bg-muted dark:text-white dark:hover:bg-muted dark:hover:text-white',
            'justify-start w-full',
          )"
          @click="handleMenuClick(link)"
        >
          <Icon :name="link.icon" class="mr-2 size-4" />
          {{ link.title }}
          <span
            v-if="link.label"
            :class="cn(
              'ml-auto',
              link.key === activeMenu
                && 'text-background dark:text-white',
            )"
          >
            {{ link.label }}
          </span>
        </button>
      </template>
    </nav>
  </div>
</template>
