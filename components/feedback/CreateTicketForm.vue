<script setup lang="ts">
import type { TicketApiTypes } from '~/api/feedback/ticket'
import type { AttachmentItem } from '~/components/ui/attachment-viewer'
import { createTicket, getCategoryList } from '~/api/feedback/ticket'
import { AttachmentUploaderSimple } from '~/components/ui/attachment-uploader'
import { AttachmentViewer } from '~/components/ui/attachment-viewer'
import { CascadeDropdown } from '~/components/ui/cascade-dropdown'
import { Checkbox } from '~/components/ui/checkbox'
import { DateTimePicker } from '~/components/ui/date-time-picker'
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group'
import { TagsInput, TagsInputInput, TagsInputItem, TagsInputItemDelete, TagsInputItemText } from '~/components/ui/tags-input'

interface CreateTicketFormProps {
  open: boolean
}

interface CreateTicketFormEmits {
  (e: 'update:open', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<CreateTicketFormProps>()
const emit = defineEmits<CreateTicketFormEmits>()

const notification = useNotification()

// 上传文件状态
interface UploadingFile {
  uid: string
  file: File
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

const uploadingFiles = ref<UploadingFile[]>([])

// 表单数据
const formData = ref<TicketApiTypes.CreateTicketParams['data']>({
  problemDescription: '',
  functionType: [],
  endType: [],
  startTime: '',
  severityLevel: '',
  apps: [],
  appVersion: [],
  osType: [],
  mobileType: [],
  userTtid: [],
  isUploadLogs: '未反馈',
  attachments: [],
})

// 表单选项
const endTypeOptions = [
  {
    label: '服务端问题',
    value: '服务端',
    description: '后台服务、API接口、数据库等问题',
  },
  {
    label: '客户端问题',
    value: '客户端',
    description: '应用界面、功能操作、性能等问题',
  },
]

const severityLevelOptions = [
  {
    label: '个例问题',
    value: '个例问题',
    description: '影响少数用户，可延后处理',
    color: 'text-blue-600',
  },
  {
    label: '多例问题',
    value: '多例问题',
    description: '影响多数用户，需优先处理',
    color: 'text-red-600',
  },
]

const appsOptions = [
  {
    label: 'TT语音',
    value: 'TT语音',
    icon: 'i-lucide-mic',
    description: '语音聊天应用',
  },
  {
    label: '欢游',
    value: '欢游',
    icon: 'i-lucide-gamepad-2',
    description: '游戏社交平台',
  },
  {
    label: '试境',
    value: '试境',
    icon: 'i-lucide-test-tube',
    description: '测试环境应用',
  },
  {
    label: '其可',
    value: '其可',
    icon: 'i-lucide-zap',
    description: '其他产品应用',
  },
]

const osTypeOptions = [
  {
    label: 'Android',
    value: 'Android',
    icon: 'i-lucide-smartphone',
    description: '安卓手机/平板',
  },
  {
    label: 'iOS',
    value: 'iOS',
    icon: 'i-lucide-smartphone',
    description: 'iPhone/iPad',
  },
  {
    label: 'PC',
    value: 'PC',
    icon: 'i-lucide-monitor',
    description: 'Windows/Mac电脑',
  },
]

const isUploadLogsOptions = [
  {
    label: '未反馈',
    value: '未反馈',
    description: '用户未在应用内反馈此问题',
  },
  {
    label: '已反馈',
    value: '已反馈',
    description: '用户已在应用内反馈此问题',
  },
]

// 常用版本选项
const commonVersions = ['1.0.0', '1.1.0', '1.2.0', '2.0.0', '2.1.0']

// 常用设备型号
const commonDevices = [
  'iPhone 15 Pro',
  'iPhone 14',
  'Samsung Galaxy S24',
  'Xiaomi 14',
  'OPPO Find X7',
  'vivo X100',
  'Windows 11',
  'macOS Sonoma',
]

// 功能类型数据
interface CategoryItem {
  id: string | number
  name: string
  children?: CategoryItem[]
}

const categoryData = ref<CategoryItem[]>([])
const categoryLoading = ref(false)

// 获取功能类型数据
async function fetchCategoryData() {
  try {
    categoryLoading.value = true
    const response = await getCategoryList()

    // 处理API响应数据，转换为级联选择器需要的格式
    if (response && Array.isArray(response)) {
      categoryData.value = response.map((item: any) => ({
        id: item.id || item.value,
        name: item.name || item.label,
        children: item.children?.map((child: any) => ({
          id: child.id || child.value,
          name: child.name || child.label,
          children: child.children?.map((grandChild: any) => ({
            id: grandChild.id || grandChild.value,
            name: grandChild.name || grandChild.label,
          })) || [],
        })) || [],
      }))
    }
  }
  catch (error) {
    console.error('获取功能类型数据失败:', error)
    notification.error('获取功能类型数据失败', '加载失败')
  }
  finally {
    categoryLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCategoryData()
})

// 表单验证
const isFormValid = computed(() => {
  return (
    formData.value.problemDescription.trim() !== ''
    && formData.value.functionType.length > 0
    && formData.value.endType.length > 0
    && formData.value.startTime !== ''
    && formData.value.severityLevel !== ''
    && formData.value.apps.length > 0
    && formData.value.osType.length > 0
  )
})

// 提交状态
const isSubmitting = ref(false)

// 重置表单
function resetForm() {
  formData.value = {
    problemDescription: '',
    functionType: [],
    endType: [],
    startTime: '',
    severityLevel: '',
    apps: [],
    appVersion: [],
    osType: [],
    mobileType: [],
    userTtid: [],
    isUploadLogs: '未反馈',
    attachments: [],
  }
}

// 处理表单提交
async function handleSubmit() {
  if (!isFormValid.value) {
    notification.error('请填写所有必填字段', '表单验证失败')
    return
  }

  try {
    isSubmitting.value = true

    const response = await createTicket({
      data: formData.value,
    })

    // 由于响应拦截器在成功时返回 data.data，所以这里直接判断响应存在即表示成功
    if (response) {
      notification.success('工单已成功创建', '创建成功')

      emit('success')
      emit('update:open', false)
      resetForm()
    }
    else {
      throw new Error('创建工单失败')
    }
  }
  catch (error) {
    console.error('创建工单失败:', error)
    notification.error(
      error instanceof Error ? error.message : '创建工单时发生错误',
      '创建失败',
    )
  }
  finally {
    isSubmitting.value = false
  }
}

// 处理取消
function handleCancel() {
  emit('update:open', false)
  resetForm()
}

// 附件数据转换
const attachmentItems = computed<AttachmentItem[]>(() => {
  return formData.value.attachments.map(attachment => ({
    id: attachment.uid,
    name: attachment.name,
    url: attachment.url,
    type: attachment.type,
    size: undefined, // 原始数据中没有size字段
  }))
})

// 处理附件列表更新
function handleAttachmentsUpdate(attachments: AttachmentItem[]) {
  formData.value.attachments = attachments.map(attachment => ({
    uid: attachment.id,
    name: attachment.name,
    url: attachment.url,
    type: attachment.type,
  }))
}

// 处理附件上传成功
function handleUploadSuccess(_attachment: AttachmentItem) {
  // 附件上传成功处理
}

// 处理附件上传失败
function handleUploadError(error: string) {
  console.error('附件上传失败:', error)
  notification.error(`附件上传失败: ${error}`, '上传失败')
}

// 处理附件删除
function handleAttachmentDelete(attachment: AttachmentItem) {
  const newAttachments = formData.value.attachments.filter(item => item.uid !== attachment.id)
  formData.value.attachments = newAttachments
}

// 处理删除上传中的文件
function handleDeleteUploadingFile(file: File) {
  uploadingFiles.value = uploadingFiles.value.filter(f => f.file !== file)
}

// 处理重试上传
function handleRetryUpload(_file: File) {
  // 这个方法会被 AttachmentUploaderSimple 组件处理
}

// 添加常用版本
function addCommonVersion(version: string) {
  if (!formData.value.appVersion.includes(version)) {
    formData.value.appVersion.push(version)
  }
}

// 添加常用设备
function addCommonDevice(device: string) {
  if (!formData.value.mobileType.includes(device)) {
    formData.value.mobileType.push(device)
  }
}

// 表单字段验证状态
const fieldValidation = computed(() => ({
  problemDescription: {
    isValid: formData.value.problemDescription.trim().length >= 10,
    message: formData.value.problemDescription.trim().length === 0
      ? '请描述遇到的问题'
      : formData.value.problemDescription.trim().length < 10
        ? '问题描述至少需要10个字符'
        : '',
  },
  functionType: {
    isValid: formData.value.functionType.length > 0,
    message: formData.value.functionType.length === 0 ? '请选择功能类型' : '',
  },
  endType: {
    isValid: formData.value.endType.length > 0,
    message: formData.value.endType.length === 0 ? '请选择问题类型' : '',
  },
  startTime: {
    isValid: formData.value.startTime !== '',
    message: formData.value.startTime === '' ? '请选择问题发生时间' : '',
  },
  severityLevel: {
    isValid: formData.value.severityLevel !== '',
    message: formData.value.severityLevel === '' ? '请选择紧急程度' : '',
  },
  apps: {
    isValid: formData.value.apps.length > 0,
    message: formData.value.apps.length === 0 ? '请选择产品类型' : '',
  },
  osType: {
    isValid: formData.value.osType.length > 0,
    message: formData.value.osType.length === 0 ? '请选择设备系统' : '',
  },
}))
</script>

<template>
  <Dialog :open="props.open" @update:open="emit('update:open', $event)">
    <DialogContent class="grid grid-rows-[auto_minmax(0,1fr)_auto] max-h-[90vh] max-w-2xl overflow-hidden p-0">
      <!-- 固定顶部 -->
      <DialogHeader class="border-b px-6 py-4">
        <DialogTitle class="text-xl font-semibold">
          创建工单
        </DialogTitle>
        <DialogDescription class="text-sm text-muted-foreground">
          请填写工单信息，带 <span class="text-destructive font-medium">*</span> 的字段为必填项
        </DialogDescription>
      </DialogHeader>

      <!-- 可滚动内容区域 -->
      <ScrollArea>
        <div class="px-8">
          <form class="space-y-6">
            <!-- 问题描述 -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <Label for="problemDescription" class="text-sm font-medium">
                  问题描述 <span class="text-destructive">*</span>
                </Label>
                <div class="flex items-center gap-2 text-xs text-muted-foreground">
                  <span :class="formData.problemDescription.length >= 10 ? 'text-green-600' : 'text-amber-600'">
                    {{ formData.problemDescription.length }}/500
                  </span>
                  <Icon
                    v-if="formData.problemDescription.length >= 10"
                    name="i-lucide-check-circle"
                    class="h-3 w-3 text-green-600"
                  />
                </div>
              </div>
              <Textarea
                id="problemDescription"
                v-model="formData.problemDescription"
                placeholder="请详细描述遇到的问题"
                class="min-h-[80px] resize-none"
                :class="{
                  'border-green-500 focus:border-green-600': fieldValidation.problemDescription.isValid,
                  'border-red-500 focus:border-red-600': fieldValidation.problemDescription.message && formData.problemDescription.length > 0,
                }"
                maxlength="500"
                required
              />
              <div v-if="fieldValidation.problemDescription.message && formData.problemDescription.length > 0" class="text-xs text-red-600">
                {{ fieldValidation.problemDescription.message }}
              </div>
              <div v-else-if="formData.problemDescription.length === 0" class="text-xs text-muted-foreground">
                详细的问题描述有助于快速定位和解决问题
              </div>
              <div v-else-if="fieldValidation.problemDescription.isValid" class="text-xs text-green-600">
                问题描述格式正确
              </div>
            </div>

            <!-- 功能类型 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">
                功能类型 <span class="text-destructive">*</span>
              </Label>
              <CascadeDropdown
                v-model="formData.functionType"
                :data="categoryData"
                :loading="categoryLoading"
                placeholder="请选择功能类型"
                search-placeholder="搜索功能"
                class="w-full"
              />
              <div v-if="fieldValidation.functionType.message" class="text-xs text-red-600">
                {{ fieldValidation.functionType.message }}
              </div>
            </div>

            <!-- 问题类型 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">
                问题类型 <span class="text-destructive">*</span>
              </Label>
              <div class="space-y-3">
                <div
                  v-for="option in endTypeOptions"
                  :key="option.value"
                  class="items-top flex gap-x-2"
                >
                  <Checkbox
                    :id="option.value"
                    :checked="formData.endType.includes(option.value)"
                    @update:checked="(checked: boolean) => {
                      if (checked) {
                        formData.endType.push(option.value)
                      }
                      else {
                        const index = formData.endType.indexOf(option.value)
                        if (index > -1) formData.endType.splice(index, 1)
                      }
                    }"
                  />
                  <div class="grid gap-1.5 leading-none">
                    <Label :for="option.value" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      {{ option.label }}
                    </Label>
                    <p class="text-xs text-muted-foreground">
                      {{ option.description }}
                    </p>
                  </div>
                </div>
              </div>
              <div v-if="fieldValidation.endType.message" class="text-xs text-red-600">
                {{ fieldValidation.endType.message }}
              </div>
            </div>

            <!-- 发生时间 -->
            <div class="space-y-2">
              <Label for="startTime" class="text-sm font-medium">
                发生时间 <span class="text-destructive">*</span>
              </Label>
              <DateTimePicker
                v-model="formData.startTime"
                placeholder="请选择问题发生的时间"
                :required="true"
                class="w-full"
              />
              <div v-if="fieldValidation.startTime.message" class="text-xs text-red-600">
                {{ fieldValidation.startTime.message }}
              </div>
            </div>

            <!-- 紧急程度 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">
                紧急程度 <span class="text-destructive">*</span>
              </Label>
              <RadioGroup v-model="formData.severityLevel" class="space-y-3">
                <div
                  v-for="option in severityLevelOptions"
                  :key="option.value"
                  class="items-top flex gap-x-2"
                >
                  <RadioGroupItem
                    :id="option.value"
                    :value="option.value"
                  />
                  <div class="grid gap-1.5 leading-none">
                    <Label :for="option.value" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      {{ option.label }}
                    </Label>
                    <p class="text-xs text-muted-foreground">
                      {{ option.description }}
                    </p>
                  </div>
                </div>
              </RadioGroup>
              <div v-if="fieldValidation.severityLevel.message" class="text-xs text-red-600">
                {{ fieldValidation.severityLevel.message }}
              </div>
            </div>

            <!-- 产品类型 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">
                产品类型 <span class="text-destructive">*</span>
              </Label>
              <div class="space-y-3">
                <div
                  v-for="option in appsOptions"
                  :key="option.value"
                  class="items-top flex gap-x-2"
                >
                  <Checkbox
                    :id="option.value"
                    :checked="formData.apps.includes(option.value)"
                    @update:checked="(checked: boolean) => {
                      if (checked) {
                        formData.apps.push(option.value)
                      }
                      else {
                        const index = formData.apps.indexOf(option.value)
                        if (index > -1) formData.apps.splice(index, 1)
                      }
                    }"
                  />
                  <div class="grid gap-1.5 leading-none">
                    <div class="flex items-center gap-x-2">
                      <Icon
                        v-if="option.icon"
                        :name="option.icon"
                        class="h-4 w-4 text-muted-foreground"
                      />
                      <Label :for="option.value" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        {{ option.label }}
                      </Label>
                    </div>
                    <p class="text-xs text-muted-foreground">
                      {{ option.description }}
                    </p>
                  </div>
                </div>
              </div>
              <div v-if="fieldValidation.apps.message" class="text-xs text-red-600">
                {{ fieldValidation.apps.message }}
              </div>
            </div>

            <!-- 产品版本 -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <Label class="text-sm font-medium">
                  产品版本
                </Label>
                <div class="flex gap-1">
                  <Button
                    v-for="version in commonVersions.slice(0, 3)"
                    :key="version"
                    variant="outline"
                    size="sm"
                    class="h-6 px-2 text-xs hover:border-primary/60 hover:bg-primary/10"
                    type="button"
                    :disabled="formData.appVersion.includes(version)"
                    @click="addCommonVersion(version)"
                  >
                    <Icon name="i-lucide-plus" class="mr-1 h-3 w-3" />
                    {{ version }}
                  </Button>
                </div>
              </div>
              <TagsInput
                :model-value="formData.appVersion"
                class="min-h-[40px]"
                @update:model-value="(value) => formData.appVersion = value as string[]"
              >
                <TagsInputItem
                  v-for="version in formData.appVersion"
                  :key="version"
                  :value="version"
                  class="border-primary/30 bg-primary/10 text-primary"
                >
                  <TagsInputItemText />
                  <TagsInputItemDelete />
                </TagsInputItem>
                <TagsInputInput placeholder="输入版本号，如 1.0.0，按回车添加" class="placeholder:text-muted-foreground/60" />
              </TagsInput>
              <div class="text-xs text-muted-foreground">
                支持语义化版本号格式（如 1.0.0），可添加多个版本
              </div>
            </div>

            <!-- 设备系统 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">
                设备系统 <span class="text-destructive">*</span>
              </Label>
              <div class="space-y-3">
                <div
                  v-for="option in osTypeOptions"
                  :key="option.value"
                  class="items-top flex gap-x-2"
                >
                  <Checkbox
                    :id="`${option.value}_os`"
                    :checked="formData.osType.includes(option.value)"
                    @update:checked="(checked: boolean) => {
                      if (checked) {
                        formData.osType.push(option.value)
                      }
                      else {
                        const index = formData.osType.indexOf(option.value)
                        if (index > -1) formData.osType.splice(index, 1)
                      }
                    }"
                  />
                  <div class="grid gap-1.5 leading-none">
                    <div class="flex items-center gap-x-2">
                      <Icon
                        v-if="option.icon"
                        :name="option.icon"
                        class="h-4 w-4 text-muted-foreground"
                      />
                      <Label :for="`${option.value}_os`" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        {{ option.label }}
                      </Label>
                    </div>
                    <p class="text-xs text-muted-foreground">
                      {{ option.description }}
                    </p>
                  </div>
                </div>
              </div>
              <div v-if="fieldValidation.osType.message" class="text-xs text-red-600">
                {{ fieldValidation.osType.message }}
              </div>
            </div>

            <!-- 设备型号 -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <Label class="text-sm font-medium">
                  设备型号
                </Label>
                <div class="flex gap-1">
                  <Button
                    v-for="device in commonDevices.slice(0, 2)"
                    :key="device"
                    variant="outline"
                    size="sm"
                    class="h-6 px-2 text-xs hover:border-primary/60 hover:bg-primary/10"
                    type="button"
                    :disabled="formData.mobileType.includes(device)"
                    @click="addCommonDevice(device)"
                  >
                    <Icon name="i-lucide-plus" class="mr-1 h-3 w-3" />
                    {{ device.length > 10 ? `${device.substring(0, 10)}...` : device }}
                  </Button>
                </div>
              </div>
              <TagsInput
                :model-value="formData.mobileType"
                class="min-h-[40px]"
                @update:model-value="(value) => formData.mobileType = value as string[]"
              >
                <TagsInputItem
                  v-for="device in formData.mobileType"
                  :key="device"
                  :value="device"
                  class="border-primary/30 bg-primary/10 text-primary"
                >
                  <TagsInputItemText />
                  <TagsInputItemDelete />
                </TagsInputItem>
                <TagsInputInput placeholder="输入设备型号，如 iPhone 15 Pro，按回车添加" class="placeholder:text-muted-foreground/60" />
              </TagsInput>
              <div class="text-xs text-muted-foreground">
                支持各种品牌和型号，可添加多个设备
              </div>
            </div>

            <!-- TT ID -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">
                TT ID
              </Label>
              <TagsInput
                :model-value="formData.userTtid"
                class="min-h-[40px]"
                @update:model-value="(value) => formData.userTtid = value as string[]"
              >
                <TagsInputItem
                  v-for="ttid in formData.userTtid"
                  :key="ttid"
                  :value="ttid"
                  class="border-primary/30 bg-primary/10 text-primary"
                >
                  <TagsInputItemText />
                  <TagsInputItemDelete />
                </TagsInputItem>
                <TagsInputInput placeholder="输入 TT ID，如 123456789，按回车添加" class="placeholder:text-muted-foreground/60" />
              </TagsInput>
              <div class="text-xs text-muted-foreground">
                输入用户的 TT ID，支持添加多个用户
              </div>
            </div>

            <!-- 用户是否反馈 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">
                用户是否反馈
              </Label>
              <RadioGroup v-model="formData.isUploadLogs" class="space-y-3">
                <div
                  v-for="option in isUploadLogsOptions"
                  :key="option.value"
                  class="items-top flex gap-x-2"
                >
                  <RadioGroupItem
                    :id="option.value"
                    :value="option.value"
                  />
                  <div class="grid gap-1.5 leading-none">
                    <Label :for="option.value" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      {{ option.label }}
                    </Label>
                    <p v-if="option.description" class="text-xs text-muted-foreground">
                      {{ option.description }}
                    </p>
                  </div>
                </div>
              </RadioGroup>
              <div class="text-xs text-muted-foreground">
                选择用户是否已在应用内反馈此问题
              </div>
            </div>

            <!-- 截图视频 -->
            <div class="space-y-3">
              <Label class="text-sm font-medium">
                截图视频
              </Label>

              <!-- 附件上传和预览 -->
              <div class="space-y-3">
                <!-- 上传区域 -->
                <AttachmentUploaderSimple
                  :model-value="attachmentItems"
                  :uploading-files="uploadingFiles"
                  :max-files="10"
                  :max-size="100"
                  :accept="['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov']"
                  :multiple="true"
                  @update:model-value="handleAttachmentsUpdate"
                  @update:uploading-files="(files) => uploadingFiles = files"
                  @upload-success="handleUploadSuccess"
                  @upload-error="handleUploadError"
                />

                <!-- 附件预览器（包含上传中的文件） -->
                <div v-if="attachmentItems.length > 0 || uploadingFiles.length > 0" class="space-y-2">
                  <div class="text-sm text-muted-foreground">
                    附件预览
                  </div>
                  <div class="border border-border/30 rounded-lg bg-muted/30 p-3">
                    <AttachmentViewer
                      :attachments="attachmentItems"
                      :uploading-files="uploadingFiles"
                      thumbnail-size="sm"
                      :show-file-name="true"
                      :allow-download="false"
                      :allow-delete="true"
                      :allow-retry="true"
                      @delete="handleAttachmentDelete"
                      @delete-uploading="handleDeleteUploadingFile"
                      @retry-upload="handleRetryUpload"
                    />
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </ScrollArea>

      <!-- 固定底部 -->
      <DialogFooter class="border-t bg-muted/20 px-6 py-4">
        <div class="w-full flex items-center justify-between">
          <!-- 表单状态 -->
          <div class="text-sm">
            <div v-if="isFormValid" class="flex items-center gap-2 text-green-700">
              <Icon name="i-lucide-check-circle" class="h-4 w-4" />
              <span>表单填写完整</span>
            </div>
            <div v-else class="flex items-center gap-2 text-amber-700">
              <Icon name="i-lucide-alert-triangle" class="h-4 w-4" />
              <span>请填写所有必填字段</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-3">
            <Button
              type="button"
              variant="outline"
              @click="handleCancel"
            >
              取消
            </Button>
            <Button
              :disabled="!isFormValid || isSubmitting"
              @click="handleSubmit"
            >
              <Icon
                v-if="isSubmitting"
                name="i-lucide-loader-2"
                class="mr-2 h-4 w-4 animate-spin"
              />
              <Icon
                v-else
                name="i-lucide-plus-circle"
                class="mr-2 h-4 w-4"
              />
              {{ isSubmitting ? '创建中...' : '创建工单' }}
            </Button>
          </div>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
