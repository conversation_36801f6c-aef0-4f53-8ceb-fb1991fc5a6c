<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import { CheckCircle } from 'lucide-vue-next'
import { ref } from 'vue'
import { useModal } from '~/composables/useModal'

interface Props {
  ticket: Ticket
  onComplete?: (data: { cause: string, result: string }) => Promise<void>
}

const props = defineProps<Props>()

const modal = useModal()

// 表单数据
const formData = ref({
  cause: '',
  result: '',
})

// 表单验证
const errors = ref({
  cause: '',
  result: '',
})

// 验证表单
function validateForm() {
  errors.value = { cause: '', result: '' }

  if (!formData.value.cause.trim()) {
    errors.value.cause = '请填写问题原因'
    return false
  }

  if (!formData.value.result.trim()) {
    errors.value.result = '请填写处理结果'
    return false
  }

  return true
}

// 提交表单
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  try {
    // 调用父组件传递的完成回调
    if (props.onComplete) {
      await props.onComplete(formData.value)
    }

    // 触发父组件的确认事件
    const resolve = modal.getModalResolve()
    if (resolve) {
      resolve(true)
      modal.setModalResolve(null)
    }

    // 关闭弹窗
    modal.closeModal()
  }
  catch (error) {
    console.error('提交完成工单表单失败:', error)
  }
}

// 取消操作
function handleCancel() {
  const resolve = modal.getModalResolve()
  if (resolve) {
    resolve(false)
    modal.setModalResolve(null)
  }
  modal.closeModal()
}
</script>

<template>
  <Dialog :open="true" @update:open="handleCancel">
    <DialogContent class="[&>button]:hidden sm:max-w-lg">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <CheckCircle class="h-5 w-5 text-green-600" />
          完成工单
        </DialogTitle>
        <DialogDescription>
          请填写问题原因和处理结果
        </DialogDescription>
      </DialogHeader>

      <div class="py-4 space-y-4">
        <!-- 工单信息 -->
        <div class="rounded-lg bg-muted/50 p-3">
          <div class="mb-1 text-sm text-muted-foreground font-medium">
            工单信息
          </div>
          <div class="text-sm">
            <div class="font-medium">
              {{ ticket.problemDescription || ticket.text || ticket.title }}
            </div>
            <div class="text-muted-foreground">
              工单ID: {{ ticket.ticketID || ticket.id }}
            </div>
          </div>
        </div>

        <!-- 问题原因 -->
        <div class="space-y-2">
          <Label for="cause" class="text-sm font-medium">
            问题原因 <span class="text-red-500">*</span>
          </Label>
          <Textarea
            id="cause"
            v-model="formData.cause"
            placeholder="请详细描述问题的根本原因..."
            rows="3"
            :class="errors.cause ? 'border-red-500' : ''"
          />
          <div v-if="errors.cause" class="text-sm text-red-500">
            {{ errors.cause }}
          </div>
        </div>

        <!-- 处理结果 -->
        <div class="space-y-2">
          <Label for="result" class="text-sm font-medium">
            处理结果 <span class="text-red-500">*</span>
          </Label>
          <Textarea
            id="result"
            v-model="formData.result"
            placeholder="请详细描述问题的处理结果..."
            rows="4"
            :class="errors.result ? 'border-red-500' : ''"
          />
          <div v-if="errors.result" class="text-sm text-red-500">
            {{ errors.result }}
          </div>
        </div>
      </div>

      <DialogFooter class="flex gap-2">
        <Button variant="outline" @click="handleCancel">
          取消
        </Button>
        <Button @click="handleSubmit">
          <CheckCircle class="mr-2 h-4 w-4" />
          完成工单
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
