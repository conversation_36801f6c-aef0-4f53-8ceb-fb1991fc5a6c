<script lang="ts" setup>
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import type { Ticket } from '~/types/ticket'

import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

import {
  Archive,
  Calendar,
  CheckCircle,
  Clock,
  Copy,
  Hand,
  Tag,
  Trash2,
  User,
  UserPlus,
  X,
  Zap,
} from 'lucide-vue-next'
import { computed, ref, watch } from 'vue'
import { useToast } from '~/components/ui/toast'
import { useModal } from '~/composables/useModal'
import { useTicketOperations } from '~/composables/useTicketOperations'
import EditableField from './EditableField.vue'
import TicketWorkflowSteps from './TicketWorkflowSteps.vue'

interface TicketDisplayProps {
  ticket: Ticket | undefined
}

const props = defineProps<TicketDisplayProps>()
const emit = defineEmits(['refresh', 'refreshTicket'])

const ticket = ref<Ticket | undefined>(props.ticket)

// 统一的工单操作管理
const {
  // 权限和状态检查
  checkPermission,
  checkTicketStatus,

  // 工单操作方法
  deleteTicketAction,
  handleTicketAction,
  assignTicketAction,
  completeTicketAction,
  checkTicketAction,
  rejectTicketAction,
  urgeTicketAction,
} = useTicketOperations()

const modal = useModal()
const { toast } = useToast()

// 监听props.ticket的变化，更新本地ticket
watch(
  () => props.ticket,
  (newTicket) => {
    ticket.value = newTicket
  },
  { immediate: true },
)

// 计算附件列表
const attachments = computed<AttachmentItem[]>(() => {
  if (!ticket.value?.attachments)
    return []

  return ticket.value.attachments.map((attachment, index) => ({
    id: `${ticket.value!.id || ticket.value!.ticketID}-${index}`,
    name: attachment.name || `附件${index + 1}`,
    url: attachment.url || attachment.path || '',
    type: attachment.type || 'file',
    size: attachment.size || 0,
  }))
})

// 更新工单字段
function updateTicketField(field: keyof Ticket, value: any) {
  if (!ticket.value)
    return

  // 创建一个新的工单对象，以保持响应性
  ticket.value = {
    ...ticket.value,
    [field]: value,
  }

  // 这里可以添加保存到后端的逻辑
  // 在实际应用中，这里应该调用API保存更改
}

// 工单操作方法
async function handleDeleteTicket() {
  if (!ticket.value)
    return

  const confirmed = await modal.confirm(
    `确定要删除"工单 #${ticket.value.ticketID || ticket.value.id}"吗？此操作不可撤销。`,
    '确认删除',
    {
      type: 'error',
      confirmText: '删除',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    const success = await deleteTicketAction(ticket.value)
    if (success) {
      // 刷新工单列表
      emit('refresh')
      // 返回主页
      window.location.href = '/feedback'
    }
  }
}

async function handleClaimTicket() {
  if (!ticket.value)
    return

  const confirmed = await modal.confirm(
    `确定要认领"工单 #${ticket.value.ticketID || ticket.value.id}"吗？`,
    '确认认领',
    {
      type: 'info',
      confirmText: '认领',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    const success = await handleTicketAction(ticket.value)
    if (success) {
      // 刷新工单列表
      emit('refresh')
      // 刷新当前工单详情
      emit('refreshTicket')
    }
  }
}

async function handleAssignTicket() {
  if (!ticket.value)
    return

  // 使用统一的弹窗系统显示指派工单表单
  await modal.showModal({
    title: '指派工单',
    type: 'custom',
    component: 'AssignTicketForm',
    props: {
      ticket: ticket.value,
      onAssign: async (data: { assignee: string, reason?: string }) => {
        // 调用指派工单API
        const success = await assignTicketAction(
          ticket.value!,
          data.assignee,
          data.reason,
        )

        // 如果操作成功，立即刷新
        if (success) {
          // 刷新工单列表
          emit('refresh')
          // 刷新当前工单详情
          emit('refreshTicket')
        }
      },
    },
    persistent: true,
    closable: true,
  })
}

// 复制工单链接
async function handleCopyTicketLink() {
  if (!ticket.value)
    return

  const ticketId = ticket.value.ticketID || ticket.value.id
  const ticketUrl = `${window.location.origin}/feedback/ticket-detail/${ticketId}`

  try {
    await navigator.clipboard.writeText(ticketUrl)
    toast({
      title: '复制成功',
      description: '工单链接已复制到剪贴板',
    })
  }
  catch (error) {
    console.error('复制失败:', error)
    toast({
      title: '复制失败',
      description: '无法复制到剪贴板，请手动复制',
      variant: 'destructive',
    })
  }
}
</script>

<template>
  <div class="h-full flex flex-col">
    <!-- 工具栏 -->
    <div class="flex shrink-0 items-center justify-between border-b">
      <div class="flex items-center gap-2">
        <!-- 认领工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('handle', ticket) || !checkTicketStatus('handle', ticket)"
                @click="handleClaimTicket"
              >
                <Hand class="size-4" />
                <span class="sr-only">认领</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket
              ? '无工单数据'
              : !checkPermission('handle', ticket)
                ? '无认领权限'
                : !checkTicketStatus('handle', ticket)
                  ? '当前状态不允许认领'
                  : '认领工单'
            }}
          </TooltipContent>
        </Tooltip>

        <!-- 指派工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('assign', ticket) || !checkTicketStatus('assign', ticket)"
                @click="handleAssignTicket"
              >
                <UserPlus class="size-4" />
                <span class="sr-only">指派</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket
              ? '无工单数据'
              : !checkPermission('assign', ticket)
                ? '无指派权限'
                : !checkTicketStatus('assign', ticket)
                  ? '当前状态不允许指派'
                  : '指派工单'
            }}
          </TooltipContent>
        </Tooltip>

        <!-- 删除工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('delete', ticket) || !checkTicketStatus('delete', ticket)"
                @click="handleDeleteTicket"
              >
                <Trash2 class="size-4" />
                <span class="sr-only">删除</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket
              ? '无工单数据'
              : !checkPermission('delete', ticket)
                ? '无删除权限'
                : !checkTicketStatus('delete', ticket)
                  ? '当前状态不允许删除'
                  : '删除工单'
            }}
          </TooltipContent>
        </Tooltip>
      </div>

      <!-- 右侧操作按钮 -->
      <div class="flex items-center gap-2">
        <!-- 复制工单链接 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket"
                @click="handleCopyTicketLink"
              >
                <Copy class="size-4" />
                <span class="sr-only">复制链接</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : '复制工单链接' }}
          </TooltipContent>
        </Tooltip>
      </div>
    </div>

    <div v-if="ticket" class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="flex flex-col p-6">
          <!-- 工单详情卡片 -->
          <div class="rounded-lg">
            <!-- 标题和状态区域 -->
            <div class="border-b border-border/20 px-6 py-5">
              <!-- 标题和状态 -->
              <div class="flex items-center gap-3">
                <!-- 状态标签 -->
                <div
                  class="flex items-center gap-1.5 whitespace-nowrap rounded-md px-2 py-1 text-xs font-medium"
                  :class="[
                    (ticket.stage || ticket.status) === '待处理'
                      ? 'bg-[rgb(215,55,55)]/10 text-[rgb(215,55,55)]'
                      : (ticket.stage || ticket.status) === '处理中'
                        ? 'bg-[rgb(201,134,0)]/10 text-[rgb(201,134,0)]'
                        : (ticket.stage || ticket.status) === '已处理'
                          ? 'bg-[rgb(40,167,69)]/10 text-[rgb(40,167,69)]'
                          : (ticket.stage || ticket.status) === '已归档'
                            ? 'bg-[rgb(100,100,105)]/10 text-[rgb(100,100,105)]'
                            : 'bg-[rgb(0,122,255)]/10 text-[rgb(0,122,255)]',
                  ]"
                >
                  <div
                    class="size-2 shrink-0 rounded-full"
                    :class="[
                      (ticket.stage || ticket.status) === '待处理'
                        ? 'bg-[rgb(215,55,55)]'
                        : (ticket.stage || ticket.status) === '处理中'
                          ? 'bg-[rgb(201,134,0)]'
                          : (ticket.stage || ticket.status) === '已处理'
                            ? 'bg-[rgb(40,167,69)]'
                            : (ticket.stage || ticket.status) === '已归档'
                              ? 'bg-[rgb(100,100,105)]'
                              : 'bg-[rgb(0,122,255)]',
                    ]"
                  />
                  {{ ticket.stage || ticket.status }}
                </div>

                <!-- 问题描述作为标题 -->
                <h2 class="line-clamp-2 text-xl font-medium tracking-tight">
                  {{ ticket.problemDescription || ticket.text || ticket.title }}
                </h2>
              </div>

              <!-- 工单ID和创建时间 -->
              <div
                class="mt-2 flex items-center gap-3 text-xs text-muted-foreground"
              >
                <span>TicketID#{{ ticket.ticketID || ticket.id }}</span>
                <span class="text-muted-foreground/40">•</span>
                <span>
                  创建于:
                  {{ ticket.enterTime || ticket.createdAt || ticket.date }}
                </span>
              </div>

              <!-- 工单标签 -->
              <div
                v-if="ticket.labels || ticket.functionType"
                class="mt-3 flex flex-wrap gap-2"
              >
                <Badge
                  v-for="(label, index) of ticket.labels
                    || ticket.functionType
                    || []"
                  :key="`${label}-${index}`"
                  variant="outline"
                  class="flex items-center gap-1 border-0 rounded-full bg-secondary/5 px-2 py-0.5 text-xs font-normal"
                >
                  <Tag class="size-2.5 text-primary/70" />
                  {{ label }}
                </Badge>
              </div>
            </div>

            <!-- 工单内容区域 -->
            <div class="p-6">
              <!-- 工单流程步骤 -->
              <div class="mb-8">
                <h3 class="mb-4 text-base font-medium">
                  工单流程
                </h3>
                <TicketWorkflowSteps
                  :ticket="ticket"
                  orientation="horizontal"
                  :show-progress="true"
                  :show-timestamps="true"
                  :show-operators="true"
                />
              </div>

              <!-- 工单信息 -->
              <div class="grid grid-cols-2 mb-6 gap-x-6 gap-y-4 text-sm">
                <!-- 基本信息 -->
                <div
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    创建人
                  </div>
                  <div class="flex items-center gap-1.5">
                    <User class="size-3 text-primary/70" />
                    <span>{{ ticket.feedbackPerson || '未知' }}</span>
                  </div>
                </div>
                <div
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    处理人
                  </div>
                  <div class="flex items-center gap-1.5">
                    <User class="size-3 text-primary/70" />
                    <span>{{ ticket.handler || ticket.devProcessor || '未分配' }}</span>
                  </div>
                </div>

                <!-- 截止日期 -->
                <div
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    截止日期
                  </div>
                  <div class="flex items-center gap-1.5">
                    <Calendar class="size-3 text-primary/70" />
                    <span>{{ ticket.dueDate || '未设置' }}</span>
                  </div>
                </div>
                <div
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    严重程度
                  </div>
                  <span>{{ ticket.severityLevel || '未设置' }}</span>
                </div>

                <!-- 时间信息 -->
                <div
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    创建时间
                  </div>
                  <div class="flex items-center gap-1.5">
                    <Clock class="size-3 text-primary/70" />
                    <span>{{
                      ticket.createdAt || ticket.enterTime || ticket.date
                    }}</span>
                  </div>
                </div>
                <div
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    处理时间
                  </div>
                  <div class="flex items-center gap-1.5">
                    <Clock class="size-3 text-primary/70" />
                    <span>{{
                      ticket.endTime || ticket.updatedAt || '未处理'
                    }}</span>
                  </div>
                </div>

                <!-- 设备信息 -->
                <div
                  v-if="ticket.apps && ticket.apps.length > 0"
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    应用
                  </div>
                  <span>{{ ticket.apps.join(', ') }}</span>
                </div>
                <div
                  v-if="ticket.appVersion && ticket.appVersion.length > 0"
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    版本
                  </div>
                  <span>{{ ticket.appVersion.join(', ') }}</span>
                </div>

                <!-- 设备类型 -->
                <div
                  v-if="ticket.osType && ticket.osType.length > 0"
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    系统
                  </div>
                  <span>{{ ticket.osType.join(', ') }}</span>
                </div>
                <div
                  v-if="ticket.mobileType && ticket.mobileType.length > 0"
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    设备
                  </div>
                  <span>{{ ticket.mobileType.join(', ') }}</span>
                </div>

                <!-- 分类 -->
                <div
                  v-if="ticket.firstLevelCategory"
                  class="flex items-center gap-2 border-b border-border/10 pb-2"
                >
                  <div class="w-16 text-xs text-muted-foreground">
                    问题分类
                  </div>
                  <span>
                    {{ ticket.firstLevelCategory }} >
                    {{ ticket.secondLevelCategory || '' }}
                  </span>
                </div>
              </div>

              <!-- 原因和结果 -->
              <div v-if="ticket.cause || ticket.result" class="mb-6 space-y-4">
                <div v-if="ticket.cause" class="space-y-1">
                  <div class="text-xs text-muted-foreground font-medium">
                    问题原因
                  </div>
                  <div
                    class="border border-border/30 rounded-md p-3 text-sm leading-relaxed"
                  >
                    {{ ticket.cause }}
                  </div>
                </div>
                <div v-if="ticket.result" class="space-y-1">
                  <div class="text-xs text-muted-foreground font-medium">
                    处理结果
                  </div>
                  <div
                    class="whitespace-pre-line border border-border/30 rounded-md p-3 text-sm leading-relaxed"
                  >
                    {{ ticket.result }}
                  </div>
                </div>
              </div>

              <!-- 附件 -->
              <div v-if="attachments.length > 0" class="space-y-3">
                <div class="text-xs text-muted-foreground font-medium">
                  附件 ({{ attachments.length }})
                </div>
                <AttachmentViewer :attachments="attachments" />
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>

    <!-- 工单未找到 -->
    <div v-else class="flex-1 flex items-center justify-center">
      <div class="text-center">
        <Icon name="i-lucide-file-x" class="mx-auto size-12 text-muted-foreground" />
        <h2 class="mt-4 text-lg font-semibold">工单数据加载中...</h2>
        <p class="mt-2 text-sm text-muted-foreground">
          请稍候，正在获取工单信息
        </p>
      </div>
    </div>
  </div>
</template>
