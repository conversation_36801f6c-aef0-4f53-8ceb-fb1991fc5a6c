<script lang="ts" setup>
import type { Ticket } from '~/types/ticket'
import { useLayoutState } from '~/composables/useLayoutState'
import { useTicketFilters } from '~/composables/useTicketFilters'
import { useTicketSearch } from '~/composables/useTicketSearch'
import { useTicketSelection } from '~/composables/useTicketSelection'
import {
  generatePersonalLinks,
  generateStatusLinks,
  getMenuIcon,
  getMenuTitle,
} from '~/constants/ticketMenus'
import TicketFilterToolbar from './filter/TicketFilterToolbar.vue'

import Nav from './Nav.vue'
import TicketDetail from './TicketDetail.vue'
import TicketList from './TicketList.vue'

interface TicketProps {
  tickets: Ticket[]
  defaultLayout?: number[]
  defaultCollapsed?: boolean
  navCollapsedSize: number
  activeMenu: string
  selectedTicketId?: string
  loading?: boolean
  error?: string | null
}

const props = withDefaults(defineProps<TicketProps>(), {
  defaultCollapsed: false,
  defaultLayout: () => [10, 25, 65],
  selectedTicketId: '',
  loading: false,
  error: null,
})

// 定义 emit 事件
const emit = defineEmits<{
  (e: 'update:activeMenu', menu: string): void
  (e: 'ticketCreated', ticket: Ticket): void
  (e: 'refreshTickets'): void
  (e: 'ticketActionRefresh'): void
  (e: 'refreshCurrentTicket'): void
}>()

// 使用 computed 来确保 tickets 响应 props.tickets 的变化
const tickets = computed(() => props.tickets)

// 使用各种 composables
const { computeTicketData } = useTicketFilters()
const {
  searchValue,
  filterAndSearchTickets,
  handleFilter,
} = useTicketSearch()
const {
  selectedTicket,
  selectedTicketData,
  autoSelectTicket: _autoSelectTicket,
} = useTicketSelection(tickets, props.selectedTicketId)
const {
  isCollapsed,
  isMobile,
  selectedMenu,
  collapse: onCollapse,
  expand: onExpand,
  setSelectedMenu,
  initLayout,
} = useLayoutState(props.activeMenu, props.defaultCollapsed)

// 应用搜索和筛选的工单列表
const filteredTicketList = computed(() => {
  return filterAndSearchTickets(tickets.value)
})

// 统一的工单列表和计数
const ticketData = computed(() => {
  return computeTicketData(filteredTicketList.value, tickets.value)
})
const currentTicketList = computed(() => {
  const menu = selectedMenu.value
  return ticketData.value.lists[menu] || ticketData.value.lists.all
})

onMounted(() => {
  // 初始化布局
  initLayout()

  // 确保selectedMenu与props.activeMenu同步
  if (props.activeMenu) {
    setSelectedMenu(props.activeMenu)
  }

  // 组件挂载后，如果有指定的工单ID，确保它被正确选中
  if (props.selectedTicketId && tickets.value && tickets.value.length > 0) {
    const ticketExists = tickets.value.some(
      item =>
        item.id === props.selectedTicketId
        || item.id.toString() === props.selectedTicketId
        || props.selectedTicketId === item.id.toString(),
    )
    if (ticketExists) {
      selectedTicket.value = props.selectedTicketId
      nextTick(() => {
        ensureTicketVisible(props.selectedTicketId!)
      })
    }
  }
})

// 监听activeMenu属性变化，更新selectedMenu
watch(
  () => props.activeMenu,
  (newValue) => {
    if (newValue) {
      setSelectedMenu(newValue)
    }
  },
)

// 监听selectedTicketId属性变化，更新selectedTicket
watch(
  () => props.selectedTicketId,
  (newValue) => {
    if (newValue && tickets.value && tickets.value.length > 0) {
      // 检查该ID的工单是否存在（支持字符串和数字类型匹配）
      const ticketExists = tickets.value.some(
        item =>
          item.id === newValue
          || item.id.toString() === newValue
          || newValue === item.id.toString(),
      )
      if (ticketExists) {
        selectedTicket.value = newValue

        // 使用 nextTick 确保所有计算属性都已更新
        nextTick(() => {
          ensureTicketVisible(newValue)
        })
      }
    }
  },
  { immediate: true },
)
// 监听当前列表变化，更新选中的工单
watch(
  currentTicketList,
  (newList) => {
    try {
      // 只有在没有通过 selectedTicketId prop 指定工单的情况下，才自动选择第一个工单
      const hasSpecifiedTicket = props.selectedTicketId

      // 如果列表为空，清除选中的工单
      if (!newList || !Array.isArray(newList) || newList.length === 0) {
        // 清除选中状态
        selectedTicket.value = undefined

        // 清除URL中的工单ID，重置到 /feedback/
        if (import.meta.client && !hasSpecifiedTicket) {
          const currentPath = window.location.pathname
          if (currentPath.includes('/feedback/ticket-detail/')) {
            window.history.replaceState({}, '', '/feedback/')
            window.dispatchEvent(new CustomEvent('urlchange'))
          }
        }
        return
      }

      // 如果有指定的工单ID，优先使用它
      if (hasSpecifiedTicket) {
        const specifiedTicket = newList.find(
          item =>
            item
            && (item.ticketID === props.selectedTicketId
              || item.ticketID?.toString() === props.selectedTicketId
              || item.id === props.selectedTicketId
              || item.id.toString() === props.selectedTicketId),
        )
        if (specifiedTicket) {
          const ticketId = specifiedTicket.ticketID || specifiedTicket.id
          selectedTicket.value = ticketId
          return
        }
      }

      // 如果当前选中的工单不在新列表中，或者没有选中的工单，则自动选择第一个工单
      if (
        !selectedTicket.value
        || !newList.some(
          item =>
            item
            && (item.ticketID === selectedTicket.value
              || item.ticketID?.toString() === selectedTicket.value
              || item.id === selectedTicket.value
              || item.id.toString() === selectedTicket.value),
        )
      ) {
        // 确保newList[0]存在且有id属性
        if (newList[0] && (newList[0].ticketID || newList[0].id)) {
          const firstTicket = newList[0]
          const ticketId = firstTicket.ticketID || firstTicket.id
          selectedTicket.value = ticketId
        }
      }
    }
    catch (error) {
      console.error('Error in currentTicketList watcher:', error)
      // 出错时重置选中状态
      selectedTicket.value = undefined
    }
  },
  { immediate: true },
)

// 确保指定的工单在当前列表中可见
function ensureTicketVisible(ticketId: string) {
  try {
    // 检查工单是否存在
    const ticket = tickets.value.find(
      item =>
        item
        && (item.id === ticketId
          || item.id.toString() === ticketId
          || ticketId === item.id.toString()),
    )

    if (ticket) {
      // 直接访问工单详情页时，统一切换到"所有工单"菜单
      selectedMenu.value = 'all'

      // 通知父组件菜单变化
      emit('update:activeMenu', 'all')
    }
  }
  catch (error) {
    console.error('Error in ensureTicketVisible:', error)
  }
}

// 使用统一的工单计数
const ticketCounts = computed(() => ticketData.value.counts)

// 生成菜单链接
const links = computed(() => generateStatusLinks(ticketCounts.value))
const links2 = computed(() => generatePersonalLinks(ticketCounts.value))

// 当前菜单图标
const currentMenuIcon = computed(() => getMenuIcon(selectedMenu.value))

// 当前菜单标题
const currentMenuTitle = computed(() => getMenuTitle(selectedMenu.value))

// 处理菜单点击
function handleMenuClick(menuKey: string) {
  setSelectedMenu(menuKey)
  emit('update:activeMenu', menuKey)

  // 当切换菜单时，重置路由到 /feedback/
  if (import.meta.client) {
    const currentPath = window.location.pathname
    // 如果当前在工单详情页，切换菜单时重置到 /feedback/
    if (currentPath.includes('/feedback/ticket-detail/')) {
      window.history.replaceState({}, '', '/feedback/')
      // 触发自定义事件，通知面包屑组件 URL 已更新
      window.dispatchEvent(new CustomEvent('urlchange'))
    }
  }

  // 触发工单列表刷新
  emit('refreshTickets')
}

// 处理创建工单点击
async function handleCreateTicket() {
  const { showCreateTicketModal } = useCreateTicketModal()

  try {
    const success = await showCreateTicketModal()
    if (success) {
      // 创建成功，刷新工单列表
      emit('refreshTickets')
    }
  }
  catch (error) {
    console.error('创建工单失败:', error)
  }
}
</script>

<template>
  <div class="h-100vh overflow-hidden">
    <ResizablePanelGroup
      id="resize-panel-group-1"
      direction="horizontal"
      class="h-full items-stretch"
    >
      <ResizablePanel
        id="resize-panel-1"
        :default-size="defaultLayout[0]"
        :collapsed-size="navCollapsedSize"
        collapsible
        :min-size="10"
        :max-size="20"
        :class="
          cn(
            isCollapsed
              && 'min-w-[50px] transition-all duration-300 ease-in-out',
          )
        "
        @expand="onExpand"
        @collapse="onCollapse"
      >
        <div class="h-full flex flex-col">
          <div :class="cn('flex h-[52px] items-center shrink-0 px-2', isCollapsed ? 'justify-center' : 'justify-center')">
            <!-- 收起状态：只显示图标，与菜单项样式一致 -->
            <Tooltip v-if="isCollapsed">
              <TooltipTrigger as-child>
                <Button
                  variant="outline"
                  size="icon"
                  class="h-9 w-9"
                  @click="handleCreateTicket"
                >
                  <Icon name="i-lucide-plus" class="size-4" />
                  <span class="sr-only">创建工单</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right" class="flex items-center gap-4">
                创建工单
              </TooltipContent>
            </Tooltip>

            <!-- 展开状态：显示完整按钮 -->
            <Button
              v-else
              variant="outline"
              class="w-full gap-1"
              @click="handleCreateTicket"
            >
              <Icon name="i-lucide-plus" class="size-4" />
              创建工单
            </Button>
          </div>
          <Separator class="shrink-0" />
          <div class="flex-1 overflow-hidden">
            <ScrollArea class="h-full">
              <Nav
                :is-collapsed="isCollapsed"
                :links="links"
                :active-menu="selectedMenu"
                @menu-click="handleMenuClick"
              />
              <Separator />
              <Nav
                :is-collapsed="isCollapsed"
                :links="links2"
                :active-menu="selectedMenu"
                @menu-click="handleMenuClick"
              />
            </ScrollArea>
          </div>
        </div>
      </ResizablePanel>
      <ResizableHandle id="resize-handle-1" />
      <ResizablePanel
        id="resize-panel-2"
        :default-size="defaultLayout[1]"
        :min-size="20"
      >
        <div class="h-full flex flex-col">
          <!-- 筛选工具栏区域 - 动态高度，但标题栏位置固定 -->
          <div class="shrink-0">
            <TicketFilterToolbar
              :tickets="tickets"
              :title="currentMenuTitle"
              :icon="currentMenuIcon"
              @filter="handleFilter"
              @refresh="() => emit('refreshTickets')"
            />
          </div>
          <div class="flex-1 overflow-hidden border-t">
            <TicketList
              v-model:selected-ticket="selectedTicket"
              v-model:search-value="searchValue"
              :items="currentTicketList"
              :loading="loading"
            />
          </div>
        </div>
      </ResizablePanel>
      <ResizableHandle v-if="!isMobile" id="resize-handle-2" />
      <ResizablePanel
        v-if="!isMobile"
        id="resize-panel-3"
        :default-size="defaultLayout[2]"
        :min-size="40"
      >
        <div class="h-full flex flex-col">
          <div class="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60" />
          <div class="flex-1 overflow-hidden">
            <TicketDetail
              v-if="!isMobile || selectedTicket"
              :ticket="selectedTicketData"
              :loading="loading"
              @refresh="() => emit('ticketActionRefresh')"
              @refresh-ticket="() => emit('refreshCurrentTicket')"
            />
          </div>
        </div>
      </ResizablePanel>
    </ResizablePanelGroup>
  </div>
</template>
