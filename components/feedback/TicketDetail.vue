<script lang="ts" setup>
import type { AttachmentItem } from '@/components/ui/attachment-viewer'

import type { Ticket } from '~/types/ticket'

import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

import {
  Calendar,
  CheckCircle,
  CheckCircle2,
  Clock,
  Copy,
  ExternalLink,
  FileText,
  Hand,
  Home,
  Tag,
  Trash2,
  User,
  UserPlus,
  X,
  Zap,
} from 'lucide-vue-next'
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useToast } from '~/components/ui/toast'
import { useModal } from '~/composables/useModal'
import { useTicketOperations } from '~/composables/useTicketOperations'
import AssignUserModal from './AssignUserModal.vue'
import EditableField from './EditableField.vue'
import TicketWorkflowSteps from './TicketWorkflowSteps.vue'

interface TicketDisplayProps {
  ticket: Ticket | undefined
  loading?: boolean
}

const props = withDefaults(defineProps<TicketDisplayProps>(), {
  loading: false,
})
const emit = defineEmits(['close', 'refresh', 'refreshTicket'])

const ticket = ref<Ticket | undefined>(props.ticket)

// 统一的工单操作管理
const {
  // 权限和状态检查
  checkPermission,
  checkTicketStatus,
  getActionTooltip,

  // 工单操作方法
  deleteTicketAction,
  handleTicketAction,
  assignTicketAction,
  completeTicketAction,
  checkTicketAction,
  rejectTicketAction,
  urgeTicketAction,
} = useTicketOperations()

const modal = useModal()
const { toast } = useToast()
const route = useRoute()

// 检测是否为独立模式
const isStandalone = computed(() => route.query.standalone === 'true')

// 监听props.ticket的变化，更新本地ticket
watch(
  () => props.ticket,
  (newTicket) => {
    // 确保当传入的ticket为undefined时，本地ticket也被清除
    ticket.value = newTicket
  },
  { immediate: true },
)

// 监听工单数据变化，确保空状态正确处理
watch(
  ticket,
  (newTicket) => {
    // 当工单数据为空时，确保所有相关状态都被重置
    if (!newTicket) {
      // 这里可以添加额外的清理逻辑，如清除缓存等
      // 工单数据已清除，将显示空状态
    }
  },
  { immediate: true },
)

// 转换附件数据格式
const attachmentItems = computed<AttachmentItem[]>(() => {
  if (!ticket.value?.attachments)
    return []

  return ticket.value.attachments.map((attachment, index) => ({
    id: attachment.uid || attachment.name || `attachment-${index}`,
    name: attachment.name || `附件${index + 1}`,
    url: attachment.url || '',
    type: attachment.type || '',
    size: attachment.size ? Number.parseInt(attachment.size, 10) : undefined,
    thumbnailUrl: undefined, // 原始数据中没有缩略图，可以后续添加生成逻辑
  }))
})

// 处理附件下载
function handleAttachmentDownload(_attachment: AttachmentItem) {
  // 这里可以添加下载统计或其他逻辑
}

// 更新工单字段
function updateTicketField(field: keyof Ticket, value: any) {
  if (!ticket.value)
    return

  // 创建一个新的工单对象，以保持响应性
  ticket.value = {
    ...ticket.value,
    [field]: value,
  }

  // 这里可以添加保存到后端的逻辑
  // 在实际应用中，这里应该调用API保存更改
}

// 工单操作方法
async function handleDeleteTicket() {
  if (!ticket.value)
    return

  const confirmed = await modal.confirm(
    `确定要删除"工单 #${ticket.value.ticketID || ticket.value.id}"吗？此操作不可撤销。`,
    '确认删除',
    {
      type: 'error',
      confirmText: '删除',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    const success = await deleteTicketAction(ticket.value)
    if (success) {
      // 刷新工单列表
      emit('refresh')
      // 关闭工单详情
      emit('close')
    }
  }
}

async function handleClaimTicket() {
  if (!ticket.value)
    return

  // 使用统一的弹窗系统显示认领确认
  const confirmed = await modal.confirm(
    `确定要认领工单"${ticket.value.problemDescription}"吗？`,
    '确认认领',
    {
      type: 'info',
      confirmText: '认领',
      cancelText: '取消',
    },
  )

  if (!confirmed)
    return

  const success = await handleTicketAction(ticket.value)

  // 如果操作成功，立即刷新
  if (success) {
    // 刷新工单列表
    emit('refresh')
    // 刷新当前工单详情
    emit('refreshTicket')
  }
}

// 指派工单弹窗状态
const showAssignModal = ref(false)

async function handleAssignTicket() {
  if (!ticket.value)
    return

  // 显示指派工单弹窗
  showAssignModal.value = true
}

// 处理指派确认
async function handleAssignConfirm(data: { assignee: any, reason?: string }) {
  if (!ticket.value)
    return

  // 调用指派工单API，使用新的参数格式
  const success = await assignTicketAction(
    ticket.value,
    data.assignee,
    undefined,
  )

  // 如果操作成功，立即刷新
  if (success) {
    // 关闭弹窗
    showAssignModal.value = false
    // 刷新工单列表
    emit('refresh')
    // 刷新当前工单详情
    emit('refreshTicket')
  }
}

async function handleCompleteTicket() {
  if (!ticket.value)
    return

  // 使用统一的弹窗系统显示完成工单表单
  await modal.showModal({
    title: '完成工单',
    type: 'custom',
    component: 'CompleteTicketForm',
    props: {
      ticket: ticket.value,
      onComplete: async (data: { cause: string, result: string }) => {
        // 调用完成工单API，传递问题原因和处理结果
        const success = await completeTicketAction(
          ticket.value!,
          data.result,
          data.cause,
        )

        // 如果操作成功，立即刷新
        if (success) {
          // 刷新工单列表
          emit('refresh')
          // 刷新当前工单详情
          emit('refreshTicket')
        }
      },
    },
    persistent: true,
    closable: true,
  })
}

async function handleArchiveTicket() {
  if (!ticket.value)
    return

  const confirmed = await modal.confirm(
    `确定要归档"工单 #${ticket.value.ticketID || ticket.value.id}"吗？`,
    '确认归档',
    {
      type: 'warning',
      confirmText: '归档',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    const success = await checkTicketAction(ticket.value)

    // 如果操作成功，立即刷新
    if (success) {
      // 刷新工单列表
      emit('refresh')
      // 刷新当前工单详情
      emit('refreshTicket')
    }
  }
}

async function handleRejectTicket() {
  if (!ticket.value)
    return

  const confirmed = await modal.confirm(
    `确定要驳回"工单 #${ticket.value.ticketID || ticket.value.id}"吗？`,
    '确认驳回',
    {
      type: 'warning',
      confirmText: '驳回',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    const success = await rejectTicketAction(ticket.value, '')

    // 如果操作成功，立即刷新
    if (success) {
      // 刷新工单列表
      emit('refresh')
      // 刷新当前工单详情
      emit('refreshTicket')
    }
  }
}

async function handleUrgeTicket() {
  if (!ticket.value)
    return

  const confirmed = await modal.confirm(
    `确定要加急"工单 #${ticket.value.ticketID || ticket.value.id}"吗？`,
    '确认加急',
    {
      type: 'warning',
      confirmText: '加急',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    const success = await urgeTicketAction(ticket.value)

    // 如果操作成功，立即刷新
    if (success) {
      // 刷新工单列表
      emit('refresh')
      // 刷新当前工单详情
      emit('refreshTicket')
    }
  }
}

// 复制工单链接
async function handleCopyTicketLink() {
  if (!ticket.value)
    return

  const ticketId = ticket.value.ticketID || ticket.value.id
  const ticketUrl = `${window.location.origin}/feedback/ticket-detail/${ticketId}`

  try {
    await navigator.clipboard.writeText(ticketUrl)
    toast({
      title: '复制成功',
      description: '工单链接已复制到剪贴板',
    })
  }
  catch (error) {
    console.error('复制失败:', error)
    toast({
      title: '复制失败',
      description: '无法复制到剪贴板，请手动复制',
      variant: 'destructive',
    })
  }
}

// 新窗口打开工单
function handleOpenInNewWindow() {
  if (!ticket.value)
    return

  const ticketId = ticket.value.ticketID || ticket.value.id
  const standaloneUrl = `/feedback/ticket-detail/${ticketId}?standalone=true`
  window.open(standaloneUrl, '_blank')
}

// 访问主页（仅在独立模式下使用）
function goToHomePage() {
  window.location.href = '/feedback'
}
</script>

<template>
  <div class="h-full flex flex-col">
    <div
      class="sticky top-0 z-10 flex shrink-0 items-center justify-between border-b rounded-t-lg bg-background p-2"
      :class="isStandalone ? 'mx-0.1  ' : ' mx-0'"
    >
      <div class="flex items-center gap-2">
        <!-- 认领工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('handle', ticket) || !checkTicketStatus('handle', ticket)"
                @click="handleClaimTicket"
              >
                <Hand class="size-4" />
                <span class="sr-only">认领</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : getActionTooltip('handle', ticket) }}
          </TooltipContent>
        </Tooltip>

        <!-- 指派工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('assign', ticket) || !checkTicketStatus('assign', ticket)"
                @click="handleAssignTicket"
              >
                <UserPlus class="size-4" />
                <span class="sr-only">指派</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : getActionTooltip('assign', ticket) }}
          </TooltipContent>
        </Tooltip>

        <!-- 完成工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('complete', ticket) || !checkTicketStatus('complete', ticket)"
                @click="handleCompleteTicket"
              >
                <CheckCircle class="size-4" />
                <span class="sr-only">完成</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : getActionTooltip('complete', ticket) }}
          </TooltipContent>
        </Tooltip>

        <!-- 驳回工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('reject', ticket) || !checkTicketStatus('reject', ticket)"
                @click="handleRejectTicket"
              >
                <X class="size-4" />
                <span class="sr-only">驳回</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : getActionTooltip('reject', ticket) }}
          </TooltipContent>
        </Tooltip>

        <!-- 归档工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('check', ticket) || !checkTicketStatus('check', ticket)"
                @click="handleArchiveTicket"
              >
                <CheckCircle2 class="size-4" />
                <span class="sr-only">归档</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : getActionTooltip('check', ticket) }}
          </TooltipContent>
        </Tooltip>

        <!-- 加急工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('urge', ticket) || !checkTicketStatus('urge', ticket)"
                @click="handleUrgeTicket"
              >
                <Zap class="size-4" />
                <span class="sr-only">加急</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : getActionTooltip('urge', ticket) }}
          </TooltipContent>
        </Tooltip>

        <!-- 删除工单 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket || !checkPermission('delete', ticket) || !checkTicketStatus('delete', ticket)"
                @click="handleDeleteTicket"
              >
                <Trash2 class="size-4" />
                <span class="sr-only">删除</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : getActionTooltip('delete', ticket) }}
          </TooltipContent>
        </Tooltip>
      </div>

      <!-- 右侧操作按钮 -->
      <div class="flex items-center gap-2">
        <!-- 复制工单链接 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket"
                @click="handleCopyTicketLink"
              >
                <Copy class="size-4" />
                <span class="sr-only">复制链接</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : '复制工单链接' }}
          </TooltipContent>
        </Tooltip>

        <!-- 独立模式：访问主页按钮 -->
        <Tooltip v-if="isStandalone">
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                @click="goToHomePage"
              >
                <Home class="size-4" />
                <span class="sr-only">访问主页</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            访问主页
          </TooltipContent>
        </Tooltip>

        <!-- 正常模式：新窗口打开按钮 -->
        <Tooltip v-else>
          <TooltipTrigger as-child>
            <span class="inline-block">
              <Button
                variant="ghost"
                size="icon"
                :disabled="!ticket"
                @click="handleOpenInNewWindow"
              >
                <ExternalLink class="size-4" />
                <span class="sr-only">新窗口打开</span>
              </Button>
            </span>
          </TooltipTrigger>
          <TooltipContent>
            {{ !ticket ? '无工单数据' : '在新窗口中打开' }}
          </TooltipContent>
        </Tooltip>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="flex flex-col p-2">
          <!-- 工单详情卡片骨架 -->
          <div class="rounded-lg">
            <!-- 标题和状态区域骨架 -->
            <div class="border-b border-border/20 px-6 py-5">
              <div class="flex items-center gap-3">
                <Skeleton class="h-6 w-16" />
                <Skeleton class="h-8 w-96" />
              </div>
              <div class="mt-2 flex items-center gap-3">
                <Skeleton class="h-3 w-24" />
                <Skeleton class="h-3 w-32" />
              </div>
              <div class="mt-3 flex gap-2">
                <Skeleton class="h-5 w-16" />
                <Skeleton class="h-5 w-20" />
                <Skeleton class="h-5 w-18" />
              </div>
            </div>

            <!-- 工单内容区域骨架 -->
            <div class="p-6">
              <!-- 工单流程步骤骨架 -->
              <div class="mb-8">
                <Skeleton class="mb-4 h-6 w-20" />
                <div class="flex gap-4">
                  <Skeleton class="h-8 w-24" />
                  <Skeleton class="h-8 w-24" />
                  <Skeleton class="h-8 w-24" />
                  <Skeleton class="h-8 w-24" />
                </div>
              </div>

              <!-- 工单信息骨架 -->
              <div class="grid grid-cols-2 mb-6 gap-x-6 gap-y-4">
                <div v-for="i in 16" :key="i" class="flex items-center gap-2 border-b border-border/10 pb-2">
                  <Skeleton class="h-3 w-16" />
                  <Skeleton class="h-4 w-32" />
                </div>
              </div>

              <!-- 处理结果骨架 -->
              <div class="space-y-4">
                <Skeleton class="h-4 w-20" />
                <Skeleton class="h-16 w-full" />
                <Skeleton class="h-4 w-20" />
                <Skeleton class="h-16 w-full" />
              </div>

              <!-- 附件骨架 -->
              <div class="mt-6 space-y-3">
                <Skeleton class="h-4 w-12" />
                <div class="flex gap-2">
                  <Skeleton class="h-20 w-20" />
                  <Skeleton class="h-20 w-20" />
                  <Skeleton class="h-20 w-20" />
                </div>
              </div>
            </div>
          </div>

          <!-- 变更日志骨架 -->
          <div class="p-6">
            <Skeleton class="mb-3 h-6 w-20" />
            <div class="space-y-3">
              <div v-for="i in 3" :key="i" class="flex gap-3">
                <Skeleton class="h-8 w-8 rounded-full" />
                <div class="flex-1 space-y-2">
                  <Skeleton class="h-4 w-48" />
                  <Skeleton class="h-3 w-32" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>

    <!-- 工单详情内容 -->
    <div v-else-if="ticket" class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="flex flex-col p-2">
          <!-- 工单详情卡片 -->
          <div class="rounded-lg">
            <!-- 标题和状态区域 -->
            <div class="border-b border-border/20 px-6 py-5">
              <!-- 标题和状态 -->
              <div class="flex items-center gap-3">
                <!-- 状态标签 -->
                <div
                  class="flex items-center gap-1.5 whitespace-nowrap rounded-md px-2 py-1 text-xs font-medium"
                  :class="[
                    (ticket.stage || ticket.status) === '待处理'
                      ? 'bg-[rgb(215,55,55)]/10 text-[rgb(215,55,55)]'
                      : (ticket.stage || ticket.status) === '处理中'
                        ? 'bg-[rgb(201,134,0)]/10 text-[rgb(201,134,0)]'
                        : (ticket.stage || ticket.status) === '已处理'
                          ? 'bg-[rgb(40,167,69)]/10 text-[rgb(40,167,69)]'
                          : (ticket.stage || ticket.status) === '已归档'
                            ? 'bg-[rgb(100,100,105)]/10 text-[rgb(100,100,105)]'
                            : 'bg-[rgb(0,122,255)]/10 text-[rgb(0,122,255)]',
                  ]"
                >
                  <!-- 状态图标 -->
                  <template v-if="(ticket.stage || ticket.status) === '已归档'">
                    <CheckCircle2 class="size-3 text-[rgb(100,100,105)]" />
                  </template>
                  <template v-else>
                    <div
                      class="size-2 shrink-0 rounded-full"
                      :class="[
                        (ticket.stage || ticket.status) === '待处理'
                          ? 'bg-[rgb(215,55,55)]'
                          : (ticket.stage || ticket.status) === '处理中'
                            ? 'bg-[rgb(201,134,0)]'
                            : (ticket.stage || ticket.status) === '已处理'
                              ? 'bg-[rgb(40,167,69)]'
                              : 'bg-[rgb(0,122,255)]',
                      ]"
                    />
                  </template>
                  {{ ticket.stage || ticket.status }}
                </div>

                <!-- 问题描述作为标题 -->
                <h2 class="line-clamp-2 text-xl font-medium tracking-tight">
                  <EditableField
                    v-if="ticket"
                    field-name="problemDescription"
                    field-label="问题描述"
                    :field-value="
                      ticket.problemDescription || ticket.text || ticket.title
                    "
                    field-type="textarea"
                    @update:field-value="
                      updateTicketField('problemDescription', $event)
                    "
                  />
                </h2>
              </div>

              <!-- 工单ID和创建时间 -->
              <div
                class="mt-2 flex items-center gap-3 text-xs text-muted-foreground"
              >
                <span>TicketID#{{ ticket.ticketID || ticket.id }}</span>
                <span class="text-muted-foreground/40">•</span>
                <span>
                  创建于:
                  {{ ticket.enterTime || ticket.createdAt || ticket.date }}
                </span>
              </div>

              <!-- 工单标签 -->
              <div
                v-if="ticket.labels || ticket.functionType"
                class="mt-3 flex flex-wrap gap-2"
              >
                <Badge
                  v-for="(label, index) of ticket.labels
                    || ticket.functionType
                    || []"
                  :key="`${label}-${index}`"
                  variant="outline"
                  class="flex items-center gap-1 border-0 rounded-full bg-secondary/5 px-2 py-0.5 text-xs font-normal"
                >
                  <Tag class="size-2.5 text-primary/70" />
                  {{ label }}
                </Badge>
              </div>
            </div>

            <!-- 工单内容区域 -->
            <div class="p-6">
              <!-- 工单流程步骤 -->
              <!-- <div class="mb-8">
                <h3 class="mb-4 text-base font-medium">
                  工单流程
                </h3>
                <TicketWorkflowSteps
                  :ticket="ticket"
                  orientation="horizontal"
                  :show-progress="true"
                  :show-timestamps="true"
                  :show-operators="true"
                />
              </div> -->
              <div>
                <h3 class="mb-4 text-base font-medium">
                  工单信息
                </h3>

                <!-- 工单信息 -->
                <div class="grid grid-cols-2 mb-6 gap-x-6 gap-y-4 text-sm">
                  <!-- 基本信息 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      创建人
                    </div>
                    <div class="flex items-center gap-1.5">
                      <User class="size-3 text-primary/70" />
                      <EditableField
                        v-if="ticket"
                        field-name="feedbackPerson"
                        field-label="创建人"
                        :field-value="ticket.feedbackPerson || '-'"
                        @update:field-value="
                          updateTicketField('feedbackPerson', $event)
                        "
                      />
                    </div>
                  </div>
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      处理人
                    </div>
                    <div class="flex items-center gap-1.5">
                      <User class="size-3 text-primary/70" />
                      <EditableField
                        v-if="ticket"
                        field-name="handler"
                        field-label="处理人"
                        :field-value="
                          ticket.handler || ticket.devProcessor || '-'
                        "
                        @update:field-value="updateTicketField('handler', $event)"
                      />
                    </div>
                  </div>

                  <!-- 问题类型 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      问题类型
                    </div>
                    <span>{{ ticket.endType && ticket.endType.length > 0 ? ticket.endType.join(', ') : '-' }}</span>
                  </div>

                  <!-- 紧急程度 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      紧急程度
                    </div>
                    <EditableField
                      v-if="ticket"
                      field-name="severityLevel"
                      field-label="紧急程度"
                      :field-value="ticket.severityLevel || '-'"
                      field-type="select"
                      :field-options="[
                        { label: '个例问题', value: '个例问题' },
                        { label: '多例报障', value: '多例报障' },
                      ]"
                      @update:field-value="
                        updateTicketField('severityLevel', $event)
                      "
                    />
                  </div>

                  <!-- 发生时间 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      发生时间
                    </div>
                    <div class="flex items-center gap-1.5">
                      <Clock class="size-3 text-primary/70" />
                      <span>{{ ticket.startTime || '-' }}</span>
                    </div>
                  </div>

                  <!-- 结束时间 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      结束时间
                    </div>
                    <div class="flex items-center gap-1.5">
                      <Clock class="size-3 text-primary/70" />
                      <span>{{ ticket.endTime || ticket.updatedAt || '-' }}</span>
                    </div>
                  </div>

                  <!-- 功能类型 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      功能类型
                    </div>
                    <span>{{ ticket.functionType && ticket.functionType.length > 0 ? ticket.functionType.join(' - ') : '-' }}</span>
                  </div>

                  <!-- 处理群 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      处理群
                    </div>
                    <div class="flex items-center gap-1.5">
                      <ExternalLink class="size-3 text-primary/70" />
                      <template v-if="ticket.feishuGroup">
                        <a
                          v-if="ticket.feishuGroupId"
                          :href="ticket.feishuGroupId"
                          target="_blank"
                          rel="noopener noreferrer"
                          class="text-primary underline hover:text-primary/80"
                        >
                          {{ ticket.feishuGroup }}
                        </a>
                        <span v-else>{{ ticket.feishuGroup }}</span>
                      </template>
                      <span v-else>-</span>
                    </div>
                  </div>

                  <!-- 产品类型 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      产品类型
                    </div>
                    <span>{{ ticket.apps && ticket.apps.length > 0 ? ticket.apps.join(', ') : '-' }}</span>
                  </div>

                  <!-- 产品版本 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      产品版本
                    </div>
                    <span>{{ ticket.appVersion && ticket.appVersion.length > 0 ? ticket.appVersion.join(', ') : '-' }}</span>
                  </div>

                  <!-- 设备系统 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      设备系统
                    </div>
                    <span>{{ ticket.osType && ticket.osType.length > 0 ? ticket.osType.join(', ') : '-' }}</span>
                  </div>

                  <!-- 设备型号 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      设备型号
                    </div>
                    <span>{{ ticket.mobileType && ticket.mobileType.length > 0 ? ticket.mobileType.join(', ') : '-' }}</span>
                  </div>

                  <!-- TT ID -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      TT ID
                    </div>
                    <span>{{ ticket.userTtid && ticket.userTtid.length > 0 ? ticket.userTtid.join(', ') : '-' }}</span>
                  </div>

                  <!-- 用户是否端内反馈 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      端内反馈
                    </div>
                    <span>{{ ticket.hasFeedback ? (ticket.hasFeedback === 'true' || ticket.hasFeedback === '是' ? '是' : '否') : '-' }}</span>
                  </div>

                  <!-- 截止日期 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      截止日期
                    </div>
                    <div class="flex items-center gap-1.5">
                      <Calendar class="size-3 text-primary/70" />
                      <EditableField
                        v-if="ticket"
                        field-name="dueDate"
                        field-label="截止日期"
                        :field-value="ticket.dueDate || '-'"
                        field-type="date"
                        @update:field-value="updateTicketField('dueDate', $event)"
                      />
                    </div>
                  </div>

                  <!-- 创建时间 -->
                  <div
                    class="flex items-center gap-2 border-b border-border/10 pb-2"
                  >
                    <div class="w-16 text-xs text-muted-foreground">
                      创建时间
                    </div>
                    <div class="flex items-center gap-1.5">
                      <Clock class="size-3 text-primary/70" />
                      <span>{{ ticket.createdAt || ticket.enterTime || ticket.date || '-' }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 附件 -->
              <div
                v-if="attachmentItems.length > 0"
                class="mb-8 w-full space-y-3"
              >
                <div class="text-xs text-muted-foreground font-medium">
                  附件
                </div>
                <AttachmentViewer
                  :attachments="attachmentItems"
                  :max-thumbnails="8"
                  thumbnail-size="lg"
                  :show-file-name="true"
                  :allow-download="true"
                  @download="handleAttachmentDownload"
                />
              </div>

              <!-- 原因和结果 -->
              <div class="mb-8 space-y-4">
                <h3 class="mb-4 text-base font-medium">
                  处理结果
                </h3>
                <div class="space-y-1">
                  <div class="text-xs text-muted-foreground font-medium">
                    问题原因
                  </div>
                  <div
                    class="border border-border/30 rounded-md p-3 text-sm leading-relaxed"
                  >
                    <EditableField
                      v-if="ticket"
                      field-name="cause"
                      field-label="问题原因"
                      :field-value="ticket.cause || '-'"
                      field-type="textarea"
                      @update:field-value="updateTicketField('cause', $event)"
                    />
                  </div>
                </div>
                <div class="space-y-1">
                  <div class="text-xs text-muted-foreground font-medium">
                    处理结果
                  </div>
                  <div
                    class="whitespace-pre-line border border-border/30 rounded-md p-3 text-sm leading-relaxed"
                  >
                    <EditableField
                      v-if="ticket"
                      field-name="result"
                      field-label="处理结果"
                      :field-value="ticket.result || '-'"
                      field-type="textarea"
                      @update:field-value="updateTicketField('result', $event)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 变更日志 -->
          <div class="px-6 pb-6">
            <h3 class="mb-3 text-base font-medium">
              处理历史
            </h3>
            <div
              v-if="ticket.changeLogs && ticket.changeLogs.length > 0"
              class="rounded-lg bg-background p-3"
            >
              <div class="relative">
                <!-- 时间线 -->
                <div
                  class="absolute bottom-0 left-0 top-0 w-px bg-primary/20"
                />

                <!-- 日志项 -->
                <div
                  v-for="(log, index) in ticket.changeLogs"
                  :key="`${log.time}-${log.type}-${index}`"
                  class="mb-5 last:mb-0"
                >
                  <div class="relative flex items-start">
                    <div
                      class="absolute top-1.5 h-2.5 w-2.5 rounded-full bg-primary/80 shadow-sm -left-[5px]"
                    />
                    <div class="ml-4">
                      <p class="text-sm leading-relaxed">
                        {{ log.content }}
                      </p>
                      <div
                        class="flex items-center gap-1.5 text-xs text-muted-foreground"
                      >
                        <span>{{ log.time }}</span>
                        <span class="text-muted-foreground/40">•</span>
                        <span>{{ log.operator || log.assignor || '' }}</span>
                        <span v-if="log.assignee">→ {{ log.assignee }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-else
              class="border border-border/30 rounded-lg bg-background p-3 text-center text-sm text-muted-foreground shadow-md"
            >
              暂无处理记录
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
    <!-- 空状态 -->
    <div v-else class="flex flex-1 items-center justify-center">
      <div class="flex flex-col items-center gap-4 text-center">
        <div class="rounded-full bg-muted/20 p-6">
          <FileText class="size-12 text-muted-foreground/50" />
        </div>
        <div class="space-y-2">
          <h3 class="text-lg text-muted-foreground font-medium">
            暂无工单数据
          </h3>
          <p class="max-w-sm text-sm text-muted-foreground/70">
            请从左侧工单列表中选择一个工单查看详情，或创建新的工单。
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- 指派工单弹窗 -->
  <AssignUserModal
    v-model:open="showAssignModal"
    :ticket-title="ticket?.problemDescription"
    @confirm="handleAssignConfirm"
    @cancel="showAssignModal = false"
  />
</template>
