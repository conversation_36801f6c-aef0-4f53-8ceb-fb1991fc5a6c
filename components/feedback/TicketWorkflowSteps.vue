<script setup lang="ts">
import type { WorkflowStep } from '~/composables/useTicketWorkflow'
import type { Ticket } from '~/types/ticket'

import { Badge } from '@/components/ui/badge'
import {
  AlertCircle,
  Archive,
  Check,
  CheckCircle,
  Circle,
  Clock,
  FileText,
  Hand,
  Play,
  User,
} from 'lucide-vue-next'

import { useTicketWorkflow } from '~/composables/useTicketWorkflow'

interface Props {
  ticket: Ticket
  orientation?: 'horizontal' | 'vertical'
  showProgress?: boolean
  showTimestamps?: boolean
  showOperators?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  orientation: 'horizontal',
  showProgress: true,
  showTimestamps: true,
  showOperators: true,
})

const { getWorkflowSteps, getCurrentStepIndex } = useTicketWorkflow()

// 获取工单流程步骤
const workflowSteps = computed(() => getWorkflowSteps(props.ticket))
const currentStepIndex = computed(() => getCurrentStepIndex(props.ticket))

// 获取步骤图标
function getStepIcon(step: WorkflowStep) {
  // 根据步骤ID选择相关图标
  const stepIcons = {
    created: FileText,
    pending: Clock,
    claimed: Hand,
    processing: Play,
    processed: CheckCircle,
    archived: Archive,
  }

  // 如果是错误状态，使用警告图标
  if (step.status === 'error') {
    return AlertCircle
  }

  // 根据步骤ID返回对应图标，默认使用圆圈
  return stepIcons[step.id as keyof typeof stepIcons] || Circle
}

// 获取步骤图标样式
function getStepIconClass(step: WorkflowStep) {
  const baseClasses = 'transition-all duration-200'

  switch (step.status) {
    case 'completed':
      return `${baseClasses} text-green-600 bg-green-50 border-green-200`
    case 'active':
      return `${baseClasses} text-blue-600 bg-blue-50 border-blue-200 ring-2 ring-blue-100`
    case 'error':
      return `${baseClasses} text-red-600 bg-red-50 border-red-200`
    case 'skipped':
      return `${baseClasses} text-gray-400 bg-gray-50 border-gray-200`
    case 'pending':
    default:
      return `${baseClasses} text-gray-400 bg-gray-50 border-gray-200`
  }
}

// 获取步骤连接线样式
function getConnectorClass(index: number) {
  const isCompleted = index < currentStepIndex.value
  const isActive = index === currentStepIndex.value

  if (isCompleted) {
    return 'bg-green-400'
  }
  else if (isActive) {
    return 'bg-blue-300'
  }
  else {
    return 'bg-gray-200'
  }
}

// 格式化时间戳
function formatTimestamp(timestamp?: string) {
  if (!timestamp)
    return ''

  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }
  catch {
    return timestamp
  }
}
</script>

<template>
  <div class="w-full">
    <!-- 水平布局 -->
    <div v-if="orientation === 'horizontal'" class="relative">
      <div class="flex items-center justify-between">
        <div
          v-for="(step, index) in workflowSteps"
          :key="step.id"
          class="relative flex flex-1 flex-col items-center"
          :class="[
            step.status === 'active' ? 'z-10' : 'z-0',
          ]"
        >
          <!-- 步骤指示器 -->
          <div
            class="relative h-8 w-8 flex items-center justify-center border-2 rounded-full transition-all duration-200"
            :class="[
              getStepIconClass(step),
              step.status === 'active' ? 'scale-110 shadow-md' : 'scale-100',
            ]"
          >
            <component
              :is="getStepIcon(step)"
              class="h-4 w-4 transition-all duration-200"
            />
            <!-- 已完成状态的勾选标记 -->
            <div
              v-if="step.status === 'completed'"
              class="absolute h-3 w-3 flex items-center justify-center rounded-full bg-primary -right-1 -top-1"
            >
              <Check class="h-2 w-2 text-primary-foreground" />
            </div>
            <!-- 进行中状态的标记 -->
            <div
              v-else-if="step.status === 'active'"
              class="absolute h-3 w-3 flex animate-pulse items-center justify-center rounded-full bg-primary -right-1 -top-1"
            >
              <div class="h-1.5 w-1.5 rounded-full bg-primary-foreground" />
            </div>
          </div>

          <!-- 步骤标题 -->
          <div
            class="mt-1.5 text-xs font-medium transition-colors duration-200"
            :class="{
              'text-primary': step.status === 'completed' || step.status === 'active',
              'text-destructive': step.status === 'error',
              'text-muted-foreground': step.status === 'pending',
            }"
          >
            {{ step.title }}
          </div>

          <!-- 时间戳和操作人（始终保持固定高度以确保水平对齐） -->
          <div class="mt-1 min-h-[2.5rem] flex flex-col justify-start text-center space-y-0.5">
            <!-- 时间戳行 -->
            <div class="h-4 flex items-center justify-center">
              <div
                v-if="showTimestamps && step.timestamp && (step.status === 'active' || step.status === 'completed')"
                class="flex items-center justify-center text-xs text-muted-foreground"
              >
                <Clock class="mr-1 h-2.5 w-2.5" />
                {{ formatTimestamp(step.timestamp) }}
              </div>
              <div
                v-else-if="showTimestamps"
                class="flex items-center justify-center text-xs text-transparent"
              >
                <!-- 占位符，保持高度一致 -->
                <Clock class="mr-1 h-2.5 w-2.5 opacity-0" />
                <span class="opacity-0">占位</span>
              </div>
            </div>

            <!-- 操作人行 -->
            <div class="h-4 flex items-center justify-center">
              <div
                v-if="showOperators && step.operator && (step.status === 'active' || step.status === 'completed')"
                class="flex items-center justify-center text-xs text-muted-foreground"
              >
                <User class="mr-1 h-2.5 w-2.5" />
                {{ step.operator }}
              </div>
              <div
                v-else-if="showOperators && step.id === 'pending' && step.status === 'active' && !step.operator"
                class="flex items-center justify-center text-xs text-muted-foreground/60"
              >
                <User class="mr-1 h-2.5 w-2.5" />
                待认领
              </div>
              <div
                v-else-if="showOperators"
                class="flex items-center justify-center text-xs text-transparent"
              >
                <!-- 占位符，保持高度一致 -->
                <User class="mr-1 h-2.5 w-2.5 opacity-0" />
                <span class="opacity-0">占位</span>
              </div>
            </div>
          </div>

          <!-- 连接线 -->
          <div
            v-if="index < workflowSteps.length - 1"
            class="absolute left-[calc(50%+16px)] right-[calc(-50%+16px)] top-4 h-0.5 transition-colors duration-200"
            :class="getConnectorClass(index)"
          />
        </div>
      </div>
    </div>

    <!-- 垂直布局 -->
    <div v-else class="space-y-3">
      <div
        v-for="(step, index) in workflowSteps"
        :key="step.id"
        class="relative flex items-start"
        :class="[
          step.status === 'active' ? 'bg-primary/5 rounded-lg p-3 -m-3' : '',
        ]"
      >
        <!-- 步骤指示器 -->
        <div
          class="relative h-8 w-8 flex shrink-0 items-center justify-center border-2 rounded-full transition-all duration-200"
          :class="[
            getStepIconClass(step),
            step.status === 'active' ? 'scale-110 shadow-md' : 'scale-100',
          ]"
        >
          <component
            :is="getStepIcon(step)"
            class="h-4 w-4 transition-all duration-200"
          />
          <!-- 已完成状态的勾选标记 -->
          <div
            v-if="step.status === 'completed'"
            class="absolute h-3 w-3 flex items-center justify-center rounded-full bg-primary -right-1 -top-1"
          >
            <Check class="h-2 w-2 text-primary-foreground" />
          </div>
          <!-- 进行中状态的标记 -->
          <div
            v-else-if="step.status === 'active'"
            class="absolute h-3 w-3 flex animate-pulse items-center justify-center rounded-full bg-primary -right-1 -top-1"
          >
            <div class="h-1.5 w-1.5 rounded-full bg-primary-foreground" />
          </div>
        </div>

        <!-- 连接线 -->
        <div
          v-if="index < workflowSteps.length - 1"
          class="absolute left-4 top-8 h-6 w-0.5 transition-colors duration-200"
          :class="getConnectorClass(index)"
        />

        <!-- 步骤内容 -->
        <div class="ml-3 min-w-0 flex-1">
          <div class="flex items-center justify-between">
            <h3
              class="text-sm font-medium transition-colors duration-200"
              :class="{
                'text-primary': step.status === 'completed' || step.status === 'active',
                'text-destructive': step.status === 'error',
                'text-muted-foreground': step.status === 'pending',
              }"
            >
              {{ step.title }}
            </h3>

            <!-- 状态标识 -->
            <Badge
              v-if="step.status === 'active'"
              variant="default"
              class="border-primary/20 bg-primary/10 text-xs text-primary"
            >
              进行中
            </Badge>
            <Badge
              v-else-if="step.status === 'completed'"
              variant="secondary"
              class="text-xs"
            >
              已完成
            </Badge>
            <Badge
              v-else-if="step.status === 'error'"
              variant="destructive"
              class="text-xs"
            >
              已驳回
            </Badge>
          </div>

          <!-- 时间戳和操作人 -->
          <div v-if="(showTimestamps && step.timestamp) || (showOperators && (step.operator || (step.id === 'pending' && step.status === 'active')))" class="mt-1.5 flex items-center gap-4 text-xs text-muted-foreground">
            <div v-if="showTimestamps && step.timestamp" class="flex items-center">
              <Clock class="mr-1 h-3 w-3" />
              {{ formatTimestamp(step.timestamp) }}
            </div>
            <div v-if="showOperators && step.operator" class="flex items-center">
              <User class="mr-1 h-3 w-3" />
              {{ step.operator }}
            </div>
            <div v-else-if="showOperators && step.id === 'pending' && step.status === 'active' && !step.operator" class="flex items-center text-muted-foreground/60">
              <User class="mr-1 h-3 w-3" />
              待认领
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
