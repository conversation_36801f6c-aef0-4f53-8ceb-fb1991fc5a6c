<script setup lang="ts">
import type { TicketApiTypes } from '~/api/feedback/ticket'
import type { Ticket } from '~/types/ticket'
import { UserSelect } from '@/components/ui/user-select'
import { UserPlus } from 'lucide-vue-next'
import { ref } from 'vue'
import { useModal } from '~/composables/useModal'

interface Props {
  ticket: Ticket
  onAssign?: (data: { assignee: string }) => Promise<void>
}

const props = defineProps<Props>()

const modal = useModal()

// 表单数据
const formData = ref({
  assignee: '',
})

// 表单验证
const errors = ref({
  assignee: '',
})

// 验证表单
function validateForm() {
  errors.value = { assignee: '' }

  if (!formData.value.assignee.trim()) {
    errors.value.assignee = '请选择指派人员'
    return false
  }

  return true
}

// 处理用户选择变化
function handleUserChange(value: string | string[], users: TicketApiTypes.UserInfo | TicketApiTypes.UserInfo[]) {
  formData.value.assignee = Array.isArray(value) ? value[0] : value
  // 清除错误信息
  if (formData.value.assignee) {
    errors.value.assignee = ''
  }
}

// 提交表单
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  try {
    // 调用父组件传递的指派回调
    if (props.onAssign) {
      await props.onAssign({
        assignee: formData.value.assignee,
      })
    }

    // 触发父组件的确认事件
    const resolve = modal.getModalResolve()
    if (resolve) {
      resolve(true)
      modal.setModalResolve(null)
    }

    // 关闭弹窗
    modal.closeModal()
  }
  catch (error) {
    console.error('提交指派工单表单失败:', error)
  }
}

// 取消操作
function handleCancel() {
  const resolve = modal.getModalResolve()
  if (resolve) {
    resolve(false)
    modal.setModalResolve(null)
  }
  modal.closeModal()
}
</script>

<template>
  <Dialog :open="true" @update:open="handleCancel">
    <DialogContent class="[&>button]:hidden sm:max-w-lg">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <UserPlus class="h-5 w-5 text-blue-600" />
          指派工单
        </DialogTitle>
        <DialogDescription>
          请选择要指派的人员
        </DialogDescription>
      </DialogHeader>

      <div class="py-4 space-y-4">
        <!-- 工单信息 -->
        <div class="rounded-lg bg-muted/50 p-3">
          <div class="mb-1 text-sm text-muted-foreground font-medium">
            工单信息
          </div>
          <div class="text-sm">
            <div class="font-medium">
              {{ ticket.problemDescription || ticket.text || ticket.title }}
            </div>
            <div class="text-muted-foreground">
              工单ID: {{ ticket.ticketID || ticket.id }}
            </div>
          </div>
        </div>

        <!-- 指派人员 -->
        <div class="space-y-2">
          <Label for="assignee" class="text-sm font-medium">
            指派给 <span class="text-red-500">*</span>
          </Label>
          <UserSelect
            id="assignee"
            v-model="formData.assignee"
            placeholder="请选择处理人员"
            :multiple="false"
            @change="handleUserChange"
          />
          <div v-if="errors.assignee" class="text-sm text-red-500">
            {{ errors.assignee }}
          </div>
        </div>
      </div>

      <DialogFooter class="flex gap-2">
        <Button variant="outline" @click="handleCancel">
          取消
        </Button>
        <Button @click="handleSubmit">
          <UserPlus class="mr-2 h-4 w-4" />
          确认指派
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
