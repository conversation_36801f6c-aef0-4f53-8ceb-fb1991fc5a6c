<script lang="ts" setup>
import type { Ticket } from '~/types/ticket'

import { Skeleton } from '@/components/ui/skeleton'
import { Search } from 'lucide-vue-next'

import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'

// 移除不再需要的导入，因为已经在 TicketCard 组件中使用
import TicketCard from './TicketCard.vue'

interface TicketListProps {
  items?: Ticket[]
  selectedTicket?: null | string
  searchValue?: string
  loading?: boolean
}

const props = withDefaults(defineProps<TicketListProps>(), {
  items: () => [],
  selectedTicket: null,
  searchValue: '',
  loading: false,
})

// 定义emit
const emit = defineEmits<{
  (e: 'update:selectedTicket', id: string): void
  (e: 'update:searchValue', value: string): void
}>()

// 当前选中的工单ID - 使用计算属性实现双向绑定
const selectedTicket = computed({
  get: () => props.selectedTicket || null,
  set: (value) => {
    if (value) {
      emit('update:selectedTicket', value)
    }
  },
})

// 搜索值的双向绑定
const searchValue = computed({
  get: () => props.searchValue || '',
  set: value => emit('update:searchValue', value),
})

// 路由
const route = useRoute()

// 初始化选中的工单
function initializeSelectedTicket() {
  // 从路由参数中获取工单ID
  const { id } = route.params

  // 如果URL中有工单ID，优先使用它
  if (id && typeof id === 'string') {
    // 检查该ID的工单是否存在，优先使用ticketID匹配
    const ticketExists = props.items.some(
      item =>
        item.ticketID === id
        || item.ticketID?.toString() === id
        || item.id === id
        || item.id.toString() === id,
    )
    if (ticketExists) {
      selectedTicket.value = id
      return
    }
  }

  // 如果URL中没有工单ID或ID无效，且当前没有选中的工单，则选择第一个工单
  if (!selectedTicket.value && props.items.length > 0 && props.items[0]) {
    const firstTicket = props.items[0]
    const ticketId = firstTicket.ticketID || firstTicket.id
    if (ticketId) {
      selectedTicket.value = ticketId

      // 更新浏览器URL为工单详情格式，但不触发页面刷新
      const targetPath = `/feedback/ticket-detail/${ticketId}`
      window.history.replaceState({}, '', targetPath)

      // 触发自定义事件，通知面包屑组件 URL 已更新
      if (import.meta.client) {
        window.dispatchEvent(new CustomEvent('urlchange'))
      }
    }
  }
}

// 监听工单列表变化，处理工单选择逻辑
watch(
  () => props.items,
  (newItems) => {
    // 如果工单列表为空，清除选中状态
    if (!newItems || newItems.length === 0) {
      if (selectedTicket.value) {
        selectedTicket.value = null
        // 清除URL中的工单ID
        if (import.meta.client) {
          const currentPath = window.location.pathname
          if (currentPath.includes('/feedback/ticket-detail/')) {
            window.history.replaceState({}, '', '/feedback/')
            window.dispatchEvent(new CustomEvent('urlchange'))
          }
        }
      }
      return
    }

    // 重新初始化选中的工单
    initializeSelectedTicket()
  },
  { immediate: true },
)

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId && typeof newId === 'string') {
      // 检查该ID的工单是否存在，优先使用ticketID匹配
      const ticketExists = props.items.some(
        item =>
          item.ticketID === newId
          || item.ticketID?.toString() === newId
          || item.id === newId
          || item.id.toString() === newId,
      )
      if (ticketExists) {
        selectedTicket.value = newId
      }
    }
  },
)

// 监听选中工单的变化，当没有选中工单时重置URL
watch(
  () => props.selectedTicket,
  (newSelectedTicket) => {
    if (!newSelectedTicket && import.meta.client) {
      // 当没有选中工单时，重置URL到 /feedback/
      const currentPath = window.location.pathname
      if (currentPath.includes('/feedback/ticket-detail/')) {
        window.history.replaceState({}, '', '/feedback/')
        // 触发自定义事件，通知面包屑组件 URL 已更新
        window.dispatchEvent(new CustomEvent('urlchange'))
      }
    }
  },
)

// 处理工单点击事件
function handleItemClick(id: string) {
  // 只更新选中的工单，不进行路由导航
  selectedTicket.value = id

  // 更新浏览器URL为工单详情格式，但不触发页面刷新
  const targetPath = `/feedback/ticket-detail/${id}`
  window.history.replaceState({}, '', targetPath)

  // 触发自定义事件，通知面包屑组件 URL 已更新
  if (import.meta.client) {
    window.dispatchEvent(new CustomEvent('urlchange'))
  }
}
</script>

<template>
  <div class="h-full flex flex-col">
    <!-- 搜索框 -->
    <div class="shrink-0 p-0">
      <div class="relative w-full">
        <Search class="absolute left-2.5 top-1/2 z-10 size-3.5 text-muted-foreground -translate-y-1/2" />
        <Input
          v-model="searchValue"
          class="h-10 w-full rounded-0 border-none pl-8 pr-4 text-sm focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
          placeholder="搜索工单..."
        />
      </div>
    </div>
    <Separator class="shrink-0" />

    <!-- 工单列表 -->
    <div class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="flex flex-1 flex-col pt-0">
          <!-- 加载状态 -->
          <template v-if="loading">
            <div v-for="i in 5" :key="i" class="border-b p-4">
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <Skeleton class="h-4 w-24" />
                  <Skeleton class="h-3 w-16" />
                </div>
                <div class="flex gap-2">
                  <Skeleton class="h-5 w-12" />
                  <Skeleton class="h-5 w-16" />
                </div>
              </div>
            </div>
          </template>

          <!-- 工单列表 -->
          <template v-else>
            <TicketCard
              v-for="(item, index) of props.items"
              :key="item.ticketID || item.id"
              :ticket="item"
              :is-selected="selectedTicket === (item.ticketID || item.id)"
              :index="index"
              @click="handleItemClick"
            />

            <div
              v-if="props.items.length === 0"
              class="h-full flex flex-col items-center justify-center py-8"
            >
              <div class="text-muted-foreground font-medium">
                暂无数据
              </div>
            </div>
          </template>
        </div>
      </ScrollArea>
    </div>
  </div>
</template>

<style scoped></style>
