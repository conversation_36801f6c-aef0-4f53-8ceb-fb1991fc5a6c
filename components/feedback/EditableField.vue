<script lang="ts" setup>
import { Pencil } from 'lucide-vue-next'

const props = defineProps<{
  disabled?: boolean
  fieldLabel: string
  fieldName: string
  fieldOptions?: { label: string, value: string }[]
  // 支持的字段类型
  // 目前支持：日期、文本输入、下拉选择、文本域
  // 如果将来需要更多类型，可以扩展此处
  fieldType?: 'date' | 'input' | 'select' | 'text' | 'textarea'
  fieldValue: null | string | string[] | undefined
  placeholder?: string
}>()

const emit = defineEmits<{
  (e: 'update:fieldValue', value: null | string | string[] | undefined): void
}>()

// 下拉菜单状态（用于所有编辑）
const isDropdownOpen = ref(false)
// 临时存储编辑值
const editValue = ref<string>('')

// 处理不同类型的编辑值
function setEditValue(value: null | string | string[] | undefined) {
  if (value === null || value === undefined) {
    editValue.value = ''
  }
  else if (Array.isArray(value)) {
    editValue.value = value.join(', ')
  }
  else {
    editValue.value = value
  }
}

// 监听props.fieldValue的变化，更新editValue
watch(
  () => props.fieldValue,
  (newValue) => {
    setEditValue(newValue)
  },
  { immediate: true },
)

// 开始编辑
function startEdit() {
  if (props.disabled)
    return

  // 所有字段都使用下拉菜单编辑
  isDropdownOpen.value = true
  setEditValue(props.fieldValue)
}

// 保存编辑
function saveEdit() {
  // 如果是空字符串，则发送null
  const valueToEmit = editValue.value === '' ? null : editValue.value
  emit('update:fieldValue', valueToEmit)
  isDropdownOpen.value = false
}

// 取消编辑
function cancelEdit() {
  setEditValue(props.fieldValue)
  isDropdownOpen.value = false
}

// 格式化数组值为字符串显示
function formatArrayValue(value: null | string[] | undefined): string {
  if (!value || value.length === 0)
    return '未设置'
  return value.join(', ')
}
</script>

<template>
  <div class="group relative">
    <!-- 非编辑状态 -->
    <div
      class="min-h-[24px] flex cursor-pointer items-center rounded px-1 transition-colors -mx-1 hover:bg-secondary/10"
      @click="startEdit"
    >
      <slot v-if="$slots.default" />
      <template v-else>
        <span v-if="fieldValue">{{
          Array.isArray(fieldValue) ? formatArrayValue(fieldValue) : fieldValue
        }}</span>
        <span v-else class="text-muted-foreground italic">未设置</span>
      </template>

      <!-- 编辑按钮 -->
      <Button
        v-if="!disabled"
        variant="ghost"
        size="icon"
        class="ml-1 size-5 opacity-0 transition-opacity group-hover:opacity-100"
      >
        <Pencil class="size-3 text-muted-foreground" />
      </Button>
    </div>

    <!-- 移除行内编辑，所有编辑都使用下拉菜单 -->

    <!-- 下拉菜单编辑 (用于所有字段) -->
    <DropdownMenu v-model:open="isDropdownOpen">
      <DropdownMenuTrigger as-child>
        <div class="hidden">
          <!-- 这个触发器是隐藏的，我们通过编程方式控制下拉菜单的打开状态 -->
          <span />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent class="w-[300px] p-4" align="start">
        <div class="mb-4 text-sm font-medium">
          编辑{{ fieldLabel }}
        </div>

        <!-- 文本域 -->
        <div v-if="fieldType === 'textarea'" class="mb-4">
          <Textarea
            v-model="editValue"
            :placeholder="placeholder || `请输入${fieldLabel}`"
            class="min-h-[100px] w-full"
          />
        </div>

        <!-- 下拉选择 -->
        <div v-else-if="fieldType === 'select'" class="mb-4">
          <Select v-model="editValue">
            <SelectTrigger class="w-full">
              <SelectValue
                :placeholder="placeholder || `请选择${fieldLabel}`"
              />
              <!-- 覆盖默认的下拉图标 -->
              <template #suffix>
                <span />
              </template>
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in fieldOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 日期选择 -->
        <div v-else-if="fieldType === 'date'" class="mb-4">
          <Input
            v-model="editValue"
            type="date"
            class="w-full"
            :placeholder="placeholder || `请选择${fieldLabel}`"
          />
        </div>

        <!-- 普通文本输入 -->
        <div v-else class="mb-4">
          <Input
            v-model="editValue"
            class="w-full"
            :placeholder="placeholder || `请输入${fieldLabel}`"
          />
        </div>

        <div class="flex justify-end gap-2">
          <Button variant="outline" size="sm" @click="cancelEdit">
            取消
          </Button>
          <Button size="sm" @click="saveEdit">
            保存
          </Button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>
