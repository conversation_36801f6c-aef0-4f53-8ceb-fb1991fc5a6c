<script setup lang="ts">
import type { TicketApiTypes } from '~/api/feedback/ticket'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { UserSelect } from '@/components/ui/user-select'

interface Props {
  /** 是否显示对话框 */
  open: boolean
  /** 工单标题 */
  ticketTitle?: string
  /** 是否为多选模式 */
  multiple?: boolean
}

interface AssigneeParam {
  label: string
  value: string
  key: string
  option: {
    user_name: string
    email: string
    user_id: string
    TT_id?: string
    deptname?: string
  }
  originLabel: string
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'confirm', data: { assignee: AssigneeParam, reason?: string }): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
})

const emit = defineEmits<Emits>()

// 响应式数据
const assignees = ref<string | string[]>(props.multiple ? [] : '')
const selectedUsers = ref<TicketApiTypes.UserInfo[]>([])
const reason = ref('')
const loading = ref(false)

// 计算属性
const isValid = computed(() => {
  if (props.multiple) {
    return Array.isArray(assignees.value) && assignees.value.length > 0
  }
  return !!assignees.value
})

// 方法
async function handleConfirm() {
  if (!isValid.value)
    return

  loading.value = true
  try {
    // 构建指派参数，格式符合API要求
    const assigneeData = selectedUsers.value.length > 0 ? selectedUsers.value[0] : null
    if (!assigneeData) {
      return
    }

    const assigneeParam: AssigneeParam = {
      label: assigneeData.user_name,
      value: assigneeData.email || '',
      key: assigneeData.email || '',
      option: {
        user_name: assigneeData.user_name,
        email: assigneeData.email || '',
        user_id: assigneeData.user_id,
        TT_id: assigneeData.TT_id,
        deptname: assigneeData.deptname,
      },
      originLabel: assigneeData.user_name,
    }

    emit('confirm', {
      assignee: assigneeParam,
      reason: reason.value || undefined,
    })
  }
  finally {
    loading.value = false
  }
}

function handleCancel() {
  emit('cancel')
  emit('update:open', false)
}

function handleOpenChange(open: boolean) {
  emit('update:open', open)
  if (!open) {
    // 重置表单
    assignees.value = props.multiple ? [] : ''
    reason.value = ''
  }
}

function handleUserChange(value: string | string[], users: TicketApiTypes.UserInfo | TicketApiTypes.UserInfo[]) {
  assignees.value = value
  // 保存完整的用户信息
  if (Array.isArray(users)) {
    selectedUsers.value = users
  }
  else {
    selectedUsers.value = [users]
  }
}
</script>

<template>
  <Dialog :open="open" @update:open="handleOpenChange">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>指派工单</DialogTitle>
        <DialogDescription>
          {{ ticketTitle ? `工单：${ticketTitle}` : '请选择要指派的人员' }}
        </DialogDescription>
      </DialogHeader>

      <div class="py-4 space-y-4">
        <div class="space-y-2">
          <Label for="assignees">
            {{ multiple ? '指派给（多选）' : '指派给' }}
            <span class="text-red-500">*</span>
          </Label>
          <UserSelect
            id="assignees"
            v-model="assignees"
            :multiple="multiple"
            :placeholder="multiple ? '请选择多个处理人员' : '请选择处理人员'"
            @change="handleUserChange"
          />
        </div>
      </div>

      <DialogFooter>
        <Button
          variant="outline"
          :disabled="loading"
          @click="handleCancel"
        >
          取消
        </Button>
        <Button
          :disabled="!isValid || loading"
          :loading="loading"
          @click="handleConfirm"
        >
          确认指派
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
