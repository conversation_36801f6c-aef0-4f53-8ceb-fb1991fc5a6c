<script lang="ts" setup>
import type { FilterOption } from '~/composables/useFilterLogic'

import { Circle, Search } from 'lucide-vue-next'

import { getStatusColor } from '~/constants/filterCategories'

// 定义组件属性
const props = withDefaults(
  defineProps<{
    // 筛选类别键
    categoryKey: string
    // 筛选类别标签
    categoryLabel: string
    // 全局搜索关键词（来自一级菜单的搜索框）
    globalSearchKeyword?: string
    // 筛选选项
    options: FilterOption[]
    // 当前选中的值
    selectedValues: string[]
    // 是否显示标题和搜索框
    showHeader?: boolean
  }>(),
  {
    showHeader: true,
    globalSearchKeyword: '',
  },
)

// 定义组件事件
const emit = defineEmits<{
  (e: 'toggle', key: string, value: string, isMultiSelect: boolean): void
  (e: 'search', keyword: string): void
}>()

// 搜索关键字
const searchKeyword = ref('')

// 监听全局搜索关键词变化，当全局搜索清空时，也清空本地搜索
watch(
  () => props.globalSearchKeyword,
  (newValue) => {
    if (!newValue || newValue.trim() === '') {
      searchKeyword.value = ''
    }
  },
)

// 筛选后的选项
const filteredOptions = computed(() => {
  if (!searchKeyword.value)
    return props.options

  return props.options.filter(option =>
    option.label.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})

// 检查选项是否被选中
function isOptionSelected(value: string): boolean {
  return props.selectedValues.includes(value)
}

// 处理搜索关键字变化
function handleSearchChange() {
  emit('search', searchKeyword.value)
}

// 处理选项点击 - 多选模式
function handleCheckboxToggle(value: string) {
  emit('toggle', props.categoryKey, value, true) // true表示多选模式
}

// 高亮匹配的文本 - 返回安全的文本片段数组
function getHighlightedTextParts(text: string, keyword: string) {
  if (!keyword || !keyword.trim()) {
    return [{ text, isMatch: false }]
  }

  const parts = []
  const escapedKeyword = keyword
    .trim()
    .replaceAll(/[.*+?^${}()|[\]\\]/g, String.raw`\$&`)
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  let lastIndex = 0

  const matches = [...text.matchAll(regex)]

  for (const match of matches) {
    if (match.index === undefined)
      continue

    // 添加匹配前的文本
    if (match.index > lastIndex) {
      parts.push({
        text: text.slice(lastIndex, match.index),
        isMatch: false,
      })
    }

    // 添加匹配的文本
    parts.push({
      text: match[1],
      isMatch: true,
    })

    lastIndex = match.index + match[0].length
  }

  // 添加剩余的文本
  if (lastIndex < text.length) {
    parts.push({
      text: text.slice(lastIndex),
      isMatch: false,
    })
  }

  return parts
}

// 获取用于高亮的关键词（优先使用全局搜索关键词）
function getHighlightKeyword(): string {
  return props.globalSearchKeyword || searchKeyword.value
}
</script>

<template>
  <!-- 标题和搜索框 -->
  <div v-if="showHeader" class="border-b border-border/20 p-2">
    <div class="mb-2 px-2 py-1 text-xs font-medium">
      {{ props.categoryLabel }}
    </div>
    <div class="relative">
      <Search class="absolute left-2 top-2 size-3.5 text-muted-foreground" />
      <Input
        v-model="searchKeyword"
        class="h-8 bg-muted/30 pl-8 text-sm"
        placeholder="Filter..."
        @input="handleSearchChange"
      />
    </div>
  </div>

  <div class="max-h-[300px] overflow-y-auto">
    <!-- 选项列表 -->
    <div
      v-for="option in filteredOptions"
      :key="option.value"
      class="flex cursor-pointer items-center justify-between rounded-md px-3 py-2 transition-colors hover:bg-muted/50"
      @click="handleCheckboxToggle(option.value)"
    >
      <div class="w-full flex items-center gap-2">
        <!-- Checkbox组件 - 多选且菜单不消失 -->
        <Checkbox
          :checked="isOptionSelected(option.value)"
          @update:checked="() => handleCheckboxToggle(option.value)"
          @click.stop
        />

        <!-- 状态选项的特殊显示 -->
        <div
          v-if="props.categoryKey === 'status'"
          class="size-3.5 rounded-full"
          :style="`background-color: ${option.color || getStatusColor(option.value)}`"
        />

        <!-- 其他选项的图标 -->
        <Circle
          v-else-if="!option.icon"
          class="size-3.5 text-muted-foreground"
        />
        <component
          :is="option.icon"
          v-else
          class="size-3.5 text-muted-foreground"
        />

        <!-- 选项文字 -->
        <span class="flex-1 text-sm">
          <template
            v-for="(part, index) in getHighlightedTextParts(
              option.label,
              getHighlightKeyword(),
            )"
            :key="index"
          >
            <span v-if="part.isMatch" class="text-primary font-medium">
              {{ part.text }}
            </span>
            <span v-else>{{ part.text }}</span>
          </template>
        </span>

        <!-- 数量显示 -->
        <span v-if="option.count" class="ml-1 text-xs text-muted-foreground">
          {{ option.count }}
        </span>
      </div>
    </div>
  </div>
</template>
