<script lang="ts" setup>
import type { Ticket } from '~/types/ticket'

import { ChevronDown, ChevronUp } from 'lucide-vue-next'
import { useDynamicFilters } from '~/composables/useDynamicFilters'
import { useFilterLogic } from '~/composables/useFilterLogic'
// 导入筛选配置
import {
  getCategoryIcon,
  getCategoryLabel,
  getStatusColor,
} from '~/constants/filterCategories'
import FilterDropdown from './FilterDropdown.vue'
import QuickFilterMenu from './QuickFilterMenu.vue'

interface TicketFilterToolbarProps {
  tickets?: Ticket[]
  title?: string // 新增标题属性
  icon?: string // 新增图标属性
  initialFilters?: Record<string, string[]> // 初始筛选条件
}

const {
  title = '工单列表',
  tickets = [],
  initialFilters = {},
} = defineProps<TicketFilterToolbarProps>()

// 定义emit
const emit = defineEmits<{
  (e: 'filter', filters: Record<string, string[]>): void
  (e: 'refresh'): void
}>()

// 使用动态筛选生成器
const { generateFilterCategories } = useDynamicFilters()

// 生成动态筛选类别
const dynamicFilterCategories = computed(() => {
  return generateFilterCategories(tickets)
})

// 使用共享的筛选逻辑
const {
  selectedFilters: activeFilters,
  toggleFilter,
  clearCategoryFilters,
} = useFilterLogic(initialFilters, (filters) => {
  // 向父组件发送筛选事件
  emit('filter', filters)
})

// 是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return Object.values(activeFilters.value).some(
    arr => Array.isArray(arr) && arr.length > 0,
  )
})

// 处理快速筛选菜单的选择事件
function handleQuickFilterSelect(
  categoryKey: string,
  value: string,
  isMultiSelect = false,
) {
  // 调用共享的toggleFilter函数，传递isMultiSelect参数
  toggleFilter(categoryKey, value, undefined, isMultiSelect)
}

// 处理快速筛选菜单的移除事件
function handleQuickFilterRemove(categoryKey: string, _value: string) {
  // 直接调用clearCategoryFilters函数
  clearCategoryFilters(categoryKey)
}

// 处理筛选事件
function handleFilter(filters: Record<string, string[]>) {
  // 创建一个全新的对象，而不是基于当前的activeFilters
  const newFilters: Record<string, string[]> = {}

  // 更新筛选条件
  for (const [key, values] of Object.entries(filters)) {
    if (Array.isArray(values)) {
      newFilters[key] = [...values]
    }
  }

  // 更新筛选条件
  activeFilters.value = newFilters

  // 触发筛选事件到父组件
  emit('filter', newFilters)
}

// 处理清除所有筛选条件
function handleClearAllFilters() {
  // 清空所有筛选条件
  activeFilters.value = {}
  // 触发筛选事件到父组件
  emit('filter', {})
}

// 刷新加载状态
const isRefreshing = ref(false)

// 处理刷新按钮点击
async function handleRefresh() {
  if (isRefreshing.value)
    return

  isRefreshing.value = true
  try {
    emit('refresh')
    // 模拟刷新延迟，给用户视觉反馈
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  finally {
    isRefreshing.value = false
  }
}

// 计算所有标签
const allTags = computed(() => {
  const tags: Array<{ key: string, values: string[] }> = []
  for (const [key, values] of Object.entries(activeFilters.value)) {
    if (Array.isArray(values) && values.length > 0) {
      tags.push({ key: String(key), values })
    }
  }
  return tags
})

// 展开/收起状态管理
const isExpanded = ref(true)

// 是否需要显示展开/收起功能（只要有筛选条件就显示）
const shouldShowToggle = computed(() => {
  return hasActiveFilters.value
})

// 是否显示筛选标签区域（只有在展开状态下才显示）
const shouldShowTags = computed(() => {
  return hasActiveFilters.value && isExpanded.value
})

// 切换展开/收起状态
function toggleExpanded() {
  isExpanded.value = !isExpanded.value
}

// 计算总的筛选条件数量
const totalFilterCount = computed(() => {
  return Object.values(activeFilters.value).reduce((total, values) => {
    return total + (Array.isArray(values) ? values.length : 0)
  }, 0)
})

// 筛选标签容器引用
const tagsContainerRef = ref<HTMLElement>()

// 动态计算筛选标签区域的高度
const tagsHeight = ref('0px')

// 更新筛选标签区域高度的函数
async function updateTagsHeight() {
  if (shouldShowTags.value) {
    // 需要显示标签时，等待DOM更新后计算高度
    await nextTick()
    if (tagsContainerRef.value) {
      const height = tagsContainerRef.value.scrollHeight
      tagsHeight.value = `${Math.min(height + 16, 200)}px` // 最大200px，+16px为padding
    }
  }
  else {
    // 不显示标签时，立即设置高度为0
    tagsHeight.value = '0px'
  }
}

// 监听筛选标签的变化，动态计算高度
watch([shouldShowTags, allTags], updateTagsHeight, { immediate: true })

// 监听展开/收起状态变化
watch(isExpanded, updateTagsHeight)
</script>

<template>
  <div class="w-full">
    <!-- 标题和筛选按钮 - 固定位置，永不移动 -->
    <div class="h-[52px] flex items-center justify-between bg-background px-3 py-2">
      <!-- 左侧：列表标题 -->
      <div class="flex-shrink-0">
        <h2 class="flex items-center gap-2 text-lg text-foreground font-semibold">
          <Icon v-if="icon" :name="icon" class="size-5" />
          {{ title }}
        </h2>
      </div>

      <!-- 右侧：筛选按钮和操作按钮 -->
      <div class="flex flex-shrink-0 items-center gap-1">
        <!-- 刷新按钮 -->
        <Tooltip>
          <TooltipTrigger as-child>
            <Button
              variant="ghost"
              size="sm"
              :disabled="isRefreshing"
              class="flex items-center gap-1.5 text-muted-foreground hover:text-foreground disabled:opacity-50"
              @click="handleRefresh"
            >
              <Icon
                name="i-lucide-refresh-cw"
                class="size-3.5"
                :class="{ 'animate-spin': isRefreshing }"
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent>{{ isRefreshing ? '刷新中...' : '刷新工单列表' }}</TooltipContent>
        </Tooltip>

        <!-- 收起/展开按钮（仅在有筛选条件时显示） -->
        <Tooltip v-if="shouldShowToggle">
          <TooltipTrigger as-child>
            <Button
              variant="ghost"
              size="sm"
              class="flex items-center gap-1.5 text-muted-foreground hover:text-foreground"
              @click="toggleExpanded"
            >
              <template v-if="isExpanded">
                <ChevronUp class="size-3" />
              </template>
              <template v-else>
                <ChevronDown class="size-3" />
              </template>
            </Button>
          </TooltipTrigger>
          <TooltipContent>{{ isExpanded ? '收起筛选条件' : '展开筛选条件' }}</TooltipContent>
        </Tooltip>
        <!-- 清除所有筛选按钮（仅在有筛选条件时显示） -->
        <Tooltip v-if="hasActiveFilters">
          <TooltipTrigger as-child>
            <Button
              variant="ghost"
              size="sm"
              class="flex items-center gap-1.5 text-muted-foreground hover:text-foreground"
              @click="handleClearAllFilters"
            >
              <Icon name="i-lucide-eraser" class="size-3.5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>清除所有筛选条件</TooltipContent>
        </Tooltip>

        <!-- 筛选按钮 -->
        <FilterDropdown
          :categories="dynamicFilterCategories"
          :initial-filters="activeFilters"
          :show-filter-count="hasActiveFilters && !isExpanded"
          :filter-count="totalFilterCount"
          @filter="handleFilter"
        />
      </div>
    </div>

    <!-- 筛选标签区域 - 动态显示，有平滑过渡 -->
    <div
      class="overflow-hidden transition-all duration-300 ease-out"
      :style="{
        maxHeight: tagsHeight,
        opacity: shouldShowTags ? 1 : 0,
      }"
    >
      <div
        v-show="hasActiveFilters"
        ref="tagsContainerRef"
        class="px-3 py-2"
      >
        <!-- 筛选标签行 -->
        <div class="flex flex-wrap items-center gap-2">
          <!-- 显示的标签 -->
          <template v-for="tag in allTags" :key="tag.key">
            <div
              class="flex items-center whitespace-nowrap border rounded-md bg-background/80 px-2 py-1 text-xs transition-all duration-200 hover:bg-background hover:shadow-sm"
            >
              <!-- 类别标签，不可点击 -->
              <span class="flex items-center gap-1.5 font-medium">
                <component
                  :is="getCategoryIcon(tag.key)"
                  class="size-3.5 text-muted-foreground"
                />
                {{ getCategoryLabel(tag.key) }}
              </span>
              <Separator orientation="vertical" class="mx-2 h-4" />
              <!-- 值标签，可点击 -->
              <QuickFilterMenu
                :category-key="tag.key"
                :category-label="getCategoryLabel(tag.key)"
                :options="
                  dynamicFilterCategories.find((cat) => cat.key === tag.key)?.options
                    || []
                "
                :selected-values="tag.values"
                @select="handleQuickFilterSelect"
                @remove="handleQuickFilterRemove"
              >
                <span
                  class="flex cursor-pointer items-center gap-1 hover:text-primary"
                >
                  <!-- 多个值时显示用分隔符连接的值 -->
                  <template v-if="tag.values.length > 1">
                    <!-- 如果是状态类型，显示带颜色的状态 -->
                    <template v-if="tag.key === 'status'">
                      <span
                        class="flex cursor-pointer items-center gap-1"
                      >
                        <template
                          v-for="value in tag.values"
                          :key="value"
                        >
                          <Badge variant="secondary" class="gap-1 rounded-sm px-1 font-normal">
                            <span
                              class="inline-block size-2 rounded-full"
                              :style="{
                                backgroundColor: getStatusColor(String(value)),
                              }"
                            />
                            <span>{{ value }}</span>
                          </Badge>
                        </template>
                      </span>
                    </template>
                    <!-- 其他类型正常显示 -->
                    <template v-else>
                      <span
                        class="flex cursor-pointer items-center gap-1"
                      >
                        <template
                          v-for="value in tag.values"
                          :key="value"
                        >
                          <Badge variant="secondary" class="rounded-sm px-1 font-normal">
                            <span>{{ value }}</span>
                          </Badge>
                        </template>
                      </span>
                    </template>
                  </template>
                  <!-- 单个值时显示具体值 -->
                  <template v-else>
                    <Badge variant="secondary" class="gap-1 rounded-sm px-1 font-normal">
                      <span
                        v-if="tag.key === 'status'"
                        class="mr-1 inline-block size-2 rounded-full"
                        :style="{
                          backgroundColor: getStatusColor(String(tag.values[0])),
                        }"
                      />
                      <span
                        class="max-w-48 cursor-pointer truncate"
                      >{{ tag.values[0] }}
                      </span>
                    </Badge>
                  </template>
                </span>
              </QuickFilterMenu>
              <Tooltip>
                <TooltipTrigger as-child>
                  <Button
                    variant="ghost"
                    size="icon"
                    class="ml-1 h-4 w-4 rounded-full p-0 transition-colors duration-200 hover:bg-red-100 hover:text-red-600"
                    @click="clearCategoryFilters(tag.key)"
                  >
                    <Icon name="i-lucide-x" class="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>清除此筛选条件</TooltipContent>
              </Tooltip>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
