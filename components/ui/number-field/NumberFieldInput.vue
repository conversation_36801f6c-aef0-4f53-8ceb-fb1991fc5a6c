<script setup lang="ts">
import { cn } from '@/lib/utils'
import { NumberFieldInput } from 'radix-vue'
</script>

<template>
  <NumberFieldInput :class="cn('flex h-9 w-full rounded-md border border-input bg-transparent px-10 py-1 text-sm text-center shadow-sm transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50')" />
</template>
