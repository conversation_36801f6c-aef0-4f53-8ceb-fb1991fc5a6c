<script setup lang="ts">
import type { ToastRootEmits } from 'radix-vue'
import type { ToastProps } from '.'
import { cn } from '@/lib/utils'
import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-vue-next'
import { ToastRoot, useForwardPropsEmits } from 'radix-vue'
import { computed } from 'vue'
import { toastVariants } from '.'

const props = defineProps<ToastProps>()

const emits = defineEmits<ToastRootEmits>()

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props

  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)

// 获取图标配置
const iconConfig = computed(() => {
  switch (props.variant) {
    case 'success':
      return {
        icon: CheckCircle,
        iconClass: 'text-green-600 dark:text-green-400',
        bgClass: 'bg-green-100 dark:bg-green-900',
      }
    case 'destructive':
      return {
        icon: XCircle,
        iconClass: 'text-red-600 dark:text-red-400',
        bgClass: 'bg-red-100 dark:bg-red-900',
      }
    case 'warning':
      return {
        icon: AlertTriangle,
        iconClass: 'text-yellow-600 dark:text-yellow-400',
        bgClass: 'bg-yellow-100 dark:bg-yellow-900',
      }
    case 'info':
      return {
        icon: Info,
        iconClass: 'text-blue-600 dark:text-blue-400',
        bgClass: 'bg-blue-100 dark:bg-blue-900',
      }
    default:
      return {
        icon: Info,
        iconClass: 'text-blue-600 dark:text-blue-400',
        bgClass: 'bg-blue-100 dark:bg-blue-900',
      }
  }
})
</script>

<template>
  <ToastRoot
    v-bind="forwarded"
    :class="cn(toastVariants({ variant }), props.class)"
    @update:open="onOpenChange"
  >
    <div class="w-full flex items-center space-x-3">
      <!-- 图标 -->
      <div v-if="props.showIcon !== false" :class="cn('w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0', iconConfig.bgClass)">
        <component :is="iconConfig.icon" :class="cn('w-4 h-4', iconConfig.iconClass)" />
      </div>

      <!-- 内容 -->
      <div class="min-w-0 flex-1">
        <slot />
      </div>
    </div>
  </ToastRoot>
</template>
