<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div
    data-sidebar="content"
    :class="cn('flex min-h-0 flex-1 flex-col overflow-hidden group-data-[collapsible=icon]:overflow-hidden', props.class)"
  >
    <ScrollArea class="flex-1">
      <div class="flex flex-col gap-2 p-2">
        <slot />
      </div>
    </ScrollArea>
  </div>
</template>
