# UserSelect 人员选择组件

一个基于 shadcn-ui 的通用人员选择下拉框组件，支持单选和多选模式，适用于工单指派、人员分配等场景。

## 组件变体

### UserSelectOld (传统组件)

传统的下拉选择框样式，适用于表单和常规场景。

### UserSelect (默认组件 - Linear风格)

参考Linear设计的现代化用户选择组件，具有以下特点：

- 🎨 现代化的无边框按钮设计
- 👤 用户头像显示和堆叠效果
- 🔍 优化的搜索体验
- 📋 清晰的分组和状态显示
- ⚡ "无指派"和"邀请新用户"快捷选项

## 特性

- ✅ 支持单选和多选模式
- ✅ 实时搜索（支持中文名、拼音、工号）
- ✅ 标签显示和数量限制
- ✅ 清除功能和禁用状态
- ✅ 响应式设计和无障碍支持
- ✅ 自定义样式和配置
- ✅ 防抖优化搜索性能
- ✅ 虚拟列表支持（大数据量性能优化）
- ✅ 使用email作为唯一标识

## API 接口

组件使用 `/user/auth/userlist/` 接口获取用户数据，支持以下参数：

- `keyword`: 搜索关键词（可选）
- `page`: 页码（可选）
- `size`: 每页数量（可选）

## 基本用法

### UserSelectOld (传统组件)

#### 单选模式

```vue
<script setup>
import { UserSelectOld } from '@/components/ui/user-select'

const selectedUser = ref('')

function handleChange(value, user) {
  console.log('选择的用户:', { value, user })
}
</script>

<template>
  <UserSelectOld
    v-model="selectedUser"
    placeholder="请选择人员"
    @change="handleChange"
  />
</template>
```

#### 多选模式

```vue
<script setup>
import { UserSelectOld } from '@/components/ui/user-select'

const selectedUsers = ref([])

function handleChange(value, users) {
  console.log('选择的用户:', { value, users })
}
</script>

<template>
  <UserSelectOld
    v-model="selectedUsers"
    multiple
    placeholder="请选择多个人员"
    :max-tag-count="3"
    @change="handleChange"
  />
</template>
```

### UserSelect (Linear风格 - 默认组件)

#### 单选模式

```vue
<script setup>
import { UserSelect } from '@/components/ui/user-select'

const assignee = ref('')

function handleChange(value, user) {
  console.log('指派给:', { value, user })
}

function handleInviteUser(searchValue) {
  console.log('邀请用户:', searchValue)
  // 实现邀请用户逻辑
}

function handleNoAssignee() {
  console.log('取消指派')
}
</script>

<template>
  <UserSelect
    v-model="assignee"
    placeholder="No assignee"
    :show-no-assignee="true"
    :show-invite-user="true"
    @change="handleChange"
    @invite-user="handleInviteUser"
    @no-assignee="handleNoAssignee"
  />
</template>
```

#### 多选模式

```vue
<script setup>
import { UserSelect } from '@/components/ui/user-select'

const assignees = ref([])

function handleChange(value, users) {
  console.log('指派给多人:', { value, users })
}
</script>

<template>
  <UserSelect
    v-model="assignees"
    multiple
    placeholder="No assignees"
    :show-invite-user="true"
    @change="handleChange"
  />
</template>
```

## Props

| 属性                | 类型                 | 默认值                                 | 说明                                  |
| ------------------- | -------------------- | -------------------------------------- | ------------------------------------- |
| `modelValue`        | `string \| string[]` | -                                      | 当前选中的值（使用email作为唯一标识） |
| `multiple`          | `boolean`            | `false`                                | 是否多选                              |
| `placeholder`       | `string`             | `'请选择人员'`                         | 占位符文本                            |
| `searchPlaceholder` | `string`             | `'搜索人员（支持中文名、拼音、工号）'` | 搜索占位符文本                        |
| `disabled`          | `boolean`            | `false`                                | 是否禁用                              |
| `class`             | `string`             | -                                      | 自定义类名                            |
| `maxTagCount`       | `number`             | `3`                                    | 最大显示标签数（多选时）              |
| `clearable`         | `boolean`            | `true`                                 | 是否显示清除按钮                      |
| `virtualThreshold`  | `number`             | `100`                                  | 启用虚拟列表的阈值                    |

### UserSelect Props (Linear风格)

| 属性                | 类型                   | 默认值               | 说明                                  |
| ------------------- | ---------------------- | -------------------- | ------------------------------------- |
| `modelValue`        | `string \| string[]`   | -                    | 当前选中的值（使用email作为唯一标识） |
| `multiple`          | `boolean`              | `false`              | 是否多选                              |
| `placeholder`       | `string`               | `'No assignee'`      | 占位符文本                            |
| `searchPlaceholder` | `string`               | `'Search people...'` | 搜索占位符文本                        |
| `disabled`          | `boolean`              | `false`              | 是否禁用                              |
| `class`             | `string`               | -                    | 自定义类名                            |
| `showNoAssignee`    | `boolean`              | `true`               | 是否显示"无指派"选项                  |
| `showInviteUser`    | `boolean`              | `true`               | 是否显示"邀请新用户"选项              |
| `size`              | `'sm' \| 'md' \| 'lg'` | `'md'`               | 组件大小                              |
| `virtualThreshold`  | `number`               | `50`                 | 启用虚拟列表的阈值                    |
| `itemHeight`        | `number`               | `56`                 | 虚拟列表项高度（像素）                |
| `listHeight`        | `number`               | `280`                | 虚拟列表容器高度（像素）              |

## Events

| 事件                | 参数                                                       | 说明                      |
| ------------------- | ---------------------------------------------------------- | ------------------------- |
| `update:modelValue` | `value: string \| string[]`                                | 值变化时触发（email数组） |
| `change`            | `value: string \| string[], users: UserInfo \| UserInfo[]` | 选择变化时触发            |

### UserSelect Events (Linear风格)

| 事件                | 参数                                                       | 说明                      |
| ------------------- | ---------------------------------------------------------- | ------------------------- |
| `update:modelValue` | `value: string \| string[]`                                | 值变化时触发（email数组） |
| `change`            | `value: string \| string[], users: UserInfo \| UserInfo[]` | 选择变化时触发            |
| `invite-user`       | `searchValue: string`                                      | 点击邀请新用户时触发      |
| `no-assignee`       | -                                                          | 选择"无指派"时触发        |

## 类型定义

```typescript
interface UserInfo {
  user_id: string
  user_name: string
  user_name_id: string
  email?: string
  deptname?: string
  TT_id?: string
  role?: string
}
```

## 高级用法

### 在对话框中使用

```vue
<script setup>
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { UserSelect } from '@/components/ui/user-select'

const open = ref(false)
const assignees = ref([])

function handleConfirm() {
  console.log('指派给:', assignees.value)
  open.value = false
}
</script>

<template>
  <Dialog v-model:open="open">
    <DialogContent>
      <div class="space-y-4">
        <UserSelect
          v-model="assignees"
          multiple
          placeholder="选择指派人员"
        />
        <Button @click="handleConfirm">
          确认指派
        </Button>
      </div>
    </DialogContent>
  </Dialog>
</template>
```

### 自定义样式

```vue
<template>
  <UserSelect
    v-model="selectedUser"
    class="border-2 border-dashed"
    placeholder="自定义边框样式"
  />
</template>
```

### 禁用状态

```vue
<template>
  <UserSelect
    v-model="selectedUser"
    disabled
    placeholder="禁用状态"
  />
</template>
```

## 测试页面

- 基础功能测试: `/test/user-select`
- 实际应用示例: `/test/user-select-demo`
- 修复验证测试: `/test/user-select-fixed`（验证搜索、唯一ID、虚拟列表修复）

## 注意事项

1. **唯一标识**：组件使用 `email` 作为用户的唯一标识，如果email不存在则使用 `user_id`
2. **搜索功能**：支持搜索用户名、工号、邮箱等信息，使用300ms防抖优化，修复了搜索框自动清除问题
3. **虚拟列表**：当用户数量超过 `virtualThreshold`（默认100）时自动启用虚拟列表，解决大数据量卡顿问题
4. 组件依赖 `@vueuse/core` 的 `useDebounceFn` 进行防抖处理
5. 使用 shadcn-ui 的设计系统，确保样式一致性
6. 支持键盘导航和屏幕阅读器
7. 多选模式下会显示标签，超出限制数量会显示 "+N" 提示

## 最近修复

- ✅ **搜索功能修复**：解决了搜索框输入内容自动清除的问题
- ✅ **唯一ID修复**：改用email作为唯一标识，修复了全部选中的问题
- ✅ **性能优化**：添加虚拟列表支持，解决大数据量时的卡顿现象
- ✅ **移除测试数据**：直接使用API接口数据，不再使用模拟数据
