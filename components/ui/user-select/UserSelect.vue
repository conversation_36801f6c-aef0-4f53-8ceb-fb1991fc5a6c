<script setup lang="ts">
import type { TicketApiTypes } from '~/api/feedback/ticket'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { Check, ChevronDown, Loader2, User, UserPlus } from 'lucide-vue-next'
import { getUserList } from '~/api/feedback/ticket'

interface Props {
  /** 当前选中的值 */
  modelValue?: string | string[]
  /** 是否多选 */
  multiple?: boolean
  /** 占位符文本 */
  placeholder?: string
  /** 搜索占位符文本 */
  searchPlaceholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 自定义类名 */
  class?: string
  /** 是否显示"无指派"选项 */
  showNoAssignee?: boolean
  /** 是否显示"邀请新用户"选项 */
  showInviteUser?: boolean
  /** 组件大小 */
  size?: 'sm' | 'md' | 'lg'
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'change', value: string | string[], users: TicketApiTypes.UserInfo | TicketApiTypes.UserInfo[]): void
  (e: 'invite-user', searchValue: string): void
  (e: 'no-assignee'): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'No assignee',
  searchPlaceholder: 'Search people...',
  multiple: false,
  disabled: false,
  showNoAssignee: true,
  showInviteUser: true,
  size: 'md',
})

const emit = defineEmits<Emits>()

// 响应式数据
const open = ref(false)
const searchValue = ref('')
const users = ref<TicketApiTypes.UserInfo[]>([])
const loading = ref(false)
// 已选择用户的缓存，独立于候选用户列表
const selectedUsersCache = ref<TicketApiTypes.UserInfo[]>([])

// 计算属性
const selectedValues = computed(() => {
  if (props.multiple) {
    return Array.isArray(props.modelValue) ? props.modelValue : []
  }
  return props.modelValue ? [props.modelValue] : []
})

const selectedUsers = computed(() => {
  // 优先从缓存中获取已选择的用户
  const cachedUsers = selectedUsersCache.value.filter(user =>
    selectedValues.value.includes(user.email || user.user_id),
  )

  // 如果缓存中有足够的用户，直接返回
  if (cachedUsers.length === selectedValues.value.length) {
    return cachedUsers
  }

  // 否则从当前用户列表中补充
  const currentUsers = users.value.filter(user =>
    selectedValues.value.includes(user.email || user.user_id),
  )

  // 合并并去重
  const allUsers = [...cachedUsers, ...currentUsers]
  const uniqueUsers = allUsers.filter((user, index, arr) =>
    arr.findIndex(u => (u.email || u.user_id) === (user.email || user.user_id)) === index,
  )

  return uniqueUsers
})

const displayText = computed(() => {
  if (selectedUsers.value.length === 0) {
    return props.placeholder
  }

  if (!props.multiple) {
    return getUserDisplayName(selectedUsers.value[0]) || props.placeholder
  }

  return `${selectedUsers.value.length} assignees`
})

const buttonClass = computed(() => {
  const sizeClasses = {
    sm: 'h-7 px-2 text-xs',
    md: 'h-8 px-2 text-sm',
    lg: 'h-9 px-3 text-sm',
  }

  return cn(
    'border-0 bg-transparent hover:bg-accent/50 justify-start font-normal gap-2',
    sizeClasses[props.size],
    !selectedUsers.value.length && 'text-muted-foreground',
    props.class,
  )
})

const hasNoAssignee = computed(() => {
  return props.showNoAssignee && !props.multiple
})

const hasInviteUser = computed(() => {
  return props.showInviteUser && searchValue.value.trim()
})

// 获取用户显示名称
function getUserDisplayName(user: TicketApiTypes.UserInfo) {
  // 确保user对象存在
  if (!user)
    return 'Unknown User'

  // 优先使用user_name，如果没有则使用email的用户名部分
  if (user.user_name && typeof user.user_name === 'string' && user.user_name.trim()) {
    return user.user_name.trim()
  }

  // 如果user_name_id存在且包含有用信息
  if (user.user_name_id && typeof user.user_name_id === 'string' && user.user_name_id.trim()) {
    // 尝试从user_name_id中提取用户名（通常格式为 "name(id)" 或 "name"）
    const match = user.user_name_id.match(/^([^(]+)/)
    if (match && match[1].trim()) {
      return match[1].trim()
    }
  }

  if (user.email && typeof user.email === 'string') {
    return user.email.split('@')[0]
  }

  if (user.user_id && typeof user.user_id === 'string') {
    return user.user_id
  }

  return 'Unknown User'
}

// 获取用户头像缩写
function getUserInitials(user: TicketApiTypes.UserInfo) {
  const name = getUserDisplayName(user)
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
}

// 获取用户头像URL，确保返回字符串或undefined
function getUserAvatar(user: TicketApiTypes.UserInfo): string | undefined {
  // 由于TicketApiTypes.UserInfo可能没有avatar字段，我们需要安全地访问
  const avatar = (user as any).avatar
  return avatar && typeof avatar === 'string' ? avatar : undefined
}

// 缓存用户搜索值的计算属性
const usersWithSearchValue = computed(() => {
  return users.value.map(user => ({
    ...user,
    searchValue: `${user.user_name || ''} ${user.email || ''} ${user.user_id || ''} ${user.user_name_id || ''}`.trim(),
  }))
})

// 加载所有用户数据（只在弹窗打开时调用一次）
async function fetchAllUsers() {
  try {
    loading.value = true
    const response = await getUserList() // 不传 keyword，获取所有用户

    // 处理API响应数据格式
    let userList: TicketApiTypes.UserInfo[] = []

    if (Array.isArray(response)) {
      userList = response
    }
    else if (response && Array.isArray((response as any).data)) {
      userList = (response as any).data
    }
    else if (response && Array.isArray((response as any).list)) {
      userList = (response as any).list
    }
    else {
      userList = []
    }

    // 验证和清理用户数据
    const validUsers = userList.filter((user) => {
      // 确保用户对象有基本的标识符
      return user && (user.user_id || user.email || user.user_name || user.user_name_id)
    }).map((user) => {
      // 确保必要字段存在
      return {
        ...user,
        user_id: user.user_id || user.email || 'unknown',
        user_name: user.user_name || '',
        email: user.email || '',
        user_name_id: user.user_name_id || '',
      }
    })

    users.value = validUsers
  }
  catch (error) {
    console.error('获取用户列表失败:', error)
    users.value = []
  }
  finally {
    loading.value = false
  }
}

function handleSelect(user: TicketApiTypes.UserInfo) {
  if (props.disabled)
    return

  let newValue: string | string[]
  let selectedUser: TicketApiTypes.UserInfo | TicketApiTypes.UserInfo[]

  const userKey = user.email || user.user_id

  if (props.multiple) {
    const currentValues = Array.isArray(props.modelValue) ? props.modelValue : []
    if (currentValues.includes(userKey)) {
      newValue = currentValues.filter(v => v !== userKey)
      // 从缓存中移除取消选择的用户
      selectedUsersCache.value = selectedUsersCache.value.filter(u => (u.email || u.user_id) !== userKey)
    }
    else {
      newValue = [...currentValues, userKey]
      // 添加新选择的用户到缓存
      if (!selectedUsersCache.value.find(u => (u.email || u.user_id) === userKey)) {
        selectedUsersCache.value.push(user)
      }
    }
    selectedUser = users.value.filter(u => newValue.includes(u.email || u.user_id))
  }
  else {
    newValue = userKey
    selectedUser = user
    // 单选模式：替换缓存中的用户
    selectedUsersCache.value = [user]
    open.value = false
  }

  emit('update:modelValue', newValue)
  emit('change', newValue, selectedUser)
}

function handleNoAssignee() {
  if (props.disabled)
    return

  const newValue = props.multiple ? [] : ''
  const selectedUser = props.multiple ? [] : null

  // 清空用户缓存
  selectedUsersCache.value = []

  emit('update:modelValue', newValue)
  emit('change', newValue, selectedUser as any)
  // eslint-disable-next-line vue/custom-event-name-casing
  emit('no-assignee')
  open.value = false
}

function handleInviteUser() {
  if (props.disabled || !searchValue.value.trim())
    return

  // eslint-disable-next-line vue/custom-event-name-casing
  emit('invite-user', searchValue.value.trim())
  open.value = false
}

// 搜索功能使用 Command 组件的内置过滤

// 同步已选用户缓存
function syncSelectedUsersCache() {
  if (selectedValues.value.length === 0) {
    selectedUsersCache.value = []
    return
  }

  // 从当前用户列表中查找已选择的用户
  const foundUsers = users.value.filter(user =>
    selectedValues.value.includes(user.email || user.user_id),
  )

  // 更新缓存，保留已有的用户信息，添加新找到的用户
  foundUsers.forEach((user) => {
    const userKey = user.email || user.user_id
    if (!selectedUsersCache.value.find(u => (u.email || u.user_id) === userKey)) {
      selectedUsersCache.value.push(user)
    }
  })

  // 移除不再选中的用户
  selectedUsersCache.value = selectedUsersCache.value.filter(user =>
    selectedValues.value.includes(user.email || user.user_id),
  )
}

// 监听弹窗打开状态
watch(open, (isOpen) => {
  if (isOpen) {
    // 弹窗打开时加载所有用户数据
    fetchAllUsers()
  }
  else {
    // 弹窗关闭时清除搜索值，但保留用户数据和已选用户缓存
    searchValue.value = ''
  }
})

// 监听用户数据变化，同步缓存
watch(users, () => {
  syncSelectedUsersCache()
}, { deep: true })

// 监听modelValue变化，同步缓存
watch(() => props.modelValue, () => {
  syncSelectedUsersCache()
}, { immediate: true })
</script>

<template>
  <Popover v-model:open="open">
    <PopoverTrigger as-child>
      <Button
        variant="ghost"
        role="combobox"
        :aria-expanded="open"
        :disabled="props.disabled"
        :class="buttonClass"
      >
        <!-- 单选显示 -->
        <template v-if="!props.multiple">
          <template v-if="selectedUsers.length > 0">
            <Avatar class="h-5 w-5">
              <AvatarImage v-if="getUserAvatar(selectedUsers[0])" :src="getUserAvatar(selectedUsers[0])!" />
              <AvatarFallback class="text-xs">
                {{ getUserInitials(selectedUsers[0]) }}
              </AvatarFallback>
            </Avatar>
            <span class="truncate">{{ getUserDisplayName(selectedUsers[0]) }}</span>
          </template>
          <template v-else>
            <User class="h-4 w-4 opacity-50" />
            <span>{{ displayText }}</span>
          </template>
        </template>

        <!-- 多选显示 -->
        <template v-else>
          <template v-if="selectedUsers.length > 0">
            <div class="flex -space-x-1">
              <Avatar
                v-for="(user, index) in selectedUsers.slice(0, 3)"
                :key="user.email || user.user_id"
                class="h-5 w-5 border-2 border-background"
                :class="{ 'z-10': index === 0, 'z-0': index > 0 }"
              >
                <AvatarImage v-if="getUserAvatar(user)" :src="getUserAvatar(user)!" />
                <AvatarFallback class="text-xs">
                  {{ getUserInitials(user) }}
                </AvatarFallback>
              </Avatar>
              <div
                v-if="selectedUsers.length > 3"
                class="h-5 w-5 flex items-center justify-center rounded-full bg-muted text-xs font-medium"
              >
                +{{ selectedUsers.length - 3 }}
              </div>
            </div>
            <span>{{ displayText }}</span>
          </template>
          <template v-else>
            <User class="h-4 w-4 opacity-50" />
            <span>{{ displayText }}</span>
          </template>
        </template>

        <ChevronDown class="h-3 w-3 opacity-50" />
      </Button>
    </PopoverTrigger>

    <PopoverContent class="w-80 p-0" align="start">
      <Command>
        <CommandInput
          v-model="searchValue"
          :placeholder="props.searchPlaceholder"
        />

        <CommandList class="max-h-[300px]">
          <!-- Loading状态 -->
          <div v-if="loading" class="flex items-center justify-center py-6">
            <Loader2 class="mr-2 h-4 w-4 animate-spin" />
            <span class="text-sm text-muted-foreground">Loading users...</span>
          </div>

          <!-- 内容区域 -->
          <template v-else>
            <CommandEmpty>
              No people found.
            </CommandEmpty>

            <!-- 无指派选项 -->
            <CommandGroup v-if="hasNoAssignee">
              <CommandItem
                value="no-assignee"
                class="cursor-pointer"
                @select="handleNoAssignee"
              >
                <Check
                  :class="cn('mr-2 h-4 w-4', selectedUsers.length === 0 ? 'opacity-100' : 'opacity-0')"
                />
                <User class="mr-2 h-4 w-4 opacity-50" />
                <span>No assignee</span>
              </CommandItem>
            </CommandGroup>

            <!-- 团队成员 - ScrollArea模式 -->
            <CommandGroup v-if="usersWithSearchValue.length > 0" heading="Team members">
              <ScrollArea class="h-[200px]">
                <CommandItem
                  v-for="user in usersWithSearchValue"
                  :key="user.email || user.user_id"
                  :value="user.searchValue"
                  class="cursor-pointer"
                  @select="handleSelect(user)"
                >
                  <Check
                    :class="cn('mr-2 h-4 w-4', selectedValues.includes(user.email || user.user_id) ? 'opacity-100' : 'opacity-0')"
                  />
                  <Avatar class="mr-2 h-6 w-6">
                    <AvatarImage v-if="getUserAvatar(user)" :src="getUserAvatar(user)!" />
                    <AvatarFallback class="text-xs">
                      {{ getUserInitials(user) }}
                    </AvatarFallback>
                  </Avatar>
                  <div class="flex flex-1 flex-col">
                    <span class="font-medium">{{ getUserDisplayName(user) }}</span>
                    <span class="text-xs text-muted-foreground">{{ user.email || user.user_id }}</span>
                  </div>
                </CommandItem>
              </ScrollArea>
            </CommandGroup>
            <!-- 邀请新用户 -->
            <CommandGroup v-if="hasInviteUser">
              <Separator />
              <CommandItem
                value="invite-user"
                class="cursor-pointer"
                @select="handleInviteUser"
              >
                <UserPlus class="mr-2 h-4 w-4" />
                <span>Invite and assign...</span>
              </CommandItem>
            </CommandGroup>
          </template>
        </CommandList>
      </Command>
    </PopoverContent>
  </Popover>
</template>
