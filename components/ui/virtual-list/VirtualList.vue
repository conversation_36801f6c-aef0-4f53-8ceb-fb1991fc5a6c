<script setup lang="ts" generic="T">
import { cn } from '@/lib/utils'

interface Props {
  /** 列表数据 */
  items: T[]
  /** 每项高度 */
  itemHeight: number
  /** 容器高度 */
  height: number
  /** 预渲染数量（缓冲区） */
  buffer?: number
  /** 自定义类名 */
  class?: string
}

interface Emits {
  (e: 'scroll', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
  buffer: 5,
})

const emit = defineEmits<Emits>()

// 响应式状态
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)

// 计算属性
const visibleCount = computed(() => Math.ceil(props.height / props.itemHeight))

const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight)
  return Math.max(0, index - props.buffer)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value + props.buffer * 2
  return Math.min(props.items.length, index)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value).map((item, index) => ({
    item,
    index: startIndex.value + index,
    top: (startIndex.value + index) * props.itemHeight,
  }))
})

const totalHeight = computed(() => props.items.length * props.itemHeight)

const offsetY = computed(() => startIndex.value * props.itemHeight)

// 方法
function handleScroll(event: Event) {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  emit('scroll', event)
}

// 滚动到指定索引
function scrollToIndex(index: number) {
  if (!containerRef.value)
    return

  const targetScrollTop = index * props.itemHeight
  containerRef.value.scrollTop = targetScrollTop
}

// 滚动到顶部
function scrollToTop() {
  scrollToIndex(0)
}

// 滚动到底部
function scrollToBottom() {
  scrollToIndex(props.items.length - 1)
}

// 暴露方法
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
})
</script>

<template>
  <div
    ref="containerRef"
    :class="cn('relative overflow-auto', props.class)"
    :style="{ height: `${height}px` }"
    @scroll="handleScroll"
  >
    <!-- 总高度占位 -->
    <div :style="{ height: `${totalHeight}px`, position: 'relative' }">
      <!-- 可见项容器 -->
      <div :style="{ transform: `translateY(${offsetY}px)` }">
        <div
          v-for="{ item, index, top } in visibleItems"
          :key="index"
          :style="{ height: `${itemHeight}px` }"
          class="absolute w-full"
        >
          <slot :item="item" :index="index" />
        </div>
      </div>
    </div>
  </div>
</template>
