<script setup lang="ts">
import type { TooltipRootProps } from 'radix-vue'
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { Tooltip, TooltipContent, TooltipTrigger } from '.'

interface EnhancedTooltipProps extends TooltipRootProps {
  content: string
  side?: 'top' | 'right' | 'bottom' | 'left'
  sideOffset?: number
  disabled?: boolean
  maxWidth?: string
  autoFix?: boolean
}

const props = withDefaults(defineProps<EnhancedTooltipProps>(), {
  side: 'top',
  sideOffset: 4,
  disabled: false,
  maxWidth: '300px',
  autoFix: true,
  delayDuration: 300,
})

const isVisible = ref(false)
const tooltipRef = ref<HTMLElement>()
let fixTimer: NodeJS.Timeout | null = null

// 修复tooltip状态的函数
const fixTooltipState = async () => {
  if (!props.autoFix) return
  
  try {
    // 强制关闭当前tooltip
    isVisible.value = false
    await nextTick()
    
    // 清理可能残留的tooltip内容
    const orphanedTooltips = document.querySelectorAll('[data-radix-tooltip-content]')
    orphanedTooltips.forEach(tooltip => {
      const parent = tooltip.parentElement
      if (parent && parent.tagName === 'BODY') {
        parent.removeChild(tooltip)
      }
    })
  } catch (error) {
    console.warn('Error fixing tooltip state:', error)
  }
}

// 防抖的修复函数
const debouncedFix = () => {
  if (fixTimer) {
    clearTimeout(fixTimer)
  }
  fixTimer = setTimeout(fixTooltipState, 100)
}

// 处理tooltip打开
const handleOpenChange = (open: boolean) => {
  isVisible.value = open
  if (!open) {
    debouncedFix()
  }
}

// 处理鼠标离开
const handleMouseLeave = () => {
  setTimeout(() => {
    if (isVisible.value) {
      debouncedFix()
    }
  }, 100)
}

onMounted(() => {
  // 监听页面可见性变化
  const handleVisibilityChange = () => {
    if (document.hidden && isVisible.value) {
      debouncedFix()
    }
  }
  
  document.addEventListener('visibilitychange', handleVisibilityChange)
  
  // 清理函数
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    if (fixTimer) {
      clearTimeout(fixTimer)
    }
  })
})
</script>

<template>
  <Tooltip 
    v-bind="$attrs"
    :open="isVisible"
    :delay-duration="delayDuration"
    @update:open="handleOpenChange"
  >
    <TooltipTrigger 
      as-child 
      :disabled="disabled"
      @mouseleave="handleMouseLeave"
    >
      <slot />
    </TooltipTrigger>
    <TooltipContent 
      v-if="!disabled && content"
      :side="side"
      :side-offset="sideOffset"
      :style="{ maxWidth }"
      class="z-[9999] break-words"
    >
      {{ content }}
    </TooltipContent>
  </Tooltip>
</template>
