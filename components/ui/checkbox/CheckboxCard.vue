<script setup lang="ts">
import type { CheckboxRootEmits, CheckboxRootProps } from 'radix-vue'
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { CheckboxIndicator, CheckboxRoot, useForwardPropsEmits } from 'radix-vue'
import { computed } from 'vue'

interface CheckboxCardProps extends CheckboxRootProps {
  class?: HTMLAttributes['class']
  label?: string
  description?: string
  icon?: string
}

const props = defineProps<CheckboxCardProps>()
const emits = defineEmits<CheckboxRootEmits>()

const delegatedProps = computed(() => {
  const { class: _, label: __, description: ___, icon: ____, ...delegated } = props
  return delegated
})

const forwarded = useForwardPropsEmits(delegatedProps, emits)
</script>

<template>
  <div class="relative">
    <!-- 隐藏的复选框 -->
    <CheckboxRoot
      v-bind="forwarded"
      :class="cn('absolute inset-0 opacity-0 cursor-pointer', props.class)"
    >
      <CheckboxIndicator />
    </CheckboxRoot>

    <!-- 可见的卡片界面 -->
    <Label
      :for="props.id"
      class="min-w-0 flex cursor-pointer items-center gap-3 border border-border/60 rounded-lg p-3 transition-all hover:border-primary/60 hover:bg-primary/5"
      :class="{
        'border-primary bg-primary/10': props.checked,
      }"
    >
      <!-- 自定义复选框指示器 -->
      <div class="flex-shrink-0">
        <div
          class="h-4 w-4 flex items-center justify-center border-2 rounded-sm transition-colors"
          :class="{
            'border-primary bg-primary': props.checked,
            'border-muted-foreground/30': !props.checked,
          }"
        >
          <Icon
            v-if="props.checked"
            name="radix-icons:check"
            class="h-3 w-3 text-primary-foreground"
          />
        </div>
      </div>

      <!-- 图标（可选） -->
      <Icon
        v-if="props.icon"
        :name="props.icon"
        class="h-4 w-4 flex-shrink-0 text-primary"
      />

      <!-- 内容区域 -->
      <div class="min-w-0 flex-1">
        <div class="text-sm font-medium">
          {{ props.label }}
        </div>
        <div v-if="props.description" class="mt-1 text-xs text-muted-foreground">
          {{ props.description }}
        </div>
      </div>
    </Label>
  </div>
</template>
