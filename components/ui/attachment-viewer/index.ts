export { default as AttachmentPreviewModal } from './AttachmentPreviewModal.vue'
export { default as AttachmentThumbnail } from './AttachmentThumbnail.vue'
export { default as AttachmentViewer } from './AttachmentViewer.vue'

export interface AttachmentItem {
  id: string
  name: string
  url: string
  type: string
  size?: number
  thumbnailUrl?: string
}

export interface AttachmentViewerProps {
  attachments: AttachmentItem[]
  maxThumbnails?: number
  thumbnailSize?: 'sm' | 'md' | 'lg'
  showFileName?: boolean
  allowDownload?: boolean
}
