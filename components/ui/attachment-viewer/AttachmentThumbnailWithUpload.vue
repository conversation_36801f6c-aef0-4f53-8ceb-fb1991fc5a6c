<script setup lang="ts">
import type { AttachmentItem } from './index'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { AlertCircle, CheckCircle, Eye, FileImage, FileVideo, Play, RotateCcw, X } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { formatMediaUrl } from '~/utils/mediaUrl'

interface Props {
  attachment?: AttachmentItem
  file?: File
  uploadProgress?: number
  uploadStatus?: 'uploading' | 'success' | 'error'
  uploadError?: string
  size?: 'sm' | 'md' | 'lg'
  showFileName?: boolean
  allowDelete?: boolean
  allowRetry?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showFileName: true,
  allowDelete: false,
  allowRetry: false,
  uploadProgress: 0,
})

const emit = defineEmits<{
  preview: [attachment: AttachmentItem]
  delete: [attachment?: AttachmentItem, file?: File]
  retry: [file: File]
}>()

// 判断是否为上传中的文件
const isUploading = computed(() => {
  return props.file && !props.attachment
})

// 判断文件类型
const isImage = computed(() => {
  const imageTypes = ['png', 'jpeg', 'jpg']
  if (props.attachment) {
    return imageTypes.includes(props.attachment.type.toLowerCase())
  }
  if (props.file) {
    const extension = props.file.name.split('.').pop()?.toLowerCase()
    return imageTypes.includes(extension || '')
  }
  return false
})

const isVideo = computed(() => {
  const videoTypes = ['mp4', 'rmvb', 'mov']
  if (props.attachment) {
    return videoTypes.includes(props.attachment.type.toLowerCase())
  }
  if (props.file) {
    const extension = props.file.name.split('.').pop()?.toLowerCase()
    return videoTypes.includes(extension || '')
  }
  return false
})

// 文件扩展名
const fileExtension = computed(() => {
  if (props.attachment) {
    return props.attachment.type.toUpperCase()
  }
  if (props.file) {
    return props.file.name.split('.').pop()?.toUpperCase() || ''
  }
  return ''
})

// 文件名
const fileName = computed(() => {
  return props.attachment?.name || props.file?.name || ''
})

// 文件大小
const fileSize = computed(() => {
  if (props.attachment?.size) {
    return formatFileSize(props.attachment.size)
  }
  if (props.file?.size) {
    return formatFileSize(props.file.size)
  }
  return ''
})

// 尺寸样式
const sizeClasses = computed(() => {
  const sizes = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
  }
  return sizes[props.size]
})

// 格式化文件大小
function formatFileSize(bytes: number) {
  const sizes = ['B', 'KB', 'MB', 'GB']
  if (bytes === 0)
    return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${Math.round(bytes / 1024 ** i * 100) / 100} ${sizes[i]}`
}

// 视频缩略图相关
const videoThumbnail = ref<string>()
const videoLoading = ref(false)
const videoError = ref(false)

// 格式化视频URL
const formattedVideoUrl = computed(() => {
  if (!props.attachment?.url) return ''
  return formatMediaUrl(props.attachment.url)
})

// 生成视频缩略图
function generateVideoThumbnail() {
  if (!props.attachment?.url) return
  
  videoLoading.value = true
  videoError.value = false

  const video = document.createElement('video')
  video.crossOrigin = 'anonymous'
  video.currentTime = 1

  video.addEventListener('seeked', () => {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        videoError.value = true
        videoLoading.value = false
        return
      }

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
      videoThumbnail.value = canvas.toDataURL('image/jpeg', 0.8)
      videoLoading.value = false
    }
    catch (error) {
      console.warn('生成视频缩略图失败:', error)
      videoError.value = true
      videoLoading.value = false
    }
  })

  video.addEventListener('error', () => {
    videoError.value = true
    videoLoading.value = false
  })

  video.src = formattedVideoUrl.value
}

// 组件挂载时生成视频缩略图
onMounted(() => {
  if (isVideo.value && props.attachment) {
    generateVideoThumbnail()
  }
})

// 创建文件预览URL（用于上传中的文件）
const filePreviewUrl = computed(() => {
  if (!props.file) return ''
  
  if (isImage.value) {
    return URL.createObjectURL(props.file)
  }
  return ''
})

function handlePreview() {
  if (props.attachment) {
    emit('preview', props.attachment)
  }
}

function handleDelete() {
  emit('delete', props.attachment, props.file)
}

function handleRetry() {
  if (props.file) {
    emit('retry', props.file)
  }
}
</script>

<template>
  <div class="group relative">
    <!-- 缩略图容器 -->
    <div
      :class="cn(
        'relative overflow-hidden rounded-lg border-muted-foreground/25 bg-muted/50 transition-all duration-200',
        !isUploading && 'cursor-pointer hover:border-primary/50 hover:bg-muted/80 group-hover:shadow-md',
        isUploading && 'border-primary/30 bg-primary/5',
        sizeClasses,
      )"
      @click="!isUploading && handlePreview()"
    >
      <!-- 已上传的图片缩略图 -->
      <img
        v-if="isImage && attachment"
        :src="attachment.thumbnailUrl || attachment.url"
        :alt="attachment.name"
        class="h-full w-full object-cover"
        loading="lazy"
      >

      <!-- 上传中的图片预览 -->
      <img
        v-else-if="isImage && file && filePreviewUrl"
        :src="filePreviewUrl"
        :alt="file.name"
        class="h-full w-full object-cover opacity-70"
        loading="lazy"
      >

      <!-- 已上传的视频缩略图 -->
      <div
        v-else-if="isVideo && attachment"
        class="relative h-full w-full"
      >
        <!-- 视频画面缩略图 -->
        <img
          v-if="videoThumbnail && !videoError"
          :src="videoThumbnail"
          :alt="attachment.name"
          class="h-full w-full object-cover"
        >

        <!-- 视频加载状态 -->
        <div
          v-else-if="videoLoading"
          class="h-full w-full flex flex-col items-center justify-center from-blue-50 to-blue-100 bg-gradient-to-br dark:from-blue-950 dark:to-blue-900"
        >
          <div class="h-6 w-6 animate-spin border-b-2 border-blue-600 rounded-full dark:border-blue-400" />
          <span class="mt-2 text-xs text-blue-600 dark:text-blue-400">加载中</span>
        </div>

        <!-- 视频错误状态或默认状态 -->
        <div
          v-else
          class="h-full w-full flex flex-col items-center justify-center from-blue-50 to-blue-100 bg-gradient-to-br dark:from-blue-950 dark:to-blue-900"
        >
          <FileVideo class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          <Badge variant="secondary" class="mt-1 text-xs">
            {{ fileExtension }}
          </Badge>
        </div>

        <!-- 播放图标覆盖层 -->
        <div
          v-if="videoThumbnail && !videoError"
          class="absolute inset-0 flex items-center justify-center bg-black/20"
        >
          <div class="rounded-full bg-black/50 p-2">
            <Play class="h-4 w-4 fill-white text-white" />
          </div>
        </div>
      </div>

      <!-- 上传中的视频或其他文件类型 -->
      <div
        v-else
        class="h-full w-full flex flex-col items-center justify-center from-gray-50 to-gray-100 bg-gradient-to-br dark:from-gray-950 dark:to-gray-900"
      >
        <FileVideo v-if="isVideo" class="h-8 w-8 text-gray-600 dark:text-gray-400" />
        <FileImage v-else class="h-8 w-8 text-gray-600 dark:text-gray-400" />
        <Badge variant="secondary" class="mt-1 text-xs">
          {{ fileExtension }}
        </Badge>
      </div>

      <!-- 上传进度覆盖层 -->
      <div
        v-if="isUploading && uploadStatus === 'uploading'"
        class="absolute inset-0 bg-black/50 flex flex-col items-center justify-center"
      >
        <div class="w-3/4 space-y-2">
          <Progress :value="uploadProgress" class="h-1 bg-white/20" />
          <div class="text-xs text-white text-center">
            {{ Math.round(uploadProgress) }}%
          </div>
        </div>
      </div>

      <!-- 上传成功覆盖层 -->
      <div
        v-if="uploadStatus === 'success'"
        class="absolute inset-0 bg-green-500/20 flex items-center justify-center"
      >
        <CheckCircle class="h-6 w-6 text-green-600" />
      </div>

      <!-- 上传失败覆盖层 -->
      <div
        v-if="uploadStatus === 'error'"
        class="absolute inset-0 bg-red-500/20 flex items-center justify-center"
      >
        <AlertCircle class="h-6 w-6 text-red-600" />
      </div>

      <!-- 悬浮操作按钮 -->
      <div
        v-if="!isUploading"
        class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 group-hover:opacity-100"
      >
        <div class="flex gap-2">
          <Button
            size="sm"
            variant="secondary"
            class="h-8 w-8 p-0"
            @click.stop="handlePreview"
          >
            <Eye class="h-4 w-4" />
          </Button>
          <Button
            v-if="allowDelete"
            size="sm"
            variant="destructive"
            class="h-8 w-8 p-0"
            @click.stop="handleDelete"
          >
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>

      <!-- 上传中/失败状态的操作按钮 -->
      <div
        v-if="isUploading"
        class="absolute top-1 right-1 flex gap-1"
      >
        <Button
          v-if="uploadStatus === 'error' && allowRetry"
          size="sm"
          variant="secondary"
          class="h-6 w-6 p-0"
          @click.stop="handleRetry"
        >
          <RotateCcw class="h-3 w-3" />
        </Button>
        <Button
          size="sm"
          variant="destructive"
          class="h-6 w-6 p-0"
          @click.stop="handleDelete"
        >
          <X class="h-3 w-3" />
        </Button>
      </div>
    </div>

    <!-- 文件信息 -->
    <div v-if="showFileName" class="mt-2 space-y-1">
      <p class="truncate text-xs text-foreground font-medium">
        {{ fileName }}
      </p>
      <div class="flex items-center justify-between text-xs text-muted-foreground">
        <span v-if="fileSize">{{ fileSize }}</span>
        <Badge v-if="uploadStatus === 'error'" variant="destructive" class="text-xs">
          失败
        </Badge>
        <Badge v-else-if="uploadStatus === 'uploading'" variant="secondary" class="text-xs">
          上传中
        </Badge>
      </div>
      <p v-if="uploadStatus === 'error' && uploadError" class="text-xs text-destructive">
        {{ uploadError }}
      </p>
    </div>
  </div>
</template>
