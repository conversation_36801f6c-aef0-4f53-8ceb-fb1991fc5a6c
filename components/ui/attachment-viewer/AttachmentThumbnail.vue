<script setup lang="ts">
import type { AttachmentItem } from './index'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Eye, FileImage, FileVideo, Play, X } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import { formatMediaUrl } from '~/utils/mediaUrl'

interface Props {
  attachment: AttachmentItem
  size?: 'sm' | 'md' | 'lg'
  showFileName?: boolean
  allowDelete?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showFileName: true,
  allowDelete: false,
})

const emit = defineEmits<{
  preview: [attachment: AttachmentItem]
  delete: [attachment: AttachmentItem]
}>()

// 判断文件类型
const isImage = computed(() => {
  const imageTypes = ['png', 'jpeg', 'jpg']
  return imageTypes.includes(props.attachment.type.toLowerCase())
})

const isVideo = computed(() => {
  const videoTypes = ['mp4', 'rmvb', 'mov']
  return videoTypes.includes(props.attachment.type.toLowerCase())
})

// 尺寸样式
const sizeClasses = computed(() => {
  const sizes = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
  }
  return sizes[props.size]
})

// 格式化文件大小
function formatFileSize(bytes?: number) {
  if (!bytes)
    return ''
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${Math.round(bytes / 1024 ** i * 100) / 100} ${sizes[i]}`
}

// 获取文件扩展名显示
const fileExtension = computed(() => {
  return props.attachment.type.toUpperCase()
})

// 视频缩略图状态
const videoThumbnail = ref<string>('')
const videoLoading = ref(false)
const videoError = ref(false)

// 格式化视频URL
const formattedVideoUrl = computed(() => {
  if (!isVideo.value)
    return ''
  return formatMediaUrl(props.attachment.url)
})

// 生成视频缩略图
function generateVideoThumbnail() {
  if (!isVideo.value || videoThumbnail.value)
    return

  videoLoading.value = true
  videoError.value = false

  const video = document.createElement('video')
  video.crossOrigin = 'anonymous'
  video.preload = 'metadata'
  video.muted = true

  video.addEventListener('loadedmetadata', () => {
    // 设置到视频的第一秒或10%的位置
    video.currentTime = Math.min(1, video.duration * 0.1)
  })

  video.addEventListener('seeked', () => {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        videoError.value = true
        videoLoading.value = false
        return
      }

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
      videoThumbnail.value = canvas.toDataURL('image/jpeg', 0.8)
      videoLoading.value = false
    }
    catch (error) {
      console.warn('生成视频缩略图失败:', error)
      videoError.value = true
      videoLoading.value = false
    }
  })

  video.addEventListener('error', () => {
    videoError.value = true
    videoLoading.value = false
  })

  video.src = formattedVideoUrl.value
}

// 组件挂载时生成视频缩略图
onMounted(() => {
  if (isVideo.value) {
    generateVideoThumbnail()
  }
})

function handlePreview() {
  emit('preview', props.attachment)
}

function handleDelete() {
  emit('delete', props.attachment)
}
</script>

<template>
  <div class="group relative">
    <!-- 缩略图容器 -->
    <div
      :class="cn(
        'relative overflow-hidden rounded-lg   border-muted-foreground/25 bg-muted/50 transition-all duration-200 cursor-pointer',
        'hover:border-primary/50 hover:bg-muted/80 group-hover:shadow-md',
        sizeClasses,
      )"
      @click="handlePreview"
    >
      <!-- 图片缩略图 -->
      <img
        v-if="isImage"
        :src="attachment.thumbnailUrl || attachment.url"
        :alt="attachment.name"
        class="h-full w-full object-cover"
        loading="lazy"
      >

      <!-- 视频缩略图 -->
      <div
        v-else-if="isVideo"
        class="relative h-full w-full"
      >
        <!-- 视频画面缩略图 -->
        <img
          v-if="videoThumbnail && !videoError"
          :src="videoThumbnail"
          :alt="attachment.name"
          class="h-full w-full object-cover"
        >

        <!-- 加载状态 -->
        <div
          v-else-if="videoLoading"
          class="h-full w-full flex flex-col items-center justify-center from-blue-50 to-blue-100 bg-gradient-to-br dark:from-blue-950 dark:to-blue-900"
        >
          <div class="h-6 w-6 animate-spin border-b-2 border-blue-600 rounded-full dark:border-blue-400" />
          <span class="mt-2 text-xs text-blue-600 dark:text-blue-400">加载中</span>
        </div>

        <!-- 错误状态或默认状态 -->
        <div
          v-else
          class="h-full w-full flex flex-col items-center justify-center from-blue-50 to-blue-100 bg-gradient-to-br dark:from-blue-950 dark:to-blue-900"
        >
          <FileVideo class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          <Badge variant="secondary" class="mt-1 text-xs">
            {{ fileExtension }}
          </Badge>
        </div>

        <!-- 播放图标覆盖层 -->
        <div
          v-if="videoThumbnail && !videoError"
          class="absolute inset-0 flex items-center justify-center bg-black/20"
        >
          <div class="rounded-full bg-black/50 p-2">
            <Play class="h-4 w-4 fill-white text-white" />
          </div>
        </div>
      </div>

      <!-- 其他文件类型 -->
      <div
        v-else
        class="h-full w-full flex flex-col items-center justify-center from-gray-50 to-gray-100 bg-gradient-to-br dark:from-gray-950 dark:to-gray-900"
      >
        <FileImage class="h-8 w-8 text-gray-600 dark:text-gray-400" />
        <Badge variant="secondary" class="mt-1 text-xs">
          {{ fileExtension }}
        </Badge>
      </div>

      <!-- 悬浮操作按钮 -->
      <div class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 transition-opacity duration-200 group-hover:opacity-100">
        <div class="flex gap-2">
          <Button
            size="sm"
            variant="secondary"
            class="h-8 w-8 p-0"
            @click.stop="handlePreview"
          >
            <Eye class="h-4 w-4" />
          </Button>
          <Button
            v-if="allowDelete"
            size="sm"
            variant="destructive"
            class="h-8 w-8 p-0"
            @click.stop="handleDelete"
          >
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 文件信息 -->
    <div v-if="showFileName" class="mt-2 space-y-1">
      <p class="truncate text-xs text-foreground font-medium">
        {{ attachment.name }}
      </p>
      <p v-if="attachment.size" class="text-xs text-muted-foreground">
        {{ formatFileSize(attachment.size) }}
      </p>
    </div>
  </div>
</template>
