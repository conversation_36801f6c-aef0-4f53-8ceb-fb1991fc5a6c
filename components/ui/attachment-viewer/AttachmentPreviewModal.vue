<script setup lang="ts">
import type { AttachmentItem } from './index'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

import { ChevronLeft, ChevronRight, Download, X } from 'lucide-vue-next'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { formatMediaUrl } from '~/utils/mediaUrl'

interface Props {
  open: boolean
  attachments: AttachmentItem[]
  currentIndex: number
  allowDownload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  allowDownload: true,
})

const emit = defineEmits<{
  'update:open': [value: boolean]
  'update:currentIndex': [value: number]
  'download': [attachment: AttachmentItem]
}>()

const currentAttachment = computed(() => {
  return props.attachments[props.currentIndex] || null
})

// 格式化当前附件的URL - 使用ref避免computed的响应式问题
const currentAttachmentUrl = ref('')

// 静态URL缓存，避免重复计算
const urlCache = new Map<string, string>()

// 安全地更新附件URL，避免无限循环
function updateAttachmentUrl(attachment: AttachmentItem | null) {
  if (!attachment?.url) {
    currentAttachmentUrl.value = ''
    return
  }

  // 使用缓存避免重复计算
  const cacheKey = attachment.url
  if (urlCache.has(cacheKey)) {
    currentAttachmentUrl.value = urlCache.get(cacheKey)!
    return
  }

  // 计算并缓存URL
  const formattedUrl = formatMediaUrl(attachment.url)
  urlCache.set(cacheKey, formattedUrl)
  currentAttachmentUrl.value = formattedUrl
}

// 视频容器引用
const videoContainer = ref<HTMLDivElement>()

// 移除调试信息，避免可能的副作用

// 加载状态
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const loadingTimeout = ref<NodeJS.Timeout | null>(null)

// 处理图片/视频加载
function handleMediaLoad() {
  isLoading.value = false
  hasError.value = false
  errorMessage.value = ''
  clearLoadingTimeout()
}

function handleMediaError(event?: Event) {
  isLoading.value = false
  hasError.value = true
  clearLoadingTimeout()

  // 检查是否是CORS错误 - 但排除代理URL
  const isCorsError = currentAttachmentUrl.value
    && !currentAttachmentUrl.value.startsWith('/proxy/')
    && !currentAttachmentUrl.value.startsWith('/api/')
    && (currentAttachment.value?.url?.includes('myhuaweicloud.com')
      || currentAttachment.value?.url?.includes('obs-')
      || (currentAttachment.value?.url?.startsWith('https://') && !currentAttachment.value?.url?.includes(window.location.hostname)))

  if (isCorsError) {
    errorMessage.value = 'CORS跨域错误：视频服务器未允许跨域访问，请联系管理员配置CORS策略'
  }
  else {
    // 获取更详细的错误信息
    if (event?.target) {
      const target = event.target as HTMLVideoElement | HTMLImageElement
      if ('error' in target && target.error) {
        const error = target.error
        switch (error.code) {
          case error.MEDIA_ERR_ABORTED:
            errorMessage.value = '媒体加载被中止'
            break
          case error.MEDIA_ERR_NETWORK:
            errorMessage.value = '网络错误，可能是CORS跨域问题或网络连接问题'
            break
          case error.MEDIA_ERR_DECODE:
            errorMessage.value = '媒体解码错误，文件可能已损坏'
            break
          case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage.value = isCorsError ? 'CORS跨域错误：无法访问外部视频资源' : '不支持的媒体格式或文件不存在'
            break
          default:
            errorMessage.value = '媒体加载失败'
        }
      }
      else {
        errorMessage.value = isCorsError ? 'CORS跨域错误：无法访问外部视频资源' : '媒体加载失败'
      }
    }
    else {
      errorMessage.value = isCorsError ? 'CORS跨域错误：无法访问外部视频资源' : '媒体加载失败'
    }
  }

  // 移除console.error避免可能的副作用
}

function handleMediaLoadStart() {
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''

  // 设置加载超时（30秒）
  clearLoadingTimeout()
  loadingTimeout.value = setTimeout(() => {
    if (isLoading.value) {
      handleMediaError()
      errorMessage.value = '加载超时，请检查网络连接或文件是否存在'
    }
  }, 30000)
}

function clearLoadingTimeout() {
  if (loadingTimeout.value) {
    clearTimeout(loadingTimeout.value)
    loadingTimeout.value = null
  }
}

// 移除手动创建视频元素的逻辑

// 重新加载媒体
function retryLoad() {
  if (currentAttachment.value && currentAttachmentUrl.value) {
    handleMediaLoadStart()
    // 强制重新加载
    nextTick(() => {
      const videoEl = document.querySelector(`video[src="${currentAttachmentUrl.value}"]`) as HTMLVideoElement
      const imgEl = document.querySelector(`img[src="${currentAttachmentUrl.value}"]`) as HTMLImageElement

      if (videoEl) {
        videoEl.load()
      }
      else if (imgEl) {
        // 强制重新加载图片
        const originalSrc = imgEl.src
        imgEl.src = ''
        imgEl.src = originalSrc
      }
    })
  }
}

// 监听当前附件变化，重置状态并更新URL
watch(currentAttachment, (newAttachment) => {
  isLoading.value = false
  hasError.value = false
  errorMessage.value = ''
  clearLoadingTimeout()

  // 安全地更新URL
  updateAttachmentUrl(newAttachment)

  // 如果是视频，延迟创建视频元素
  if (newAttachment?.type && ['mp4', 'rmvb', 'mov'].includes(newAttachment.type.toLowerCase())) {
    // 使用setTimeout确保DOM已经更新
    setTimeout(() => {
      createVideoElement(currentAttachmentUrl.value)
    }, 100)
  }
}, { immediate: true })

// 组件卸载时清理定时器
onUnmounted(() => {
  clearLoadingTimeout()
})

// 判断文件类型
const isImage = computed(() => {
  if (!currentAttachment.value)
    return false
  const imageTypes = ['png', 'jpeg', 'jpg']
  return imageTypes.includes(currentAttachment.value.type.toLowerCase())
})

const isVideo = computed(() => {
  if (!currentAttachment.value)
    return false
  const videoTypes = ['mp4', 'rmvb', 'mov']
  return videoTypes.includes(currentAttachment.value.type.toLowerCase())
})

// 导航功能
const canGoPrevious = computed(() => props.currentIndex > 0)
const canGoNext = computed(() => props.currentIndex < props.attachments.length - 1)

function goToPrevious() {
  if (canGoPrevious.value) {
    emit('update:currentIndex', props.currentIndex - 1)
  }
}

function goToNext() {
  if (canGoNext.value) {
    emit('update:currentIndex', props.currentIndex + 1)
  }
}

// 键盘导航
function handleKeydown(event: KeyboardEvent) {
  if (!props.open)
    return

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      goToPrevious()
      break
    case 'ArrowRight':
      event.preventDefault()
      goToNext()
      break
    case 'Escape':
      event.preventDefault()
      emit('update:open', false)
      break
  }
}

// 监听键盘事件和弹窗打开状态
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    document.addEventListener('keydown', handleKeydown)

    // 弹窗打开时，如果当前是视频，立即创建视频元素
    nextTick(() => {
      if (currentAttachment.value?.type && ['mp4', 'rmvb', 'mov'].includes(currentAttachment.value.type.toLowerCase())) {
        updateAttachmentUrl(currentAttachment.value)
        setTimeout(() => {
          createVideoElement(currentAttachmentUrl.value)
        }, 100)
      }
    })
  }
  else {
    document.removeEventListener('keydown', handleKeydown)
  }
})

function handleDownload() {
  if (currentAttachment.value) {
    emit('download', currentAttachment.value)
  }
}

function handleClose() {
  emit('update:open', false)
}

// 响应式尺寸计算
const viewportSize = ref({ width: 0, height: 0 })

function updateViewportSize() {
  viewportSize.value = {
    width: window.innerWidth,
    height: window.innerHeight,
  }
}

// 计算最大显示尺寸
const maxDisplaySize = computed(() => {
  const vw = viewportSize.value.width
  const vh = viewportSize.value.height

  // 根据屏幕尺寸动态调整
  let widthRatio = 0.85
  let heightRatio = 0.75
  let headerHeight = 120
  let padding = 48

  // 移动端适配
  if (vw < 640) { // sm
    widthRatio = 0.95
    heightRatio = 0.85
    headerHeight = 100
    padding = 24
  }
  else if (vw < 768) { // md
    widthRatio = 0.90
    heightRatio = 0.80
    headerHeight = 110
    padding = 32
  }

  const maxWidth = Math.max(vw * widthRatio - padding * 2, 300)
  const maxHeight = Math.max(vh * heightRatio - headerHeight, 200)

  return {
    width: maxWidth,
    height: maxHeight,
  }
})

// 手动创建视频元素，避免Vue响应式系统的问题
function createVideoElement(url: string) {
  if (!videoContainer.value || !url)
    return

  // 清空容器
  videoContainer.value.innerHTML = ''

  // 创建视频元素
  const video = document.createElement('video')
  video.src = url
  video.controls = true
  video.preload = 'metadata'
  video.className = 'rounded-lg shadow-lg'

  // 设置等比例缩放样式
  video.style.cssText = `
    max-width: ${maxDisplaySize.value.width}px;
    max-height: ${maxDisplaySize.value.height}px;
    width: auto;
    height: auto;
    display: block;
    object-fit: contain;
  `

  // 添加事件监听器（但不触发Vue的响应式更新）
  video.addEventListener('loadstart', () => {
    // 静默处理，不触发响应式更新
  })
  video.addEventListener('canplay', () => {
    // 静默处理，不触发响应式更新
  })
  video.addEventListener('error', () => {
    // 静默处理，不触发响应式更新
  })

  // 添加到容器
  videoContainer.value.appendChild(video)
}

// 监听窗口大小变化
onMounted(() => {
  updateViewportSize()
  window.addEventListener('resize', updateViewportSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateViewportSize)
})

// 监听窗口大小变化，更新视频元素尺寸
watch(maxDisplaySize, () => {
  // 如果当前显示的是视频，更新视频元素的尺寸
  if (videoContainer.value && currentAttachment.value?.type && ['mp4', 'rmvb', 'mov'].includes(currentAttachment.value.type.toLowerCase())) {
    const video = videoContainer.value.querySelector('video')
    if (video) {
      video.style.maxWidth = `${maxDisplaySize.value.width}px`
      video.style.maxHeight = `${maxDisplaySize.value.height}px`
    }
  }
}, { deep: true })

// 格式化文件大小
function formatFileSize(bytes?: number) {
  if (!bytes)
    return ''
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${Math.round(bytes / 1024 ** i * 100) / 100} ${sizes[i]}`
}
</script>

<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent
      class="h-[90vh] max-w-6xl w-[95vw] p-0 [&>button]:hidden lg:max-w-5xl md:h-[80vh] md:w-[85vw] sm:h-[85vh] sm:w-[90vw] xl:max-w-6xl"
    >
      <div class="h-full flex flex-col">
        <!-- 头部 -->
        <DialogHeader class="flex-shrink-0 border-b px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="min-w-0 flex-1">
              <DialogTitle class="truncate">
                {{ currentAttachment?.name || '附件预览' }}
              </DialogTitle>
              <div class="mt-1 flex items-center gap-2">
                <Badge variant="secondary">
                  {{ currentAttachment?.type.toUpperCase() }}
                </Badge>
                <span v-if="currentAttachment?.size" class="text-sm text-muted-foreground">
                  {{ formatFileSize(currentAttachment.size) }}
                </span>
                <span class="text-sm text-muted-foreground">
                  {{ currentIndex + 1 }} / {{ attachments.length }}
                </span>
              </div>
            </div>

            <div class="flex items-center gap-2">
              <!-- 下载按钮 -->
              <Button
                v-if="allowDownload && currentAttachment"
                variant="outline"
                size="sm"
                @click="handleDownload"
              >
                <Download class="mr-2 h-4 w-4" />
                下载
              </Button>

              <!-- 关闭按钮 -->
              <Button
                variant="ghost"
                size="sm"
                @click="handleClose"
              >
                <X class="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <!-- 内容区域 -->
        <div class="relative flex-1 overflow-hidden">
          <div class="h-full overflow-auto">
            <div class="min-h-full flex items-center justify-center p-6">
              <!-- 图片预览 -->
              <div
                v-if="isImage && currentAttachment"
                class="relative flex items-center justify-center"
                :style="{
                  minHeight: `${maxDisplaySize.height}px`,
                  width: '100%',
                }"
              >
                <!-- 加载状态 -->
                <div
                  v-if="isLoading"
                  class="absolute inset-0 flex items-center justify-center rounded-lg bg-background/80"
                >
                  <div class="flex flex-col items-center gap-2">
                    <div class="h-8 w-8 animate-spin border-b-2 border-primary rounded-full" />
                    <span class="text-sm text-muted-foreground">加载中...</span>
                  </div>
                </div>

                <!-- 错误状态 -->
                <div
                  v-else-if="hasError"
                  class="flex flex-col items-center gap-4 p-8 text-center"
                >
                  <div class="text-muted-foreground">
                    <svg class="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="mb-2 text-lg font-medium">
                      图片加载失败
                    </h3>
                    <p class="mb-4 text-muted-foreground">
                      {{ errorMessage || '图片加载失败，请检查网络连接或稍后重试' }}
                    </p>
                    <div class="flex gap-2">
                      <Button variant="outline" @click="retryLoad">
                        重新加载
                      </Button>
                      <Button v-if="allowDownload" variant="secondary" @click="handleDownload">
                        <Download class="mr-2 h-4 w-4" />
                        下载
                      </Button>
                    </div>
                  </div>
                </div>

                <!-- 图片内容 -->
                <img
                  v-else
                  :src="currentAttachmentUrl"
                  :alt="currentAttachment.name"
                  class="max-h-full max-w-full rounded-lg object-contain shadow-lg transition-all duration-200"
                  :style="{
                    maxWidth: `${maxDisplaySize.width}px`,
                    maxHeight: `${maxDisplaySize.height}px`,
                    width: 'auto',
                    height: 'auto',
                    display: 'block',
                  }"
                  @load="handleMediaLoad"
                  @error="handleMediaError"
                  @loadstart="handleMediaLoadStart"
                >
              </div>

              <!-- 视频预览 -->
              <div
                v-else-if="isVideo && currentAttachment"
                class="relative flex items-center justify-center"
                :style="{
                  minHeight: `${maxDisplaySize.height}px`,
                  width: '100%',
                }"
              >
                <!-- 视频加载状态 -->
                <div
                  v-if="isLoading"
                  class="absolute inset-0 z-10 flex items-center justify-center rounded-lg bg-background/80"
                >
                  <div class="flex flex-col items-center gap-2">
                    <div class="h-8 w-8 animate-spin border-b-2 border-primary rounded-full" />
                    <span class="text-sm text-muted-foreground">加载视频中...</span>
                  </div>
                </div>

                <!-- 视频错误状态 -->
                <div
                  v-else-if="hasError"
                  class="flex flex-col items-center gap-4 p-8 text-center"
                >
                  <div class="text-muted-foreground">
                    <svg class="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <div>
                    <h3 class="mb-2 text-lg font-medium">
                      视频加载失败
                    </h3>
                    <p class="mb-4 text-muted-foreground">
                      {{ errorMessage || '视频无法播放，请检查网络连接或文件格式' }}
                    </p>
                    <div v-if="currentAttachment" class="mb-4 text-xs text-muted-foreground">
                      <div>文件名: {{ currentAttachment.name }}</div>
                      <div>文件类型: {{ currentAttachment.type }}</div>
                      <div>原始URL: {{ currentAttachment.url }}</div>
                      <div>格式化URL: {{ currentAttachmentUrl }}</div>
                    </div>
                    <div class="flex gap-2">
                      <Button variant="outline" @click="retryLoad">
                        重新加载
                      </Button>
                      <Button v-if="allowDownload" variant="secondary" @click="handleDownload">
                        <Download class="mr-2 h-4 w-4" />
                        下载
                      </Button>
                    </div>
                  </div>
                </div>

                <!-- 视频内容 -->
                <div
                  v-else
                  ref="videoContainer"
                  class="flex items-center justify-center"
                  :style="{
                    maxWidth: `${maxDisplaySize.width}px`,
                    maxHeight: `${maxDisplaySize.height}px`,
                    width: '100%',
                    height: '100%',
                  }"
                />
              </div>

              <!-- 不支持的文件类型 -->
              <div
                v-else
                class="flex flex-col items-center justify-center p-8 text-center"
              >
                <div class="mb-4 text-muted-foreground">
                  <svg class="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 class="mb-2 text-lg font-medium">
                  无法预览此文件
                </h3>
                <p class="mb-4 text-muted-foreground">
                  此文件类型不支持在线预览，请下载后查看。
                </p>
                <Button v-if="allowDownload" @click="handleDownload">
                  <Download class="mr-2 h-4 w-4" />
                  下载文件
                </Button>
              </div>
            </div>
          </div>

          <!-- 导航按钮 -->
          <div v-if="attachments.length > 1" class="absolute inset-y-0 left-4 flex items-center">
            <Button
              :disabled="!canGoPrevious"
              variant="secondary"
              size="sm"
              class="h-10 w-10 rounded-full p-0 shadow-lg"
              @click="goToPrevious"
            >
              <ChevronLeft class="h-5 w-5" />
            </Button>
          </div>

          <div v-if="attachments.length > 1" class="absolute inset-y-0 right-4 flex items-center">
            <Button
              :disabled="!canGoNext"
              variant="secondary"
              size="sm"
              class="h-10 w-10 rounded-full p-0 shadow-lg"
              @click="goToNext"
            >
              <ChevronRight class="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
