<script setup lang="ts">
import type { AttachmentItem, AttachmentViewerProps } from './index'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

import { computed, ref } from 'vue'
import AttachmentPreviewModal from './AttachmentPreviewModal.vue'
import AttachmentThumbnail from './AttachmentThumbnail.vue'
import AttachmentThumbnailWithUpload from './AttachmentThumbnailWithUpload.vue'

interface UploadingFile {
  uid: string
  file: File
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

interface ExtendedAttachmentViewerProps extends AttachmentViewerProps {
  allowDelete?: boolean
  uploadingFiles?: UploadingFile[]
  allowRetry?: boolean
}

const props = withDefaults(defineProps<ExtendedAttachmentViewerProps>(), {
  maxThumbnails: 6, // 保留属性以保持向后兼容性，但不再使用
  thumbnailSize: 'md',
  showFileName: true,
  allowDownload: true,
  allowDelete: false,
  uploadingFiles: () => [],
  allowRetry: false,
})

const emit = defineEmits<{
  download: [attachment: AttachmentItem]
  delete: [attachment: AttachmentItem]
  deleteUploading: [file: File]
  retryUpload: [file: File]
}>()

// 预览弹窗状态
const isPreviewOpen = ref(false)
const currentPreviewIndex = ref(0)

// 支持的文件类型
const supportedTypes = ['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov']

// 过滤支持的附件
const validAttachments = computed(() => {
  return props.attachments.filter(attachment =>
    supportedTypes.includes(attachment.type.toLowerCase()),
  )
})

// 显示的缩略图 - 显示所有附件
const displayedAttachments = computed(() => {
  return validAttachments.value
})

// 网格样式 - 支持自动换行的响应式网格
const gridClasses = computed(() => {
  // 使用固定的响应式网格，支持自动换行
  return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6'
})

// 处理预览
function handlePreview(attachment: AttachmentItem) {
  const index = validAttachments.value.findIndex(item => item.id === attachment.id)
  if (index !== -1) {
    currentPreviewIndex.value = index
    isPreviewOpen.value = true
  }
}

// 处理下载
function handleDownload(attachment: AttachmentItem) {
  // 创建下载链接
  const link = document.createElement('a')
  link.href = attachment.url
  link.download = attachment.name
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  emit('download', attachment)
}

// 处理删除
function handleDelete(attachment: AttachmentItem) {
  emit('delete', attachment)
}

// 处理删除上传中的文件
function handleDeleteUploading(attachment: AttachmentItem | undefined, file: File | undefined) {
  if (attachment) {
    emit('delete', attachment)
  }
  else if (file) {
    emit('deleteUploading', file)
  }
}

// 处理重试上传
function handleRetryUpload(file: File) {
  emit('retryUpload', file)
}

// 统计不同类型的文件
const fileTypeStats = computed(() => {
  const stats = {
    images: 0,
    videos: 0,
    total: validAttachments.value.length + props.uploadingFiles.length,
  }

  // 统计已上传的文件
  validAttachments.value.forEach((attachment) => {
    const type = attachment.type.toLowerCase()
    if (['png', 'jpeg', 'jpg'].includes(type)) {
      stats.images++
    }
    else if (['mp4', 'rmvb', 'mov'].includes(type)) {
      stats.videos++
    }
  })

  // 统计上传中的文件
  props.uploadingFiles.forEach((uploadingFile) => {
    const extension = uploadingFile.file.name.split('.').pop()?.toLowerCase()
    if (['png', 'jpeg', 'jpg'].includes(extension || '')) {
      stats.images++
    }
    else if (['mp4', 'rmvb', 'mov'].includes(extension || '')) {
      stats.videos++
    }
  })

  return stats
})
</script>

<template>
  <div class="space-y-3">
    <!-- 附件统计信息 -->
    <div v-if="validAttachments.length > 0" class="flex items-center gap-2 text-sm text-muted-foreground">
      <span>共 {{ fileTypeStats.total }} 个附件</span>
      <Badge v-if="fileTypeStats.images > 0" variant="secondary" class="text-xs">
        {{ fileTypeStats.images }} 张图片
      </Badge>
      <Badge v-if="fileTypeStats.videos > 0" variant="secondary" class="text-xs">
        {{ fileTypeStats.videos }} 个视频
      </Badge>
    </div>

    <!-- 附件网格 -->
    <div v-if="validAttachments.length > 0 || uploadingFiles.length > 0" :class="cn('grid gap-2', gridClasses)">
      <!-- 显示已上传的缩略图 -->
      <AttachmentThumbnail
        v-for="attachment in displayedAttachments"
        :key="attachment.id"
        :attachment="attachment"
        :size="thumbnailSize"
        :show-file-name="showFileName"
        :allow-delete="allowDelete"
        @preview="handlePreview"
        @delete="handleDelete"
      />

      <!-- 显示上传中的文件 -->
      <AttachmentThumbnailWithUpload
        v-for="uploadingFile in uploadingFiles"
        :key="uploadingFile.uid"
        :file="uploadingFile.file"
        :upload-progress="uploadingFile.progress"
        :upload-status="uploadingFile.status"
        :upload-error="uploadingFile.error"
        :size="thumbnailSize"
        :show-file-name="showFileName"
        :allow-delete="true"
        :allow-retry="allowRetry"
        @delete="handleDeleteUploading"
        @retry="handleRetryUpload"
      />
    </div>

    <!-- 无附件状态 -->
    <div v-else class="flex flex-col items-center justify-center py-8 text-center">
      <div class="mb-2 text-muted-foreground">
        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      </div>
      <p class="text-sm text-muted-foreground">
        暂无附件
      </p>
    </div>

    <!-- 预览弹窗 -->
    <AttachmentPreviewModal
      v-model:open="isPreviewOpen"
      v-model:current-index="currentPreviewIndex"
      :attachments="validAttachments"
      :allow-download="allowDownload"
      @download="handleDownload"
    />
  </div>
</template>
