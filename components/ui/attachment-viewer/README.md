# AttachmentViewer 附件查看器组件

一个基于 shadcn-ui 的附件查看器组件，支持图片和视频的在线预览，提供缩略图展示和全屏预览功能。

## 特性

- ✅ 支持多种文件格式：PNG、JPEG、JPG、MP4、RMVB、MOV
- ✅ 缩略图网格展示，支持三种尺寸（sm、md、lg）
- ✅ 点击预览大图或播放视频
- ✅ **智能缩放**：自动适应屏幕尺寸，保持原始宽高比
- ✅ **响应式设计**：移动端优化，动态调整显示尺寸
- ✅ 支持键盘导航（左右箭头切换，ESC关闭）
- ✅ 文件下载功能
- ✅ **加载状态管理**：显示加载进度，错误重试机制
- ✅ 自动统计文件类型和数量
- ✅ 响应式网格布局
- ✅ 当附件过多时显示"更多"按钮
- ✅ 完全基于 shadcn-ui 设计系统

## 组件结构

```
components/ui/attachment-viewer/
├── AttachmentViewer.vue          # 主组件
├── AttachmentThumbnail.vue       # 缩略图组件
├── AttachmentPreviewModal.vue    # 预览弹窗组件
├── index.ts                      # 导出文件
└── README.md                     # 说明文档
```

## 基本用法

### 导入组件

```vue
<script setup lang="ts">
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
</script>
```

### 基础示例

```vue
<script setup lang="ts">
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { ref } from 'vue'

const attachments = ref<AttachmentItem[]>([
  {
    id: '1',
    name: '截图1.png',
    url: 'https://example.com/image1.png',
    type: 'png',
    size: 245760,
    thumbnailUrl: 'https://example.com/thumb1.png'
  },
  {
    id: '2',
    name: '演示视频.mp4',
    url: 'https://example.com/video1.mp4',
    type: 'mp4',
    size: 15728640
  }
])

function handleDownload(attachment: AttachmentItem) {
  console.log('下载附件:', attachment.name)
}
</script>

<template>
  <AttachmentViewer
    :attachments="attachments"
    @download="handleDownload"
  />
</template>
```

## API 参考

### AttachmentViewer Props

| 属性            | 类型                   | 默认值 | 说明               |
| --------------- | ---------------------- | ------ | ------------------ |
| `attachments`   | `AttachmentItem[]`     | `[]`   | 附件列表           |
| `maxThumbnails` | `number`               | `6`    | 最大显示缩略图数量 |
| `thumbnailSize` | `'sm' \| 'md' \| 'lg'` | `'md'` | 缩略图尺寸         |
| `showFileName`  | `boolean`              | `true` | 是否显示文件名     |
| `allowDownload` | `boolean`              | `true` | 是否允许下载       |

### AttachmentViewer Events

| 事件       | 参数                           | 说明           |
| ---------- | ------------------------------ | -------------- |
| `download` | `(attachment: AttachmentItem)` | 下载附件时触发 |

### AttachmentItem 接口

```typescript
interface AttachmentItem {
  id: string // 唯一标识
  name: string // 文件名
  url: string // 文件URL
  type: string // 文件类型（扩展名）
  size?: number // 文件大小（字节）
  thumbnailUrl?: string // 缩略图URL（可选）
}
```

## 配置选项

### 缩略图尺寸

```vue
<!-- 小尺寸 -->
<AttachmentViewer :attachments="attachments" thumbnail-size="sm" />

<!-- 中等尺寸（默认） -->
<AttachmentViewer :attachments="attachments" thumbnail-size="md" />

<!-- 大尺寸 -->
<AttachmentViewer :attachments="attachments" thumbnail-size="lg" />
```

### 限制显示数量

```vue
<!-- 最多显示4个缩略图，其余显示"更多"按钮 -->
<AttachmentViewer :attachments="attachments" :max-thumbnails="4" />
```

### 隐藏文件名

```vue
<AttachmentViewer :attachments="attachments" :show-file-name="false" />
```

### 禁用下载

```vue
<AttachmentViewer :attachments="attachments" :allow-download="false" />
```

## 在工单系统中的使用

在 `TicketDetail.vue` 中的集成示例：

```vue
<script setup lang="ts">
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import type { Ticket } from '~/types/ticket'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { computed } from 'vue'

const props = defineProps<{ ticket: Ticket }>()

// 转换附件数据格式
const attachmentItems = computed<AttachmentItem[]>(() => {
  if (!props.ticket?.attachments)
    return []

  return props.ticket.attachments.map((attachment, index) => ({
    id: attachment.uid || attachment.name || `attachment-${index}`,
    name: attachment.name || `附件${index + 1}`,
    url: attachment.url || '',
    type: attachment.type || '',
    size: attachment.size ? Number.parseInt(attachment.size, 10) : undefined,
    thumbnailUrl: undefined
  }))
})

function handleAttachmentDownload(attachment: AttachmentItem) {
  // 处理下载逻辑
}
</script>

<template>
  <div v-if="attachmentItems.length > 0" class="mb-6 space-y-3">
    <div class="text-xs text-muted-foreground font-medium">
      附件
    </div>
    <AttachmentViewer
      :attachments="attachmentItems"
      :max-thumbnails="6"
      thumbnail-size="md"
      :show-file-name="true"
      :allow-download="true"
      @download="handleAttachmentDownload"
    />
  </div>
</template>
```

## 支持的文件格式

- **图片**: PNG, JPEG, JPG
- **视频**: MP4, RMVB, MOV

## 键盘快捷键

在预览模式下：

- `←` / `→` : 切换上一个/下一个附件
- `Esc` : 关闭预览

## 测试页面

访问 `/test/attachment-viewer` 查看完整的功能演示和使用示例。

## 智能缩放功能

### 自适应尺寸计算

组件会根据用户的屏幕尺寸动态计算最佳显示尺寸：

```typescript
// 响应式尺寸计算
const maxDisplaySize = computed(() => {
  const vw = viewportSize.value.width
  const vh = viewportSize.value.height

  // 根据屏幕尺寸动态调整
  let widthRatio = 0.85
  let heightRatio = 0.75

  // 移动端适配
  if (vw < 640) { // 小屏幕
    widthRatio = 0.95
    heightRatio = 0.85
  }
  else if (vw < 768) { // 中等屏幕
    widthRatio = 0.90
    heightRatio = 0.80
  }

  return {
    width: Math.max(vw * widthRatio - padding * 2, 300),
    height: Math.max(vh * heightRatio - headerHeight, 200)
  }
})
```

### 缩放特性

- **保持宽高比**：使用 `object-contain` 确保图片/视频不会变形
- **动态限制**：根据屏幕尺寸动态设置最大宽度和高度
- **移动端优化**：在小屏幕设备上使用更大的显示比例
- **平滑过渡**：添加 CSS 过渡动画，提供流畅的缩放体验
- **最小尺寸保护**：确保内容在任何设备上都有合理的最小显示尺寸

### 加载状态管理

- **加载指示器**：显示旋转动画和加载文本
- **错误处理**：网络错误或文件损坏时显示错误信息
- **重试机制**：提供重新加载按钮
- **状态同步**：切换附件时自动重置加载状态

## 测试页面

- **完整功能测试**: `/test/attachment-viewer`
- **简单使用示例**: `/test/attachment-viewer-simple`
- **缩放功能测试**: `/test/attachment-scaling`

## 注意事项

1. 组件会自动过滤不支持的文件类型
2. 视频文件会显示播放器图标，图片文件会显示缩略图
3. 如果没有提供 `thumbnailUrl`，图片会直接使用原图作为缩略图
4. 文件大小会自动格式化显示（B, KB, MB, GB）
5. 组件完全响应式，适配不同屏幕尺寸
6. **大文件处理**：超大图片和视频会自动缩放到合适尺寸，避免性能问题
7. **网络优化**：支持预加载和错误重试，提升用户体验
