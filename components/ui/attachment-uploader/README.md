# AttachmentUploader 附件上传组件

一个基于 shadcn-ui 的附件上传组件，支持拖拽上传、进度显示、错误重试等功能，并集成了独立的上传API接口。

## 特性

- ✅ 支持拖拽上传和点击上传
- ✅ 实时上传进度显示
- ✅ 上传失败重试机制
- ✅ 文件大小和类型验证
- ✅ 支持多文件上传
- ✅ 独立的上传API接口
- ✅ 完全基于 shadcn-ui 设计系统
- ✅ TypeScript 支持

## 组件结构

```
components/ui/attachment-uploader/
├── AttachmentUploader.vue       # 主组件
├── index.ts                     # 导出文件
└── README.md                    # 说明文档
```

## API 接口

### 上传接口

```typescript
POST /api/feedback/upload/

// 请求参数
{
  file: File,    // 文件对象
  uid: string    // 唯一标识符
}

// 响应格式
{
  "code": 20000,
  "msg": "上传成功",
  "data": {
    "uid": "1234567890_abcdefghij",
    "url": "https://example.com/uploads/file.jpg",
    "name": "file.jpg",
    "type": "jpg",
    "size": 1048576
  },
  "status": "ok"
}
```

## 基本用法

### 导入组件

```vue
<script setup lang="ts">
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import { AttachmentUploader } from '@/components/ui/attachment-uploader'
import { ref } from 'vue'

const attachments = ref<AttachmentItem[]>([])

function handleUploadSuccess(attachment: AttachmentItem) {
  console.log('上传成功:', attachment)
}

function handleUploadError(error: string) {
  console.error('上传失败:', error)
}
</script>

<template>
  <AttachmentUploader
    v-model="attachments"
    :max-files="10"
    :max-size="100"
    :accept="['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov']"
    :multiple="true"
    @upload-success="handleUploadSuccess"
    @upload-error="handleUploadError"
  />
</template>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `AttachmentItem[]` | `[]` | 附件列表 |
| `maxFiles` | `number` | `10` | 最大文件数量 |
| `maxSize` | `number` | `100` | 最大文件大小(MB) |
| `accept` | `string[]` | `['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov']` | 支持的文件类型 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `multiple` | `boolean` | `true` | 是否支持多文件 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `AttachmentItem[]` | 附件列表更新 |
| `uploadSuccess` | `AttachmentItem` | 上传成功 |
| `uploadError` | `string` | 上传失败 |

## 数据类型

```typescript
interface AttachmentItem {
  id: string
  name: string
  url: string
  type: string
  size?: number
  thumbnailUrl?: string
}
```

## 在创建工单中的使用

```vue
<script setup lang="ts">
import { AttachmentUploader } from '@/components/ui/attachment-uploader'

const formData = ref({
  // ... 其他表单字段
  attachments: [] as AttachmentItem[]
})

function handleUploadSuccess(attachment: AttachmentItem) {
  console.log('附件上传成功:', attachment)
}

function handleUploadError(error: string) {
  console.error('附件上传失败:', error)
}
</script>

<template>
  <div class="space-y-2">
    <Label class="text-sm font-medium">截图视频</Label>
    <AttachmentUploader
      v-model="formData.attachments"
      :max-files="10"
      :max-size="100"
      :accept="['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov']"
      :multiple="true"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
  </div>
</template>
```

## 支持的文件格式

- **图片**: PNG, JPEG, JPG
- **视频**: MP4, RMVB, MOV

## 功能特性

### 拖拽上传
- 支持将文件拖拽到上传区域
- 拖拽时显示视觉反馈

### 进度显示
- 实时显示上传进度
- 上传状态图标（上传中、成功、失败）

### 错误处理
- 文件大小验证
- 文件类型验证
- 上传失败重试
- 错误信息提示

### 文件管理
- 显示已上传文件列表
- 支持删除已上传文件
- 文件信息展示（名称、大小、类型）

## 测试页面

访问 `/test/attachment-uploader` 查看完整的功能演示和使用示例。

## 注意事项

1. 确保后端API接口 `/api/feedback/upload/` 已正确实现
2. 文件上传需要正确的CORS配置
3. 大文件上传可能需要调整服务器超时设置
4. 建议在生产环境中添加文件病毒扫描
