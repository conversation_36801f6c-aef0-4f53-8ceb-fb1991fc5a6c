<script setup lang="ts">
import type { AttachmentItem } from '~/components/ui/attachment-viewer'
import { cn } from '@/lib/utils'
import { Upload } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { useNotification } from '~/composables/useNotification'
import { requestClient } from '~/utils/request'

interface UploadingFile {
  uid: string
  file: File
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

interface AttachmentUploaderSimpleProps {
  modelValue: AttachmentItem[]
  uploadingFiles: UploadingFile[]
  maxFiles?: number
  maxSize?: number // MB
  accept?: string[]
  disabled?: boolean
  multiple?: boolean
}

interface AttachmentUploaderSimpleEmits {
  (e: 'update:modelValue', value: AttachmentItem[]): void
  (e: 'update:uploadingFiles', value: UploadingFile[]): void
  (e: 'uploadSuccess', attachment: AttachmentItem): void
  (e: 'uploadError', error: string): void
}

const props = withDefaults(defineProps<AttachmentUploaderSimpleProps>(), {
  maxFiles: 10,
  maxSize: 100, // 100MB
  accept: () => ['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov'],
  disabled: false,
  multiple: true,
})

const emit = defineEmits<AttachmentUploaderSimpleEmits>()

const notification = useNotification()
const fileInputRef = ref<HTMLInputElement>()

// 生成唯一ID
function generateUID(): string {
  return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// 上传文件API
async function uploadFile(params: { file: File, uid: string }) {
  const formData = new FormData()
  formData.append('file', params.file)
  formData.append('uid', params.uid)

  return requestClient.post('/feedback/upload/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 计算属性
const acceptString = computed(() => {
  return props.accept.map(ext => `.${ext}`).join(',')
})

const canUploadMore = computed(() => {
  return (props.modelValue.length + props.uploadingFiles.length) < props.maxFiles && !props.disabled
})

const totalFiles = computed(() => {
  return props.modelValue.length + props.uploadingFiles.length
})

// 文件验证
function validateFile(file: File): string | null {
  // 检查文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    return `文件大小不能超过 ${props.maxSize}MB`
  }

  // 检查文件类型
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  if (!fileExtension || !props.accept.includes(fileExtension)) {
    return `不支持的文件格式，仅支持: ${props.accept.join(', ')}`
  }

  return null
}

// 处理文件选择
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  processFiles(files)
  // 清空input值，允许重复选择同一文件
  target.value = ''
}

// 处理拖拽上传
function handleDrop(event: DragEvent) {
  event.preventDefault()
  if (props.disabled || !canUploadMore.value)
    return

  const files = Array.from(event.dataTransfer?.files || [])
  processFiles(files)
}

// 处理文件列表
function processFiles(files: File[]) {
  if (!canUploadMore.value) {
    notification.error('已达到最大文件数量限制')
    return
  }

  const remainingSlots = props.maxFiles - totalFiles.value
  const filesToProcess = files.slice(0, remainingSlots)

  if (files.length > remainingSlots) {
    notification.warning(`只能上传 ${remainingSlots} 个文件，已自动过滤多余文件`)
  }

  filesToProcess.forEach((file) => {
    const error = validateFile(file)
    if (error) {
      notification.error(`${file.name}: ${error}`)
      return
    }

    uploadSingleFile(file)
  })
}

// 上传单个文件
async function uploadSingleFile(file: File) {
  const uid = generateUID()
  const uploadingFile: UploadingFile = {
    uid,
    file,
    progress: 0,
    status: 'uploading',
  }

  // 添加到上传列表
  const newUploadingFiles = [...props.uploadingFiles, uploadingFile]
  emit('update:uploadingFiles', newUploadingFiles)

  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadingFile.progress < 90) {
        uploadingFile.progress += Math.random() * 20
        // 更新进度
        const updatedFiles = props.uploadingFiles.map(f => 
          f.uid === uid ? { ...f, progress: uploadingFile.progress } : f
        )
        emit('update:uploadingFiles', updatedFiles)
      }
    }, 200)

    const response = await uploadFile({ file, uid })

    clearInterval(progressInterval)
    uploadingFile.progress = 100
    uploadingFile.status = 'success'

    // 创建附件对象
    const attachment: AttachmentItem = {
      id: response.uid || uid,
      name: response.name || file.name,
      url: response.url,
      type: response.type || file.name.split('.').pop()?.toLowerCase() || '',
      size: response.size || file.size,
    }

    // 更新模型值
    const newAttachments = [...props.modelValue, attachment]
    emit('update:modelValue', newAttachments)
    emit('uploadSuccess', attachment)

    notification.success(`${file.name} 上传成功`)

    // 从上传列表中移除
    const filteredUploadingFiles = props.uploadingFiles.filter(f => f.uid !== uid)
    emit('update:uploadingFiles', filteredUploadingFiles)
  }
  catch (error) {
    console.error('上传过程中发生错误:', error)
    uploadingFile.status = 'error'
    uploadingFile.error = error instanceof Error ? error.message : '上传失败'
    
    // 更新错误状态
    const updatedFiles = props.uploadingFiles.map(f => 
      f.uid === uid ? { ...f, status: 'error' as const, error: uploadingFile.error } : f
    )
    emit('update:uploadingFiles', updatedFiles)
    
    emit('uploadError', uploadingFile.error)
    notification.error(`${file.name} 上传失败: ${uploadingFile.error}`)
  }
}

// 重试上传
function retryUpload(file: File) {
  // 移除失败的上传记录
  const filteredUploadingFiles = props.uploadingFiles.filter(f => f.file !== file)
  emit('update:uploadingFiles', filteredUploadingFiles)
  
  // 重新上传
  uploadSingleFile(file)
}

// 取消上传
function cancelUpload(file: File) {
  const filteredUploadingFiles = props.uploadingFiles.filter(f => f.file !== file)
  emit('update:uploadingFiles', filteredUploadingFiles)
}

// 触发文件选择
function triggerFileSelect() {
  if (props.disabled || !canUploadMore.value)
    return
  fileInputRef.value?.click()
}

// 暴露方法给父组件
defineExpose({
  retryUpload,
  cancelUpload,
})
</script>

<template>
  <div>
    <!-- 上传区域 -->
    <div
      :class="cn(
        'border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center transition-colors cursor-pointer',
        'hover:border-primary/50 hover:bg-accent/50',
        props.disabled && 'opacity-50 cursor-not-allowed',
        !canUploadMore && 'opacity-50 cursor-not-allowed',
      )"
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
      @click="triggerFileSelect"
    >
      <input
        ref="fileInputRef"
        type="file"
        :accept="acceptString"
        :multiple="multiple"
        :disabled="disabled || !canUploadMore"
        class="hidden"
        @change="handleFileSelect"
      >

      <div class="flex flex-col items-center gap-2">
        <Upload class="h-8 w-8 text-muted-foreground" />
        <div class="text-sm text-primary font-medium">
          点击此区域上传文件或拖拽文件到此处
        </div>
        <div class="text-xs text-muted-foreground">
          支持 {{ accept.join(', ') }} 格式，单个文件最大 {{ maxSize }}MB
        </div>
        <div class="text-xs text-muted-foreground">
          已选择 {{ totalFiles }}/{{ maxFiles }} 个文件
        </div>
      </div>
    </div>
  </div>
</template>
