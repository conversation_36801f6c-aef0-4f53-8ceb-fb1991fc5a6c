<script setup lang="ts">
import type { AttachmentItem } from '~/components/ui/attachment-viewer'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { AlertCircle, CheckCircle, FileImage, FileVideo, Upload, X } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { useNotification } from '~/composables/useNotification'
import { requestClient } from '~/utils/request'

interface AttachmentUploaderProps {
  modelValue: AttachmentItem[]
  maxFiles?: number
  maxSize?: number // MB
  accept?: string[]
  disabled?: boolean
  multiple?: boolean
}

interface AttachmentUploaderEmits {
  (e: 'update:modelValue', value: AttachmentItem[]): void
  (e: 'uploadSuccess', attachment: AttachmentItem): void
  (e: 'uploadError', error: string): void
}

const props = withDefaults(defineProps<AttachmentUploaderProps>(), {
  maxFiles: 10,
  maxSize: 100, // 100MB
  accept: () => ['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov'],
  disabled: false,
  multiple: true,
})

const emit = defineEmits<AttachmentUploaderEmits>()

const notification = useNotification()

// 生成唯一ID
function generateUID(): string {
  return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// 上传文件API
async function uploadFile(params: { file: File, uid: string }) {
  const formData = new FormData()
  formData.append('file', params.file)
  formData.append('uid', params.uid)

  return requestClient.post('/feedback/upload/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 上传状态
interface UploadingFile {
  uid: string
  file: File
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
  attachment?: AttachmentItem
}

const uploadingFiles = ref<UploadingFile[]>([])
const fileInputRef = ref<HTMLInputElement>()

// 计算属性
const acceptString = computed(() => {
  return props.accept.map(ext => `.${ext}`).join(',')
})

const canUploadMore = computed(() => {
  return props.modelValue.length + uploadingFiles.value.length < props.maxFiles
})

// 文件验证
function validateFile(file: File): string | null {
  // 检查文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    return `文件大小不能超过 ${props.maxSize}MB`
  }

  // 检查文件类型
  const fileExt = file.name.split('.').pop()?.toLowerCase()
  if (!fileExt || !props.accept.includes(fileExt)) {
    return `不支持的文件类型，仅支持：${props.accept.join(', ')}`
  }

  return null
}

// 处理文件选择
function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files)
    return

  handleFiles(Array.from(files))

  // 清空input值，允许重复选择同一文件
  target.value = ''
}

// 处理拖拽上传
function handleDrop(event: DragEvent) {
  event.preventDefault()
  if (props.disabled)
    return

  const files = event.dataTransfer?.files
  if (!files)
    return

  handleFiles(Array.from(files))
}

// 处理文件列表
function handleFiles(files: File[]) {
  if (!canUploadMore.value) {
    notification.error(`最多只能上传 ${props.maxFiles} 个文件`)
    return
  }

  const validFiles: File[] = []

  for (const file of files) {
    // 检查是否超出数量限制
    if (props.modelValue.length + uploadingFiles.value.length + validFiles.length >= props.maxFiles) {
      notification.warning(`已达到最大文件数量限制 (${props.maxFiles})`)
      break
    }

    // 验证文件
    const error = validateFile(file)
    if (error) {
      notification.error(`${file.name}: ${error}`)
      continue
    }

    validFiles.push(file)
  }

  // 开始上传有效文件
  validFiles.forEach(uploadSingleFile)
}

// 上传单个文件
async function uploadSingleFile(file: File) {
  const uid = generateUID()

  const uploadingFile: UploadingFile = {
    uid,
    file,
    progress: 0,
    status: 'uploading',
  }

  uploadingFiles.value.push(uploadingFile)

  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadingFile.progress < 90) {
        uploadingFile.progress += Math.random() * 20
      }
    }, 200)

    // 调用上传API
    const response = await uploadFile({
      file,
      uid,
    })

    clearInterval(progressInterval)
    uploadingFile.progress = 100

    // 检查响应是否成功 - API直接返回数据对象，不是包装的响应
    if (response && response.uid && response.url) {
      // 上传成功
      uploadingFile.status = 'success'

      const attachment: AttachmentItem = {
        id: response.uid,
        name: response.name,
        url: response.url,
        type: response.type,
        size: file.size, // 使用原文件大小
      }

      uploadingFile.attachment = attachment

      // 添加到附件列表
      const newAttachments = [...props.modelValue, attachment]
      emit('update:modelValue', newAttachments)
      emit('uploadSuccess', attachment)

      // 延迟移除上传状态
      setTimeout(() => {
        const index = uploadingFiles.value.findIndex(f => f.uid === uid)
        if (index > -1) {
          uploadingFiles.value.splice(index, 1)
        }
      }, 1000)
    }
    else {
      console.error('上传失败，响应数据不完整:', response)
      throw new Error('上传失败：响应数据不完整')
    }
  }
  catch (error) {
    console.error('上传过程中发生错误:', error)
    uploadingFile.status = 'error'
    uploadingFile.error = error instanceof Error ? error.message : '上传失败'
    emit('uploadError', uploadingFile.error)
    notification.error(`${file.name} 上传失败: ${uploadingFile.error}`)
  }
}

// 移除附件
function removeAttachment(attachment: AttachmentItem) {
  const newAttachments = props.modelValue.filter(item => item.id !== attachment.id)
  emit('update:modelValue', newAttachments)
}

// 重试上传
function retryUpload(uploadingFile: UploadingFile) {
  uploadingFile.status = 'uploading'
  uploadingFile.progress = 0
  uploadingFile.error = undefined
  uploadSingleFile(uploadingFile.file)
}

// 取消上传
function cancelUpload(uploadingFile: UploadingFile) {
  const index = uploadingFiles.value.findIndex(f => f.uid === uploadingFile.uid)
  if (index > -1) {
    uploadingFiles.value.splice(index, 1)
  }
}

// 触发文件选择
function triggerFileSelect() {
  if (!props.disabled && canUploadMore.value) {
    fileInputRef.value?.click()
  }
}

// 获取文件图标
function getFileIcon(fileName: string) {
  const ext = fileName.split('.').pop()?.toLowerCase()
  if (['png', 'jpeg', 'jpg'].includes(ext || '')) {
    return FileImage
  }
  if (['mp4', 'rmvb', 'mov'].includes(ext || '')) {
    return FileVideo
  }
  return FileImage
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0)
    return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}
</script>

<template>
  <div class="space-y-4">
    <!-- 上传区域 -->
    <div
      :class="cn(
        'border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center transition-colors cursor-pointer',
        'hover:border-primary/50 hover:bg-accent/50',
        props.disabled && 'opacity-50 cursor-not-allowed',
        !canUploadMore && 'opacity-50 cursor-not-allowed',
      )"
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
      @click="triggerFileSelect"
    >
      <input
        ref="fileInputRef"
        type="file"
        :accept="acceptString"
        :multiple="multiple"
        :disabled="disabled || !canUploadMore"
        class="hidden"
        @change="handleFileSelect"
      >

      <div class="flex flex-col items-center gap-2">
        <Upload class="h-8 w-8 text-muted-foreground" />
        <div class="text-sm text-primary font-medium">
          点击此区域上传文件或拖拽文件到此处
        </div>
        <div class="text-xs text-muted-foreground">
          支持 {{ accept.join(', ') }} 格式，单个文件最大 {{ maxSize }}MB
        </div>
        <div class="text-xs text-muted-foreground">
          已上传 {{ modelValue.length }}/{{ maxFiles }} 个文件
        </div>
      </div>
    </div>

    <!-- 上传中的文件 -->
    <div v-if="uploadingFiles.length > 0" class="space-y-2">
      <div class="text-sm font-medium">
        上传中
      </div>
      <div
        v-for="uploadingFile in uploadingFiles"
        :key="uploadingFile.uid"
        class="flex items-center gap-3 border rounded-lg p-3"
      >
        <component :is="getFileIcon(uploadingFile.file.name)" class="h-5 w-5 text-muted-foreground" />

        <div class="min-w-0 flex-1">
          <div class="truncate text-sm font-medium">
            {{ uploadingFile.file.name }}
          </div>
          <div class="text-xs text-muted-foreground">
            {{ formatFileSize(uploadingFile.file.size) }}
          </div>

          <!-- 进度条 -->
          <div v-if="uploadingFile.status === 'uploading'" class="mt-1">
            <Progress :value="uploadingFile.progress" class="h-1" />
          </div>

          <!-- 错误信息 -->
          <div v-if="uploadingFile.status === 'error'" class="mt-1 text-xs text-destructive">
            {{ uploadingFile.error }}
          </div>
        </div>

        <!-- 状态图标和操作 -->
        <div class="flex items-center gap-2">
          <CheckCircle v-if="uploadingFile.status === 'success'" class="h-4 w-4 text-green-500" />
          <AlertCircle v-else-if="uploadingFile.status === 'error'" class="h-4 w-4 text-destructive" />

          <Button
            v-if="uploadingFile.status === 'error'"
            variant="ghost"
            size="sm"
            @click="retryUpload(uploadingFile)"
          >
            重试
          </Button>

          <Button
            variant="ghost"
            size="sm"
            @click="cancelUpload(uploadingFile)"
          >
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 已上传的文件列表 -->
    <div v-if="modelValue.length > 0" class="space-y-2">
      <div class="text-sm font-medium">
        已上传的文件
      </div>
      <div
        v-for="attachment in modelValue"
        :key="attachment.id"
        class="flex items-center gap-3 border rounded-lg bg-muted/20 p-3"
      >
        <component :is="getFileIcon(attachment.name)" class="h-5 w-5 text-muted-foreground" />

        <div class="min-w-0 flex-1">
          <div class="truncate text-sm font-medium">
            {{ attachment.name }}
          </div>
          <div class="text-xs text-muted-foreground">
            {{ attachment.size ? formatFileSize(attachment.size) : '未知大小' }}
          </div>
        </div>

        <Badge variant="secondary" class="text-xs">
          {{ attachment.type.toUpperCase() }}
        </Badge>

        <Button
          variant="ghost"
          size="sm"
          @click="removeAttachment(attachment)"
        >
          <X class="h-4 w-4" />
        </Button>
      </div>
    </div>
  </div>
</template>
