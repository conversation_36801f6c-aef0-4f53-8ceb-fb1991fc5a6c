export { default as AttachmentUploader } from './AttachmentUploader.vue'
export { default as AttachmentUploaderOptimized } from './AttachmentUploaderOptimized.vue'
export { default as AttachmentUploaderSimple } from './AttachmentUploaderSimple.vue'

// 重新导出AttachmentItem类型
export type { AttachmentItem } from '~/components/ui/attachment-viewer'

export interface AttachmentUploaderProps {
  modelValue: AttachmentItem[]
  maxFiles?: number
  maxSize?: number // MB
  accept?: string[]
  disabled?: boolean
  multiple?: boolean
}

export interface AttachmentUploaderEmits {
  (e: 'update:modelValue', value: AttachmentItem[]): void
  (e: 'uploadSuccess', attachment: AttachmentItem): void
  (e: 'uploadError', error: string): void
}
