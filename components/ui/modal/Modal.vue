<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-vue-next'
import { computed } from 'vue'
import AssignTicketForm from '~/components/feedback/AssignTicketForm.vue'
import CompleteTicketForm from '~/components/feedback/CompleteTicketForm.vue'
import CreateTicketForm from '~/components/feedback/CreateTicketForm.vue'
import { useModal } from '~/composables/useModal'

const modal = useModal()

// 组件映射
const componentMap = {
  CreateTicketForm,
  CompleteTicketForm,
  AssignTicketForm,
}

// 计算属性
const isOpen = computed({
  get: () => modal.modalState.value.isOpen,
  set: (value) => {
    if (!value) {
      // 当弹窗被关闭时，调用解析函数返回 false
      const resolve = modal.getModalResolve()
      if (resolve) {
        resolve(false)
        modal.setModalResolve(null)
      }
      modal.closeModal()
    }
  },
})

const isLoading = computed(() => modal.modalState.value.isLoading)
const modalOptions = computed(() => modal.modalState.value.options)

// 自定义组件相关
const customComponent = computed(() => {
  const componentName = modalOptions.value?.component
  if (componentName && componentMap[componentName as keyof typeof componentMap]) {
    return componentMap[componentName as keyof typeof componentMap]
  }
  return null
})

const isCustomComponent = computed(() => !!customComponent.value)

// 获取图标配置
const iconConfig = computed(() => {
  const type = modalOptions.value?.type || 'info'
  return modal.getModalIcon(type)
})

// 事件处理
async function handleConfirm() {
  if (isLoading.value)
    return

  try {
    if (modalOptions.value?.onConfirm) {
      await modalOptions.value.onConfirm()
    }

    // 使用解析函数返回结果
    const resolve = modal.getModalResolve()
    if (resolve) {
      resolve(true)
      modal.setModalResolve(null)
    }

    modal.closeModal()
  }
  catch (error) {
    console.error('弹窗确认操作失败:', error)
  }
}

function handleCancel() {
  if (modalOptions.value?.onCancel) {
    modalOptions.value.onCancel()
  }

  // 使用解析函数返回结果
  const resolve = modal.getModalResolve()
  if (resolve) {
    resolve(false)
    modal.setModalResolve(null)
  }

  modal.closeModal()
}

function handleOutsideClick(event: Event) {
  // 如果是持久化弹窗或不可关闭，阻止外部点击关闭
  if (modalOptions.value?.persistent || modalOptions.value?.closable === false) {
    event.preventDefault()
    return
  }

  // 外部点击关闭时，调用解析函数返回 false
  const resolve = modal.getModalResolve()
  if (resolve) {
    resolve(false)
    modal.setModalResolve(null)
  }
}

function handleEscapeKey(event: Event) {
  // 如果是持久化弹窗或不可关闭，阻止 ESC 键关闭
  if (modalOptions.value?.persistent || modalOptions.value?.closable === false) {
    event.preventDefault()
    return
  }

  // ESC 键关闭时，调用解析函数返回 false
  const resolve = modal.getModalResolve()
  if (resolve) {
    resolve(false)
    modal.setModalResolve(null)
  }
}
</script>

<template>
  <!-- 自定义组件模式 -->
  <component
    :is="customComponent"
    v-if="customComponent"
    v-bind="modalOptions?.props || {}"
    v-on="modalOptions?.events || {}"
  />

  <!-- 标准模态框模式 -->
  <Dialog v-else v-model:open="isOpen" :modal="true">
    <DialogContent
      :class="cn('sm:max-w-md [&>button]:hidden', modalOptions?.customClass)"
      @pointer-down-outside="handleOutsideClick"
      @escape-key-down="handleEscapeKey"
    >
      <DialogHeader>
        <div class="flex flex-col items-center text-center space-y-4">
          <!-- 图标 -->
          <div :class="cn('w-16 h-16 rounded-full flex items-center justify-center', iconConfig.bgClass)">
            <component :is="iconConfig.icon" :class="cn('w-8 h-8', iconConfig.iconClass)" />
          </div>

          <!-- 标题和描述 -->
          <div class="space-y-2">
            <DialogTitle class="text-lg font-semibold">
              {{ modalOptions?.title || '提示' }}
            </DialogTitle>
            <DialogDescription class="text-sm text-muted-foreground">
              {{ modalOptions?.message || '' }}
            </DialogDescription>
          </div>
        </div>
      </DialogHeader>

      <!-- 自定义内容 -->
      <div v-if="modalOptions?.customContent" class="py-4">
        <component :is="modalOptions.customContent" />
      </div>

      <DialogFooter class="flex-col gap-2 sm:flex-row">
        <Button
          v-if="modalOptions?.showCancel"
          variant="outline"
          class="w-full sm:w-auto"
          :disabled="isLoading"
          @click="handleCancel"
        >
          {{ modalOptions?.cancelText || '取消' }}
        </Button>
        <Button
          v-if="modalOptions?.showConfirm"
          :disabled="isLoading"
          class="w-full sm:w-auto"
          @click="handleConfirm"
        >
          <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
          {{ modalOptions?.confirmText || '确定' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
