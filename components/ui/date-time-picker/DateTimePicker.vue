<script setup lang="ts">
import { cn } from '@/lib/utils'
import VueDatePicker from '@vuepic/vue-datepicker'
import { Calendar as CalendarIcon, X } from 'lucide-vue-next'
import '@vuepic/vue-datepicker/dist/main.css'

interface DateTimePickerProps {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
}

interface DateTimePickerEmits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<DateTimePickerProps>(), {
  placeholder: '选择日期和时间',
  disabled: false,
  required: false,
})

const emit = defineEmits<DateTimePickerEmits>()

// 内部日期值
const internalValue = ref<Date | null>(null)

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    try {
      // 解析 datetime-local 格式的字符串 (YYYY-MM-DDTHH:mm)
      const date = new Date(newValue)
      if (!Number.isNaN(date.getTime())) {
        internalValue.value = date
      }
    }
    catch (error) {
      console.error('Error parsing datetime:', error)
      internalValue.value = null
    }
  }
  else {
    internalValue.value = null
  }
}, { immediate: true })

// 处理日期变化
function handleDateChange(date: Date | null) {
  internalValue.value = date

  if (date) {
    // 转换为 datetime-local 格式 (YYYY-MM-DDTHH:mm)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')

    const datetimeLocal = `${year}-${month}-${day}T${hours}:${minutes}`
    emit('update:modelValue', datetimeLocal)
  }
  else {
    emit('update:modelValue', '')
  }
}

// 预设范围选项 - 对于单日期选择器，使用单个日期而不是范围
const presetRanges = ref([
  {
    label: '此刻',
    range: new Date(),
  },
  {
    label: '1小时后',
    range: new Date(Date.now() + 60 * 60 * 1000),
  },
  {
    label: '明天此时',
    range: new Date(Date.now() + 24 * 60 * 60 * 1000),
  },
  {
    label: '下周此时',
    range: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
  },
])

// 格式化显示文本
const displayText = computed(() => {
  if (internalValue.value) {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(internalValue.value)
  }
  return props.placeholder
})
</script>

<template>
  <VueDatePicker
    :model-value="internalValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :enable-time-picker="true"
    format="yyyy-MM-dd HH:mm"
    preview-format="yyyy-MM-dd HH:mm"
    :text-input="true"
    :auto-apply="false"
    :clearable="true"
    :close-on-scroll="false"
    :close-on-auto-apply="false"
    locale="zh-CN"
    :preset-ranges="presetRanges"
    :day-names="['日', '一', '二', '三', '四', '五', '六']"
    :month-names="['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']"
    @update:model-value="handleDateChange"
    @cleared="() => handleDateChange(null)"
  >
    <template #dp-input="{ value, onInput, onEnter, onTab, onBlur, onFocus, onKeypress, isMenuOpen, toggleMenu }">
      <Button
        variant="outline"
        :disabled="disabled"
        :class="cn(
          'w-full justify-start text-left font-normal relative',
          !value && 'text-muted-foreground',
          isMenuOpen && 'ring-2 ring-ring ring-offset-2',
        )"
        @click="toggleMenu"
        @focus="onFocus"
      >
        <CalendarIcon class="mr-2 h-4 w-4 shrink-0" />
        <input
          :value="value || ''"
          :placeholder="placeholder"
          :disabled="disabled"
          class="flex-1 border-0 bg-transparent text-sm outline-none placeholder:text-muted-foreground"
          @input="onInput"
          @keydown.enter="onEnter"
          @keydown.tab="onTab"
          @blur="onBlur"
          @keypress="onKeypress"
        >
      </Button>
    </template>
  </VueDatePicker>
</template>

<style>
/* 重置 VueDatePicker 样式以完全匹配 shadcn-ui */
:global(.dp__theme_light),
:global(.dp__theme_dark) {
  /* 基础颜色 */
  --dp-background-color: hsl(var(--background));
  --dp-text-color: hsl(var(--foreground));
  --dp-hover-color: hsl(var(--accent));
  --dp-hover-text-color: hsl(var(--accent-foreground));
  --dp-hover-icon-color: hsl(var(--accent-foreground));
  --dp-primary-color: hsl(var(--primary));
  --dp-primary-text-color: hsl(var(--primary-foreground));
  --dp-secondary-color: hsl(var(--secondary));

  /* 边框颜色 */
  --dp-border-color: hsl(var(--border));
  --dp-menu-border-color: hsl(var(--border));
  --dp-border-color-hover: hsl(var(--border));

  /* 状态颜色 */
  --dp-disabled-color: hsl(var(--muted));
  --dp-disabled-color-text: hsl(var(--muted-foreground));
  --dp-success-color: hsl(var(--primary));
  --dp-success-color-disabled: hsl(var(--muted));
  --dp-icon-color: hsl(var(--muted-foreground));
  --dp-danger-color: hsl(var(--destructive));
  --dp-highlight-color: hsl(var(--accent));

  /* 滚动条 */
  --dp-scroll-bar-background: hsl(var(--muted));
  --dp-scroll-bar-color: hsl(var(--muted-foreground));

  /* 范围选择 */
  --dp-range-between-dates-background-color: hsl(var(--accent) / 0.1);
  --dp-range-between-dates-text-color: hsl(var(--accent-foreground));

  /* 字体 */
  --dp-font-family: inherit;
  --dp-border-radius: calc(var(--radius) - 2px);
  --dp-cell-border-radius: calc(var(--radius) - 2px);
}

/* 弹出层样式 - 完全匹配 shadcn-ui Popover */
:global(.dp__menu) {
  z-index: 50;
  min-width: 8rem;
  overflow: hidden;
  border-radius: calc(var(--radius));
  border: 1px solid hsl(var(--border));
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.25rem;
  animation: dp-menu-appear 0.15s ease-out;
}

/* 调整输入框样式 */
:global(.dp__input_wrap) {
  display: none; /* 隐藏默认输入框，使用自定义触发器 */
}

/* 日历样式 - 完全匹配 shadcn-ui Calendar */
:global(.dp__calendar) {
  font-family: inherit;
  padding: 0.75rem;
}

/* 日历头部 */
:global(.dp__calendar_header) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 0.75rem;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid hsl(var(--border));
}

:global(.dp__calendar_header_item) {
  color: hsl(var(--muted-foreground));
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 日历单元格 */
:global(.dp__cell_inner) {
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  font-weight: 400;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  position: relative;
}

:global(.dp__cell_inner:hover) {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* 选中的日期 */
:global(.dp__active_date) {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 500;
}

/* 今天 */
:global(.dp__today) {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  font-weight: 500;
}

/* 今天且被选中 */
:global(.dp__today.dp__active_date) {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* 时间选择器样式 - 匹配 shadcn-ui Input */
:global(.dp__time_picker) {
  padding: 0.75rem;
  border-left: 1px solid hsl(var(--border));
  background-color: hsl(var(--popover));
}

:global(.dp__time_input) {
  display: flex;
  height: 2.25rem;
  width: 100%;
  border-radius: calc(var(--radius) - 2px);
  border: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: hsl(var(--foreground));
  transition: colors 0.15s ease-in-out;
}

:global(.dp__time_input:focus) {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

:global(.dp__time_input::placeholder) {
  color: hsl(var(--muted-foreground));
}

:global(.dp__time_input:disabled) {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 操作按钮样式 - 完全匹配 shadcn-ui Button */
:global(.dp__action_buttons) {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem;
  border-top: 1px solid hsl(var(--border));
  background-color: hsl(var(--popover));
}

:global(.dp__action_button) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  font-weight: 500;
  height: 2.25rem;
  padding: 0 0.75rem;
  transition: colors 0.15s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;
}

/* 取消按钮 - outline variant */
:global(.dp__action_cancel) {
  border: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

:global(.dp__action_cancel:hover) {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* 确认按钮 - default variant */
:global(.dp__action_select) {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: 1px solid hsl(var(--primary));
}

:global(.dp__action_select:hover) {
  background-color: hsl(var(--primary) / 0.9);
}

:global(.dp__action_button:focus-visible) {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

:global(.dp__action_button:disabled) {
  pointer-events: none;
  opacity: 0.5;
}

/* 预设范围样式 - 匹配 shadcn-ui 侧边栏样式 */
:global(.dp__preset_ranges) {
  min-width: 8rem;
  padding: 0.75rem;
  border-right: 1px solid hsl(var(--border));
  background-color: hsl(var(--popover));
}

:global(.dp__preset_range) {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.25rem;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  font-weight: 400;
  color: hsl(var(--foreground));
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-align: left;
}

:global(.dp__preset_range:hover) {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

:global(.dp__preset_range:focus-visible) {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

:global(.dp__preset_range:last-child) {
  margin-bottom: 0;
}

/* 月份/年份选择器样式 - 匹配 shadcn-ui 样式 */
:global(.dp__overlay) {
  background-color: hsl(var(--popover));
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius));
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
}

:global(.dp__overlay_cell) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.25rem;
  height: 2.25rem;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  font-weight: 400;
  cursor: pointer;
  transition: colors 0.15s ease-in-out;
}

:global(.dp__overlay_cell:hover) {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

:global(.dp__overlay_cell_active) {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 500;
}

/* 禁用状态样式 */
:global(.dp__cell_disabled) {
  color: hsl(var(--muted-foreground)) !important;
  background-color: transparent !important;
  cursor: not-allowed !important;
  opacity: 0.5;
}

:global(.dp__cell_disabled:hover) {
  background-color: transparent !important;
  color: hsl(var(--muted-foreground)) !important;
}

/* 动画效果 - 匹配 shadcn-ui 动画 */
@keyframes dp-menu-appear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 导航按钮样式 - 匹配 shadcn-ui Button ghost variant */
:global(.dp__arrow_top),
:global(.dp__arrow_bottom),
:global(.dp__inner_nav) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875rem;
  font-weight: 500;
  height: 2.25rem;
  width: 2.25rem;
  padding: 0;
  background-color: transparent;
  color: hsl(var(--foreground));
  border: 1px solid transparent;
  cursor: pointer;
  transition: colors 0.15s ease-in-out;
}

:global(.dp__arrow_top:hover),
:global(.dp__arrow_bottom:hover),
:global(.dp__inner_nav:hover) {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

:global(.dp__arrow_top:focus-visible),
:global(.dp__arrow_bottom:focus-visible),
:global(.dp__inner_nav:focus-visible) {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* 响应式优化 */
@media (max-width: 640px) {
  :global(.dp__menu) {
    max-width: 90vw;
    margin: 0 auto;
  }

  :global(.dp__calendar) {
    font-size: 0.875rem;
  }

  :global(.dp__time_picker) {
    border-left: none;
    border-top: 1px solid hsl(var(--border));
    padding-left: 0;
    padding-top: 1rem;
    margin-top: 1rem;
  }
}
</style>
