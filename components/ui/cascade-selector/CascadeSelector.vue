<script setup lang="ts">
import { ChevronRight, Search } from 'lucide-vue-next'

interface CascadeItem {
  id: string | number
  name: string
  children?: CascadeItem[]
  level?: number
}

interface CascadeSelectorProps {
  data: CascadeItem[]
  modelValue?: string[]
  placeholder?: string
  searchPlaceholder?: string
  maxLevel?: number
  loading?: boolean
}

interface CascadeSelectorEmits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[], selectedItems: CascadeItem[]): void
}

const props = withDefaults(defineProps<CascadeSelectorProps>(), {
  placeholder: '请选择',
  searchPlaceholder: '搜索功能',
  maxLevel: 3,
  loading: false,
})

const emit = defineEmits<CascadeSelectorEmits>()

// 状态管理
const isOpen = ref(false)
const searchKeyword = ref('')
const selectedPath = ref<CascadeItem[]>([])
const currentLevel = ref(0)

// 计算属性
const displayText = computed(() => {
  if (selectedPath.value.length === 0) {
    return props.placeholder
  }
  return selectedPath.value.map(item => item.name).join(' / ')
})

// 获取当前级别的数据
const currentLevelData = computed(() => {
  if (currentLevel.value === 0) {
    return props.data
  }
  
  const parent = selectedPath.value[currentLevel.value - 1]
  return parent?.children || []
})

// 搜索过滤后的数据
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return currentLevelData.value
  }
  
  return currentLevelData.value.filter(item => 
    item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 面包屑导航数据
const breadcrumbs = computed(() => {
  const crumbs = [{ name: '全部', level: 0 }]
  selectedPath.value.forEach((item, index) => {
    if (index < currentLevel.value) {
      crumbs.push({ name: item.name, level: index + 1 })
    }
  })
  return crumbs
})

// 方法
function handleItemClick(item: CascadeItem) {
  const newPath = [...selectedPath.value.slice(0, currentLevel.value), item]
  selectedPath.value = newPath
  
  if (item.children && item.children.length > 0 && currentLevel.value < props.maxLevel - 1) {
    // 有子级且未达到最大层级，进入下一级
    currentLevel.value++
    searchKeyword.value = ''
  } else {
    // 没有子级或达到最大层级，完成选择
    const selectedValues = newPath.map(pathItem => pathItem.name)
    emit('update:modelValue', selectedValues)
    emit('change', selectedValues, newPath)
    isOpen.value = false
  }
}

function handleBreadcrumbClick(level: number) {
  currentLevel.value = level
  searchKeyword.value = ''
}

function handleClear() {
  selectedPath.value = []
  currentLevel.value = 0
  searchKeyword.value = ''
  emit('update:modelValue', [])
  emit('change', [], [])
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (!newValue || newValue.length === 0) {
    selectedPath.value = []
    currentLevel.value = 0
    return
  }
  
  // 根据值重建选择路径
  // 这里需要根据实际数据结构来实现
}, { immediate: true })

// 点击外部关闭
function handleClickOutside() {
  isOpen.value = false
  currentLevel.value = selectedPath.value.length
  searchKeyword.value = ''
}
</script>

<template>
  <div class="relative">
    <!-- 触发器 -->
    <div
      class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer"
      @click="isOpen = !isOpen"
    >
      <span :class="selectedPath.length === 0 ? 'text-muted-foreground' : ''">
        {{ displayText }}
      </span>
      <ChevronRight 
        :class="['h-4 w-4 transition-transform', isOpen ? 'rotate-90' : '']" 
      />
    </div>

    <!-- 下拉面板 -->
    <div
      v-if="isOpen"
      class="absolute top-full left-0 z-50 mt-1 w-full min-w-[400px] rounded-md border bg-popover p-0 text-popover-foreground shadow-md"
      @click.stop
    >
      <!-- 搜索框 -->
      <div class="flex items-center border-b px-3 py-2">
        <Search class="mr-2 h-4 w-4 shrink-0 opacity-50" />
        <input
          v-model="searchKeyword"
          :placeholder="searchPlaceholder"
          class="flex-1 bg-transparent text-sm outline-none placeholder:text-muted-foreground"
        />
      </div>

      <!-- 面包屑导航 -->
      <div v-if="breadcrumbs.length > 1" class="flex items-center gap-1 border-b px-3 py-2 text-xs text-muted-foreground">
        <button
          v-for="(crumb, index) in breadcrumbs"
          :key="crumb.level"
          class="hover:text-foreground transition-colors"
          @click="handleBreadcrumbClick(crumb.level)"
        >
          {{ crumb.name }}
          <ChevronRight v-if="index < breadcrumbs.length - 1" class="inline h-3 w-3 mx-1" />
        </button>
      </div>

      <!-- 选项列表 -->
      <div class="max-h-60 overflow-y-auto">
        <div v-if="props.loading" class="flex items-center justify-center py-6">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
        
        <div v-else-if="filteredData.length === 0" class="py-6 text-center text-sm text-muted-foreground">
          暂无数据
        </div>
        
        <div v-else>
          <button
            v-for="item in filteredData"
            :key="item.id"
            class="flex w-full items-center justify-between px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground transition-colors"
            @click="handleItemClick(item)"
          >
            <span>{{ item.name }}</span>
            <ChevronRight 
              v-if="item.children && item.children.length > 0" 
              class="h-4 w-4 opacity-50" 
            />
          </button>
        </div>
      </div>

      <!-- 底部操作 -->
      <div v-if="selectedPath.length > 0" class="border-t px-3 py-2">
        <button
          class="text-xs text-muted-foreground hover:text-foreground transition-colors"
          @click="handleClear"
        >
          清空选择
        </button>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-40"
      @click="handleClickOutside"
    />
  </div>
</template>
