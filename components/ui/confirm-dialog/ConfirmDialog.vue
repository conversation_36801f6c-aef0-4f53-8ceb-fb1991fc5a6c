<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-vue-next'
import { computed } from 'vue'

interface Props {
  open: boolean
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'info' | 'warning' | 'error' | 'success'
  persistent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  confirmText: '确认',
  cancelText: '取消',
  type: 'info',
  persistent: false,
})

const emit = defineEmits<{
  'confirm': []
  'cancel': []
  'update:open': [value: boolean]
}>()

// 根据类型获取图标和样式
const iconAndStyle = computed(() => {
  switch (props.type) {
    case 'success':
      return {
        icon: CheckCircle,
        iconClass: 'text-green-600',
      }
    case 'error':
      return {
        icon: XCircle,
        iconClass: 'text-red-600',
      }
    case 'warning':
      return {
        icon: AlertTriangle,
        iconClass: 'text-yellow-600',
      }
    case 'info':
    default:
      return {
        icon: Info,
        iconClass: 'text-blue-600',
      }
  }
})

function handleConfirm() {
  emit('confirm')
  emit('update:open', false)
}

function handleCancel() {
  emit('cancel')
  emit('update:open', false)
}

function handleOpenChange(open: boolean) {
  if (!open && !props.persistent) {
    emit('cancel')
  }
  emit('update:open', open)
}
</script>

<template>
  <Dialog :open="open" @update:open="handleOpenChange">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-3">
          <component
            :is="iconAndStyle.icon"
            class="h-6 w-6"
            :class="iconAndStyle.iconClass"
          />
          {{ title }}
        </DialogTitle>
      </DialogHeader>

      <div class="py-4">
        <p class="text-sm text-muted-foreground">
          {{ message }}
        </p>
      </div>

      <DialogFooter class="flex gap-2 sm:gap-2">
        <Button
          variant="outline"
          @click="handleCancel"
        >
          {{ cancelText }}
        </Button>
        <Button
          @click="handleConfirm"
        >
          {{ confirmText }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
