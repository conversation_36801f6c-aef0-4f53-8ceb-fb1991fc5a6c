<script setup lang="ts">
import { ChevronDown, ChevronRight, Search } from 'lucide-vue-next'

interface CascadeItem {
  id: string | number
  name: string
  children?: CascadeItem[]
}

interface CascadeDropdownProps {
  data: CascadeItem[]
  modelValue?: string[]
  placeholder?: string
  searchPlaceholder?: string
  loading?: boolean
  disabled?: boolean
}

interface CascadeDropdownEmits {
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[], selectedItems: CascadeItem[]): void
}

const props = withDefaults(defineProps<CascadeDropdownProps>(), {
  placeholder: '请选择功能类型',
  searchPlaceholder: '搜索功能',
  loading: false,
  disabled: false,
})

const emit = defineEmits<CascadeDropdownEmits>()

// 状态管理
const isOpen = ref(false)
const searchKeyword = ref('')
const selectedPath = ref<CascadeItem[]>([])

// 计算属性
const displayText = computed(() => {
  if (selectedPath.value.length === 0) {
    return props.placeholder
  }
  return selectedPath.value.map(item => item.name).join(' / ')
})

// 搜索过滤数据
const filteredData = computed(() => {
  if (!searchKeyword.value) {
    return props.data
  }

  return filterItemsRecursively(props.data, searchKeyword.value.toLowerCase())
})

// 递归过滤函数
function filterItemsRecursively(items: CascadeItem[], keyword: string): CascadeItem[] {
  return items.filter((item) => {
    const nameMatches = item.name.toLowerCase().includes(keyword)
    const hasMatchingChildren = item.children && filterItemsRecursively(item.children, keyword).length > 0

    return nameMatches || hasMatchingChildren
  }).map(item => ({
    ...item,
    children: item.children ? filterItemsRecursively(item.children, keyword) : undefined,
  }))
}

// 处理选择
function handleSelect(item: CascadeItem, currentPath: CascadeItem[] = []) {
  const newPath = [...currentPath, item]
  selectedPath.value = newPath
  const selectedValues = newPath.map(pathItem => pathItem.name)
  emit('update:modelValue', selectedValues)
  emit('change', selectedValues, newPath)
  isOpen.value = false
}

// 清空选择
function handleClear() {
  selectedPath.value = []
  emit('update:modelValue', [])
  emit('change', [], [])
}
</script>

<template>
  <DropdownMenu v-model:open="isOpen">
    <DropdownMenuTrigger as-child>
      <Button
        variant="outline"
        role="combobox"
        :aria-expanded="isOpen"
        :disabled="disabled"
        class="w-full justify-between text-left font-normal"
        :class="selectedPath.length === 0 ? 'text-muted-foreground' : ''"
      >
        <span class="truncate">{{ displayText }}</span>
        <ChevronDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent class="w-[280px] p-0" align="start">
      <!-- 搜索框 -->
      <div class="border-b border-border/20 p-2">
        <div class="relative">
          <Search class="absolute left-2 top-2 h-4 w-4 text-muted-foreground" />
          <Input
            v-model="searchKeyword"
            :placeholder="searchPlaceholder"
            class="h-8 bg-muted/30 pl-8 text-sm"
            @keydown.stop
          />
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-6">
        <div class="h-6 w-6 animate-spin border-b-2 border-primary rounded-full" />
      </div>

      <!-- 级联选项 -->
      <ScrollArea v-else-if="filteredData.length > 0" class="h-[400px]">
        <DropdownMenuGroup>
          <template v-for="item in filteredData" :key="item.id">
            <!-- 有子项的菜单 -->
            <DropdownMenuSub v-if="item.children && item.children.length > 0">
              <DropdownMenuSubTrigger class="flex cursor-pointer items-center justify-between px-3 py-2 text-sm hover:bg-accent">
                <span>{{ item.name }}</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent class="w-[280px] p-0">
                <ScrollArea class="h-[300px]">
                  <!-- 递归渲染子菜单 -->
                  <template v-for="child in item.children" :key="child.id">
                    <!-- 二级有子项 -->
                    <DropdownMenuSub v-if="child.children && child.children.length > 0">
                      <DropdownMenuSubTrigger class="flex cursor-pointer items-center justify-between px-3 py-2 text-sm hover:bg-accent">
                        <span>{{ child.name }}</span>
                      </DropdownMenuSubTrigger>
                      <DropdownMenuSubContent class="w-[280px] p-0">
                        <ScrollArea class="h-[300px]">
                          <!-- 三级选项 -->
                          <DropdownMenuItem
                            v-for="grandChild in child.children"
                            :key="grandChild.id"
                            class="cursor-pointer px-3 py-2 text-sm"
                            @click="handleSelect(grandChild, [item, child])"
                          >
                            {{ grandChild.name }}
                          </DropdownMenuItem>
                        </ScrollArea>
                      </DropdownMenuSubContent>
                    </DropdownMenuSub>

                    <!-- 二级没有子项 -->
                    <DropdownMenuItem
                      v-else
                      class="cursor-pointer px-3 py-2 text-sm"
                      @click="handleSelect(child, [item])"
                    >
                      {{ child.name }}
                    </DropdownMenuItem>
                  </template>
                </ScrollArea>
              </DropdownMenuSubContent>
            </DropdownMenuSub>

            <!-- 没有子项的最终选项 -->
            <DropdownMenuItem
              v-else
              class="cursor-pointer px-3 py-2 text-sm"
              @click="handleSelect(item, [])"
            >
              {{ item.name }}
            </DropdownMenuItem>
          </template>
        </DropdownMenuGroup>
      </ScrollArea>

      <!-- 无数据 -->
      <div v-else class="flex items-center justify-center py-6 text-sm text-muted-foreground">
        {{ searchKeyword ? '没有找到匹配的选项' : '暂无数据' }}
      </div>

      <!-- 底部操作 -->
      <div v-if="selectedPath.length > 0" class="border-t border-border/20 p-2">
        <Button
          variant="ghost"
          size="sm"
          class="w-full text-xs text-muted-foreground hover:text-foreground"
          @click="handleClear"
        >
          清空选择
        </Button>
      </div>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
