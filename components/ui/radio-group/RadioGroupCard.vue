<script setup lang="ts">
import type { RadioGroupItemProps } from 'radix-vue'
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import {
  RadioGroupIndicator,
  RadioGroupItem,
  useForwardProps,
} from 'radix-vue'
import { computed, inject, ref } from 'vue'

interface RadioGroupCardProps extends RadioGroupItemProps {
  class?: HTMLAttributes['class']
  label?: string
  description?: string
  icon?: string
}

const props = defineProps<RadioGroupCardProps>()

const delegatedProps = computed(() => {
  const { class: _, label: __, description: ___, icon: ____, ...delegated } = props
  return delegated
})

const forwardedProps = useForwardProps(delegatedProps)

// 使用 props.checked 来判断是否选中
const isChecked = computed(() => props.checked)
</script>

<template>
  <RadioGroupItem
    v-bind="forwardedProps"
    :class="cn('block min-w-0 flex cursor-pointer items-center gap-3 border border-border/60 rounded-lg p-3 transition-all hover:border-primary/60 hover:bg-primary/5', {
      'border-primary bg-primary/10': isChecked,
    }, props.class)"
  >
    <RadioGroupIndicator class="sr-only" />

    <!-- 自定义单选指示器 -->
    <div class="flex-shrink-0">
      <div
        class="h-4 w-4 flex items-center justify-center border-2 rounded-full transition-colors"
        :class="{
          'border-primary bg-primary': isChecked,
          'border-muted-foreground/30': !isChecked,
        }"
      >
        <div
          v-if="isChecked"
          class="h-2 w-2 rounded-full bg-primary-foreground"
        />
      </div>
    </div>

    <!-- 图标（可选） -->
    <Icon
      v-if="props.icon"
      :name="props.icon"
      class="h-4 w-4 flex-shrink-0 text-primary"
    />

    <!-- 内容区域 -->
    <div class="min-w-0 flex-1">
      <div class="text-sm font-medium">
        {{ props.label }}
      </div>
      <div v-if="props.description" class="mt-1 text-xs text-muted-foreground">
        {{ props.description }}
      </div>
    </div>
  </RadioGroupItem>
</template>
