<script setup lang="ts">
const authStore = useAuthStore()
const accessStore = useAccessStore()

const userInfo = computed(() => authStore.userInfo)

async function handleLogout() {
  try {
    await authStore.logout()
  }
  catch (error) {
    console.error('退出登录失败:', error)
  }
}
</script>

<template>
  <div class="rounded-lg bg-white p-6 shadow">
    <h3 class="mb-4 text-lg text-gray-900 font-medium">
      用户信息
    </h3>

    <div v-if="authStore.isAuthenticated" class="space-y-3">
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-500 font-medium">认证状态:</span>
        <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs text-green-800 font-medium">
          已登录
        </span>
      </div>

      <div v-if="userInfo" class="space-y-2">
        <div class="flex justify-between">
          <span class="text-sm text-gray-500 font-medium">用户名:</span>
          <span class="text-sm text-gray-900">{{ userInfo.username }}</span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500 font-medium">真实姓名:</span>
          <span class="text-sm text-gray-900">{{ userInfo.realName }}</span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500 font-medium">邮箱:</span>
          <span class="text-sm text-gray-900">{{ userInfo.email }}</span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500 font-medium">角色:</span>
          <span class="text-sm text-gray-900">{{ userInfo.roles?.join(', ') }}</span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500 font-medium">部门:</span>
          <span class="text-sm text-gray-900">{{ userInfo.deptname || '无' }}</span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500 font-medium">TT ID:</span>
          <span class="text-sm text-gray-900">{{ userInfo.TT_id }}</span>
        </div>

        <div class="flex justify-between">
          <span class="text-sm text-gray-500 font-medium">权限:</span>
          <span class="text-sm text-gray-900">{{ userInfo.permissions?.join(', ') || '无' }}</span>
        </div>
      </div>

      <div class="border-t border-gray-200 pt-3">
        <div class="flex items-start justify-between">
          <span class="text-sm text-gray-500 font-medium">Token:</span>
          <span class="max-w-xs break-all text-xs text-gray-600 font-mono">
            {{ accessStore.accessToken?.substring(0, 50) }}...
          </span>
        </div>
      </div>

      <div class="pt-3">
        <button
          type="button"
          class="w-full flex justify-center border border-transparent rounded-md bg-red-600 px-4 py-2 text-sm text-white font-medium shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          @click="handleLogout"
        >
          退出登录
        </button>
      </div>
    </div>

    <div v-else class="text-center">
      <div class="mb-3 inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs text-red-800 font-medium">
        未登录
      </div>
      <p class="text-sm text-gray-500">
        请先登录以查看用户信息
      </p>
    </div>
  </div>
</template>
