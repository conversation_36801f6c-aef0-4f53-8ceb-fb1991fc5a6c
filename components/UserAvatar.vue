<script setup lang="ts">
import { LogOut, Settings, User } from 'lucide-vue-next'

const authStore = useAuthStore()
const userStore = useUserStore()

const userInfo = computed(() => userStore.userInfo)
const username = computed(() => userInfo.value?.username || 'User')
const email = computed(() => userInfo.value?.email || '')
const avatar = computed(() => userInfo.value?.avatar)

async function handleLogout() {
  await authStore.logout()
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" class="relative h-8 w-8 rounded-full">
        <Avatar class="h-8 w-8">
          <AvatarImage v-if="avatar" :src="avatar" :alt="username" />
          <AvatarFallback>
            {{ username.charAt(0).toUpperCase() }}
          </AvatarFallback>
        </Avatar>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent class="w-56" align="end" :side-offset="4">
      <DropdownMenuLabel class="font-normal">
        <div class="flex flex-col space-y-1">
          <p class="text-sm font-medium leading-none">
            {{ username }}
          </p>
          <p v-if="email" class="text-xs text-muted-foreground leading-none">
            {{ email }}
          </p>
        </div>
      </DropdownMenuLabel>
      <DropdownMenuSeparator />
      <DropdownMenuGroup>
        <DropdownMenuItem>
          <User class="mr-2 h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Settings class="mr-2 h-4 w-4" />
          <span>Settings</span>
        </DropdownMenuItem>
      </DropdownMenuGroup>
      <DropdownMenuSeparator />
      <DropdownMenuItem @click="handleLogout">
        <LogOut class="mr-2 h-4 w-4" />
        <span>Log out</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
