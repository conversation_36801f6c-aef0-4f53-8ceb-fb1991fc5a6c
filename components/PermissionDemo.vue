<script setup lang="ts">
const { hasPermission, hasRole, hasAccess } = usePermission()

// 示例权限检查
const canEdit = computed(() => hasPermission('edit'))
const canDelete = computed(() => hasPermission(['delete', 'admin']))
const isAdmin = computed(() => hasRole('admin'))
const hasUserAccess = computed(() => hasAccess({
  codes: ['user:read'],
  roles: ['user', 'admin'],
  mode: 'or',
}))
</script>

<template>
  <div class="border rounded-lg p-4 space-y-4">
    <h3 class="text-lg font-semibold">
      权限演示
    </h3>

    <div class="space-y-2">
      <div class="flex items-center gap-2">
        <Badge v-if="canEdit" variant="default">
          可编辑
        </Badge>
        <Badge v-else variant="secondary">
          不可编辑
        </Badge>
      </div>

      <div class="flex items-center gap-2">
        <Badge v-if="canDelete" variant="destructive">
          可删除
        </Badge>
        <Badge v-else variant="secondary">
          不可删除
        </Badge>
      </div>

      <div class="flex items-center gap-2">
        <Badge v-if="isAdmin" variant="default">
          管理员
        </Badge>
        <Badge v-else variant="secondary">
          普通用户
        </Badge>
      </div>

      <div class="flex items-center gap-2">
        <Badge v-if="hasUserAccess" variant="default">
          有用户访问权限
        </Badge>
        <Badge v-else variant="secondary">
          无用户访问权限
        </Badge>
      </div>
    </div>

    <!-- 使用指令的示例 -->
    <div class="space-y-2">
      <Button v-permission="'edit'" variant="default">
        编辑按钮 (需要 edit 权限)
      </Button>

      <Button v-permission="['delete', 'admin']" variant="destructive">
        删除按钮 (需要 delete 或 admin 权限)
      </Button>

      <Button v-permission="{ roles: 'admin' }" variant="outline">
        管理员按钮 (需要 admin 角色)
      </Button>

      <Button v-permission="{ codes: 'user:create', roles: 'admin', mode: 'and' }" variant="secondary">
        创建用户 (需要权限码和角色)
      </Button>
    </div>
  </div>
</template>
