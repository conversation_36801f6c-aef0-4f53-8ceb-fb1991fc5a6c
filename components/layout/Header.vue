<script setup lang="ts">
const route = useRoute()

// 获取当前路径，优先使用浏览器的实际路径
function getCurrentPath() {
  // 在客户端使用 window.location.pathname，在服务端使用 route.fullPath
  if (import.meta.client) {
    return window.location.pathname
  }
  return route.fullPath
}

function setLinks() {
  const currentPath = getCurrentPath()

  if (currentPath === '/') {
    return [{ title: 'Home', href: '/' }]
  }

  const segments = currentPath.split('/').filter(item => item !== '')

  const breadcrumbs = segments.map((item, index) => {
    const str = item.replace(/-/g, ' ')

    // 特殊处理工单详情页面
    if (str === 'ticket detail' && segments[index - 1] === 'feedback') {
      return {
        title: '工单详情',
        href: `/${segments.slice(0, index + 1).join('/')}`,
      }
    }

    // 特殊处理反馈页面
    if (str === 'feedback') {
      return {
        title: '用户反馈',
        href: `/${segments.slice(0, index + 1).join('/')}`,
      }
    }

    // 特殊处理工单列表页面
    if (str === 'tickets list') {
      return {
        title: '工单列表',
        href: `/${segments.slice(0, index + 1).join('/')}`,
      }
    }

    const title = str
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')

    return {
      title,
      href: `/${segments.slice(0, index + 1).join('/')}`,
    }
  })

  return [{ title: 'Home', href: '/' }, ...breadcrumbs]
}

const links = ref<{
  title: string
  href: string
}[]>(setLinks())

// 监听 Vue Router 的路径变化
watch(() => route.fullPath, (val) => {
  if (val) {
    links.value = setLinks()
  }
})

// 在客户端监听浏览器 URL 变化（包括 history.replaceState 引起的变化）
onMounted(() => {
  if (import.meta.client) {
    // 监听 popstate 事件（浏览器前进/后退）
    window.addEventListener('popstate', () => {
      links.value = setLinks()
    })

    // 监听自定义事件，当 URL 通过 history.replaceState 更新时触发
    window.addEventListener('urlchange', () => {
      links.value = setLinks()
    })

    // 定期检查 URL 变化（作为备用方案）
    const checkUrlChange = () => {
      const newLinks = setLinks()
      if (JSON.stringify(newLinks) !== JSON.stringify(links.value)) {
        links.value = newLinks
      }
    }

    const intervalId = setInterval(checkUrlChange, 100)

    // 清理定时器
    onUnmounted(() => {
      clearInterval(intervalId)
    })
  }
})
</script>

<template>
  <header class="sticky top-0 z-10 h-53px flex items-center gap-4 border-b bg-background px-4 md:px-6">
    <div class="w-full flex items-center gap-4">
      <SidebarTrigger />
      <Separator orientation="vertical" class="h-4" />
      <BaseBreadcrumbCustom :links="links" />
    </div>
    <div class="ml-auto flex items-center gap-4">
      <slot />
    </div>
  </header>
</template>

<style scoped>

</style>
