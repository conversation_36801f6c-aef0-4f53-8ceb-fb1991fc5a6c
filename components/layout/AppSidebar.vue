<script setup lang="ts">
import type { NavGroup, NavLink, NavSectionTitle } from '~/types/nav'
import { navMenu, navMenuBottom } from '~/constants/menus'

function resolveNavItemComponent(item: NavLink | NavGroup | NavSectionTitle): any {
  if ('children' in item)
    return resolveComponent('LayoutSidebarNavGroup')

  return resolveComponent('LayoutSidebarNavLink')
}

const apps: {
  name: string
  logo: string
  plan: string
}[] = [
  {
    name: 'TT语音',
    logo: '/logo/TT.png',
    plan: '',
  },
  {
    name: '唱鸭',
    logo: '/logo/changya.png',
    plan: '',
  },
]

// 移除硬编码的用户数据，现在由SidebarNavFooter组件直接从store获取

const { sidebar } = useAppSettings()
</script>

<template>
  <Sidebar :collapsible="sidebar.collapsible" :side="sidebar.side" :variant="sidebar.variant">
    <SidebarHeader>
      <LayoutSidebarNavHeader :apps="apps" />
      <Search />
    </SidebarHeader>
    <SidebarContent>
      <SidebarGroup v-for="(nav, indexGroup) in navMenu" :key="indexGroup">
        <SidebarGroupLabel v-if="nav.heading">
          {{ nav.heading }}
        </SidebarGroupLabel>
        <component :is="resolveNavItemComponent(item)" v-for="(item, index) in nav.items" :key="index" :item="item" />
      </SidebarGroup>
      <SidebarGroup class="mt-auto">
        <component
          :is="resolveNavItemComponent(item)"
          v-for="(item, index) in navMenuBottom"
          :key="index"
          :item="item"
          size="sm"
        />
      </SidebarGroup>
    </SidebarContent>
    <SidebarFooter>
      <ClientOnly>
        <LayoutSidebarNavFooter />
        <template #fallback>
          <div class="flex items-center gap-2 px-2 py-1.5">
            <div class="h-8 w-8 animate-pulse rounded-lg bg-muted" />
            <div class="flex-1">
              <div class="mb-1 h-4 w-20 animate-pulse rounded bg-muted" />
              <div class="h-3 w-16 animate-pulse rounded bg-muted" />
            </div>
          </div>
        </template>
      </ClientOnly>
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>

<style scoped></style>
