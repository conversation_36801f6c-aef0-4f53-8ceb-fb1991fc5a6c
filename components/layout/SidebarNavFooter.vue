<script setup lang="ts">
import { useSidebar } from '~/components/ui/sidebar'

// 移除props，直接使用认证数据
const authStore = useAuthStore()
const userStore = useUserStore()

// 计算用户信息
const userInfo = computed(() => userStore.userInfo || authStore.userInfo)
const username = computed(() => userInfo.value?.realName || userInfo.value?.username || 'User')
const email = computed(() => userInfo.value?.email || '')
const avatar = computed(() => userInfo.value?.avatar || '')

const { isMobile, setOpenMobile } = useSidebar()

async function handleLogout() {
  try {
    await authStore.logout()
  }
  catch (error) {
    console.error('Logout failed:', error)
    // 如果logout失败，强制跳转到登录页
    navigateTo('/login')
  }
}

const showModalTheme = ref(false)
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage v-if="avatar" :src="avatar" :alt="username" />
              <AvatarFallback class="rounded-lg">
                {{ username.charAt(0).toUpperCase() }}
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <div class="flex items-center gap-2">
                <span class="truncate font-semibold">{{ username }}</span>
              </div>
              <span class="truncate text-xs">{{ email }}</span>
            </div>
            <Icon name="i-lucide-chevrons-up-down" class="ml-auto size-4" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="min-w-56 w-[--radix-dropdown-menu-trigger-width] rounded-lg"
          :side="isMobile ? 'bottom' : 'right'"
          align="end"
        >
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="h-8 w-8 rounded-lg">
                <AvatarImage v-if="avatar" :src="avatar" :alt="username" />
                <AvatarFallback class="rounded-lg">
                  {{ username.charAt(0).toUpperCase() }}
                </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <div class="flex items-center gap-2">
                  <span class="truncate font-semibold">{{ username }}</span>
                </div>
                <span class="truncate text-xs">{{ email }}</span>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />

          <DropdownMenuGroup>
            <DropdownMenuItem @click="showModalTheme = true">
              <Icon name="i-lucide-paintbrush" />
              Theme
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="handleLogout">
            <Icon name="i-lucide-log-out" />
            退出登录
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>

  <Dialog v-model:open="showModalTheme">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Customize</DialogTitle>
        <DialogDescription class="text-xs text-muted-foreground">
          Customize & Preview in Real Time
        </DialogDescription>
      </DialogHeader>
      <ThemeCustomize />
    </DialogContent>
  </Dialog>
</template>

<style scoped>

</style>
