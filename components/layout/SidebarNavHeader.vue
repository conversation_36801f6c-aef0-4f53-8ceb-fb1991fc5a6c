<script setup lang="ts">
import { useSidebar } from '~/components/ui/sidebar'

const props = defineProps<{
  apps: {
    name: string
    logo: string
    plan: string
  }[]
}>()

const { isMobile } = useSidebar()

const activeApp = ref(props.apps[0])
</script>

<template>
  <SidebarMenu>
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div
              class="aspect-square size-8 flex items-center justify-center rounded-lg text-sidebar-primary-foreground"
            >
              <img :src="`${activeApp.logo}`" class="size-6">
            </div>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">
                {{ activeApp.name }}
              </span>
              <span class="truncate text-xs">{{ activeApp.plan }}</span>
            </div>
            <Icon name="i-lucide-chevrons-up-down" class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="min-w-56 w-[--radix-dropdown-menu-trigger-width] rounded-lg"
          align="start"
          :side="isMobile ? 'bottom' : 'right'"
        >
          <DropdownMenuLabel class="text-xs text-muted-foreground">
            Apps
          </DropdownMenuLabel>
          <DropdownMenuItem
            v-for="app in apps"
            :key="app.name"
            class="gap-2 p-2"
            @click="activeApp = app"
          >
            <div class="size-6 flex items-center justify-center border rounded-sm">
              <img :src="`${app.logo}`" class="size-4">
            </div>
            {{ app.name }}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
</template>

<style scoped></style>
