// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },

  // 关闭SSR，使用SPA模式
  ssr: false,

  // 开发服务器配置
  devServer: {
    port: 3456,
    host: 'localhost',
  },

  modules: [
    '@unocss/nuxt',
    'shadcn-nuxt',
    '@vueuse/nuxt',
    '@vueuse/motion/nuxt',
    '@nuxt/eslint',
    '@nuxt/icon',
    '@pinia/nuxt',
    '@nuxtjs/color-mode',
  ],

  css: [
    '@unocss/reset/tailwind.css',
  ],

  colorMode: {
    classSuffix: '',
  },

  features: {
    // For UnoCSS
    inlineStyles: false,
  },

  eslint: {
    config: {
      standalone: false,
    },
  },

  routeRules: {
    '/components': { redirect: '/components/accordion' },
    '/settings': { redirect: '/settings/profile' },
    '/feedback/ticket-detail': { redirect: '/feedback' },
  },

  imports: {
    dirs: [
      './lib',
      './composables',
    ],
  },

  runtimeConfig: {
    public: {
      apiBase: '/api',
      apiTarget: 'http://itmp-test.ttyuyin.com',
      appName: 'Nuxt Shadcn Dashboard',
      appVersion: '1.0.0',
    },
  },

  nitro: {
    devProxy: {
      // 代理所有业务API到真实服务器
      '/api/user': 'http://itmp-test.ttyuyin.com/api/user',
      '/api/feedback': 'http://itmp-test.ttyuyin.com/api/feedback',
      '/api/ResponsiblePersons': 'http://itmp-test.ttyuyin.com/api/ResponsiblePersons',
      // 代理华为云OBS媒体文件，解决CORS问题
      '/proxy/obs/**': 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com',
    },
  },

  compatibilityDate: '2024-12-14',
})
