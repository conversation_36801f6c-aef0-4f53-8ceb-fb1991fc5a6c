import type { ErrorCodeConfig } from './errorCodes'
import type { ErrorHandleResult } from './errorHandler'
import { ErrorCodeHandler } from './errorHandler'

/**
 * 错误处理工具函数
 */

/**
 * 处理 API 调用错误的通用函数
 */
export async function handleApiCall<T>(
  apiCall: () => Promise<T>,
  options: {
    showError?: boolean
    showSuccess?: boolean
    successMessage?: string
    onError?: (error: ErrorHandleResult) => void
    onSuccess?: (data: T) => void
  } = {},
): Promise<{ data: T | null, error: ErrorHandleResult | null }> {
  const {
    showError = true,
    showSuccess = false,
    successMessage = '操作成功',
    onError,
    onSuccess,
  } = options

  try {
    const data = await apiCall()

    if (showSuccess && import.meta.client) {
      const { useNotification } = await import('~/composables/useNotification')
      const notification = useNotification()
      notification.success(successMessage, '操作成功')
    }

    onSuccess?.(data)

    return { data, error: null }
  }
  catch (error) {
    const errorResult = ErrorCodeHandler.handleApiError(error)

    if (showError && import.meta.client && !errorResult.isAuthError) {
      const { useNotification } = await import('~/composables/useNotification')
      const notification = useNotification()

      // 根据错误类型显示不同的通知
      switch (errorResult.type) {
        case 'error':
          notification.error(errorResult.message, '操作失败')
          break
        case 'warning':
          notification.warning(errorResult.message, '注意')
          break
        case 'info':
          notification.info(errorResult.message, '提示')
          break
        default:
          notification.error(errorResult.message, '操作失败')
      }
    }

    onError?.(errorResult)

    return { data: null, error: errorResult }
  }
}

/**
 * 创建一个带错误处理的异步函数
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  options: {
    showError?: boolean
    showSuccess?: boolean
    successMessage?: string
  } = {},
) {
  return async (...args: T): Promise<{ data: R | null, error: ErrorHandleResult | null }> => {
    return handleApiCall(() => fn(...args), options)
  }
}

/**
 * 检查错误是否需要重新认证
 */
export function shouldReAuthenticate(error: any): boolean {
  const errorResult = ErrorCodeHandler.handleApiError(error)
  return errorResult.isAuthError && errorResult.shouldRedirect
}

/**
 * 获取错误的用户友好消息
 */
export function getErrorMessage(error: any): string {
  const errorResult = ErrorCodeHandler.handleApiError(error)
  return ErrorCodeHandler.formatErrorMessage(errorResult)
}

/**
 * 检查是否为特定类型的错误
 */
export function isErrorType(error: any, type: ErrorCodeConfig['type']): boolean {
  const errorResult = ErrorCodeHandler.handleApiError(error)
  return errorResult.type === type
}

/**
 * 检查是否为特定分类的错误
 */
export function isErrorCategory(error: any, category: ErrorCodeConfig['category']): boolean {
  const errorResult = ErrorCodeHandler.handleApiError(error)
  return errorResult.category === category
}

/**
 * 创建一个错误对象
 */
export function createApiError(code: number, message?: string): Error {
  const error = new Error(message || `Error code: ${code}`)
  ;(error as any).code = code
  return error
}

/**
 * 用于 Vue 组件的错误处理 composable
 */
export function useApiErrorHandler() {
  const loading = ref(false)
  const error = ref<ErrorHandleResult | null>(null)

  const execute = async <T>(
    apiCall: () => Promise<T>,
    options: {
      showError?: boolean
      showSuccess?: boolean
      successMessage?: string
    } = {},
  ): Promise<T | null> => {
    loading.value = true
    error.value = null

    try {
      const result = await handleApiCall(apiCall, options)
      error.value = result.error
      return result.data
    }
    finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    execute,
    clearError,
  }
}

/**
 * 批量处理多个 API 调用
 */
export async function handleBatchApiCalls<T>(
  apiCalls: Array<() => Promise<T>>,
  options: {
    showErrors?: boolean
    continueOnError?: boolean
    showProgress?: boolean
  } = {},
): Promise<{
    results: Array<{ data: T | null, error: ErrorHandleResult | null }>
    hasErrors: boolean
    successCount: number
    errorCount: number
  }> {
  const { showErrors = true, continueOnError = true } = options
  const results: Array<{ data: T | null, error: ErrorHandleResult | null }> = []
  let successCount = 0
  let errorCount = 0

  for (const apiCall of apiCalls) {
    try {
      const result = await handleApiCall(apiCall, { showError: showErrors })
      results.push(result)

      if (result.error) {
        errorCount++
        if (!continueOnError) {
          break
        }
      }
      else {
        successCount++
      }
    }
    catch (error) {
      const errorResult = ErrorCodeHandler.handleApiError(error)
      results.push({ data: null, error: errorResult })
      errorCount++

      if (!continueOnError) {
        break
      }
    }
  }

  return {
    results,
    hasErrors: errorCount > 0,
    successCount,
    errorCount,
  }
}
