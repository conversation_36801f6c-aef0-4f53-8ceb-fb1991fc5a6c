import type { ErrorCodeConfig } from './errorCodes'
import { AUTH_ERROR_CODES, ERROR_CODE_MAP, ERROR_MESSAGE_KEYWORDS } from './errorCodes'

/**
 * 错误处理结果接口
 */
export interface ErrorHandleResult {
  code: number
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  category: 'general' | 'auth' | 'permission' | 'data' | 'system'
  isAuthError: boolean
  shouldShowModal: boolean
  shouldRedirect: boolean
}

/**
 * 错误码处理器
 */
export class ErrorCodeHandler {
  /**
   * 根据错误码获取错误配置
   */
  static getErrorConfig(code: number): ErrorCodeConfig | null {
    return ERROR_CODE_MAP[code] || null
  }

  /**
   * 根据错误消息匹配认证错误
   */
  static isAuthErrorByMessage(message: string): boolean {
    if (!message)
      return false

    return ERROR_MESSAGE_KEYWORDS.authExpired.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase()),
    )
  }

  /**
   * 检查是否为认证错误
   */
  static isAuthError(code?: number, message?: string): boolean {
    // 通过错误码判断
    if (code && AUTH_ERROR_CODES.includes(code)) {
      return true
    }

    // 通过错误消息判断
    if (message && this.isAuthErrorByMessage(message)) {
      return true
    }

    return false
  }

  /**
   * 处理API错误，返回统一的错误处理结果
   */
  static handleApiError(error: any): ErrorHandleResult {
    let code: number | undefined
    let message: string = '请求失败'
    let httpStatus: number | undefined

    // 从不同位置提取错误信息
    if (error?.response) {
      // Axios 响应错误
      httpStatus = error.response.status
      const responseData = error.response.data

      if (responseData) {
        code = responseData.code
        message = responseData.msg || responseData.message || message
      }
    }
    else if (error?.code !== undefined) {
      // 直接的错误码
      code = error.code
      message = error.msg || error.message || message
    }
    else if (error?.message) {
      // 普通错误对象
      message = error.message
    }

    // 获取错误配置
    let errorConfig: ErrorCodeConfig | null = null
    if (code !== undefined) {
      errorConfig = this.getErrorConfig(code)
    }

    // 如果没有找到错误配置，根据HTTP状态码生成默认配置
    if (!errorConfig && httpStatus) {
      errorConfig = this.getHttpErrorConfig(httpStatus, message)
    }

    // 最终的错误配置
    const finalConfig: ErrorCodeConfig = errorConfig || {
      code: code || -1,
      message,
      type: 'error',
      category: 'general',
    }

    // 判断是否为认证错误
    const isAuthError = this.isAuthError(finalConfig.code, finalConfig.message)

    return {
      code: finalConfig.code,
      message: finalConfig.message,
      type: finalConfig.type,
      category: finalConfig.category,
      isAuthError,
      shouldShowModal: isAuthError || finalConfig.category === 'system',
      shouldRedirect: isAuthError,
    }
  }

  /**
   * 根据HTTP状态码生成错误配置
   */
  private static getHttpErrorConfig(status: number, originalMessage: string): ErrorCodeConfig {
    const httpErrorMap: Record<number, Omit<ErrorCodeConfig, 'code'>> = {
      400: {
        message: '请求参数错误',
        type: 'error',
        category: 'data',
      },
      401: {
        message: '未授权访问',
        type: 'warning',
        category: 'auth',
      },
      403: {
        message: '禁止访问',
        type: 'error',
        category: 'permission',
      },
      404: {
        message: '请求的资源不存在',
        type: 'error',
        category: 'general',
      },
      408: {
        message: '请求超时',
        type: 'warning',
        category: 'general',
      },
      500: {
        message: '服务器内部错误',
        type: 'error',
        category: 'system',
      },
      502: {
        message: '网关错误',
        type: 'error',
        category: 'system',
      },
      503: {
        message: '服务不可用',
        type: 'error',
        category: 'system',
      },
      504: {
        message: '网关超时',
        type: 'error',
        category: 'system',
      },
    }

    const config = httpErrorMap[status] || {
      message: originalMessage || `HTTP错误 ${status}`,
      type: 'error' as const,
      category: 'general' as const,
    }

    return {
      code: status,
      ...config,
    }
  }

  /**
   * 格式化错误消息用于显示
   */
  static formatErrorMessage(result: ErrorHandleResult): string {
    const { code, message, category } = result

    // 对于系统错误，添加错误码信息
    if (category === 'system' && code !== -1) {
      return `${message} (错误码: ${code})`
    }

    return message
  }

  /**
   * 获取错误类型对应的图标
   */
  static getErrorIcon(type: ErrorCodeConfig['type']): string {
    const iconMap = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
    }
    return iconMap[type] || iconMap.error
  }
}

/**
 * 便捷的错误处理函数
 */
export function handleError(error: any): ErrorHandleResult {
  return ErrorCodeHandler.handleApiError(error)
}

/**
 * 检查是否为认证错误的便捷函数
 */
export function isAuthError(error: any): boolean {
  const result = handleError(error)
  return result.isAuthError
}
