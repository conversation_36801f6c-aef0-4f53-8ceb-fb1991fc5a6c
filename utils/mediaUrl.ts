/**
 * 媒体URL处理工具
 * 用于处理图片和视频URL的代理、格式化等问题
 */

/**
 * 检查URL是否需要代理
 */
export function needsProxy(url: string): boolean {
  if (!url)
    return false

  // 如果是绝对URL（http/https开头），不需要代理
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return false
  }

  // 如果是相对路径或以/api开头，可能需要代理
  if (url.startsWith('/') || url.startsWith('api/')) {
    return true
  }

  return false
}

/**
 * 格式化媒体URL
 * 处理相对路径、代理等问题
 */
export function formatMediaUrl(url: string): string {
  if (!url)
    return ''

  // 检查是否是华为云OBS URL，如果是则使用代理
  if (url.includes('obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com')) {
    // 提取文件路径部分
    const urlObj = new URL(url)
    const filePath = urlObj.pathname
    // 确保路径不以/开头，避免双斜杠问题
    const cleanPath = filePath.startsWith('/') ? filePath.substring(1) : filePath
    const proxyUrl = `/api/proxy/obs/${cleanPath}`

    // 移除调试信息避免无限循环

    // 使用本地代理
    return proxyUrl
  }

  // 检查其他可能需要代理的外部媒体URL
  if (url.startsWith('https://') && (
    url.includes('myhuaweicloud.com')
    || url.includes('obs-')
    || url.includes('aliyuncs.com')
    || url.includes('qcloud.com')
  )) {
    // 对于其他云存储，也可以考虑使用代理
    // 移除console.warn避免无限循环
  }

  // 如果已经是完整的URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 处理相对路径
  if (url.startsWith('./') || url.startsWith('../')) {
    // 相对路径转换为绝对路径
    return new URL(url, window.location.origin).href
  }

  // 处理以/开头的绝对路径
  if (url.startsWith('/')) {
    // 检查是否是API路径，如果是则保持原样（会被代理处理）
    if (url.startsWith('/api/')) {
      return url
    }
    // 其他路径添加域名
    return `${window.location.origin}${url}`
  }

  // 处理不以/开头的相对路径
  if (!url.startsWith('/')) {
    // 如果是API路径，添加/api前缀
    if (url.startsWith('feedback/') || url.startsWith('upload/')) {
      return `/api/${url}`
    }
    // 其他情况添加完整域名
    return `${window.location.origin}/${url}`
  }

  return url
}

/**
 * 获取媒体文件的MIME类型
 */
export function getMediaMimeType(filename: string): string {
  const ext = filename.split('.').pop()?.toLowerCase()

  switch (ext) {
    case 'mp4':
      return 'video/mp4'
    case 'mov':
      return 'video/quicktime'
    case 'rmvb':
      return 'video/x-ms-wmv' // RMVB没有标准MIME类型，使用通用视频类型
    case 'avi':
      return 'video/x-msvideo'
    case 'webm':
      return 'video/webm'
    case 'ogg':
      return 'video/ogg'
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg'
    case 'png':
      return 'image/png'
    case 'gif':
      return 'image/gif'
    case 'webp':
      return 'image/webp'
    default:
      return 'application/octet-stream'
  }
}

/**
 * 检查浏览器是否支持指定的视频格式
 */
export function canPlayVideoType(type: string): boolean {
  const video = document.createElement('video')
  const mimeType = getMediaMimeType(`test.${type}`)

  const canPlay = video.canPlayType(mimeType)
  return canPlay === 'probably' || canPlay === 'maybe'
}

/**
 * 获取支持的视频格式列表
 */
export function getSupportedVideoFormats(): string[] {
  const formats = ['mp4', 'webm', 'ogg', 'mov']
  return formats.filter(format => canPlayVideoType(format))
}

/**
 * 预加载媒体文件
 */
export function preloadMedia(url: string, type: 'image' | 'video' = 'image'): Promise<void> {
  return new Promise((resolve, reject) => {
    if (type === 'image') {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = formatMediaUrl(url)
    }
    else {
      const video = document.createElement('video')
      video.onloadedmetadata = () => resolve()
      video.onerror = () => reject(new Error('视频加载失败'))
      video.preload = 'metadata'
      video.src = formatMediaUrl(url)
    }
  })
}

/**
 * 检查媒体URL是否可访问
 */
export async function checkMediaUrl(url: string): Promise<{
  accessible: boolean
  error?: string
  status?: number
}> {
  try {
    const formattedUrl = formatMediaUrl(url)
    const response = await fetch(formattedUrl, {
      method: 'HEAD',
      mode: 'cors',
    })

    return {
      accessible: response.ok,
      status: response.status,
    }
  }
  catch (error) {
    return {
      accessible: false,
      error: error instanceof Error ? error.message : '未知错误',
    }
  }
}

/**
 * 调试媒体URL信息
 */
export function debugMediaUrl(url: string) {
  const formattedUrl = formatMediaUrl(url)
  const isUsingProxy = formattedUrl.startsWith('/api/proxy/') || formattedUrl.startsWith('/proxy/')
  const isObsUrl = url.includes('obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com')

  const info = {
    original: url,
    formatted: formattedUrl,
    needsProxy: needsProxy(url),
    isAbsolute: url.startsWith('http'),
    isRelative: url.startsWith('./') || url.startsWith('../'),
    isApiPath: url.startsWith('/api/') || url.startsWith('api/'),
    isUsingProxy,
    isObsUrl,
    extension: url.split('.').pop()?.toLowerCase(),
    mimeType: getMediaMimeType(url),
    browserSupport: url.split('.').pop() ? canPlayVideoType(url.split('.').pop()!) : false,
  }

  // 移除console.log避免无限循环
  return info
}
