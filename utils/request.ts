import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse, RequestConfig } from '~/types/auth'
import axios from 'axios'
import { isAuthenticationError, shouldShowAuthExpiredDialog } from './errorCodes'
import { ErrorCodeHandler } from './errorHandler'

// 创建 axios 实例
function createAxiosInstance(config: AxiosRequestConfig = {}): AxiosInstance {
  const instance = axios.create({
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
    },
    ...config,
  })

  return instance
}

// 请求客户端类
export class RequestClient {
  private instance: AxiosInstance
  public isRefreshing = false
  public refreshTokenQueue: Array<(token: string) => void> = []
  private isShowingAuthDialog = false // 防止重复显示认证过期弹窗

  constructor(config: AxiosRequestConfig = {}) {
    this.instance = createAxiosInstance(config)
    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 在客户端添加 token
        if (import.meta.client) {
          try {
            const { $auth } = useNuxtApp()
            const token = $auth?.getToken()
            if (token && !(config as any).skipAuth) {
              config.headers.Authorization = `Bearer ${token}`
            }
          }
          catch {
            // 忽略错误，可能是在初始化阶段
          }
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      },
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        const { data } = response

        // 处理响应数据格式
        if (data && typeof data === 'object' && 'code' in data) {
          // 根据实际API响应格式，code: 20000 或 0 表示成功
          if (data.code === 20000 || data.code === 0 || data.status === 'ok') {
            return data.data
          }
          else {
            // 使用统一的错误处理机制，包括认证错误
            const errorResult = ErrorCodeHandler.handleApiError(data)

            // 创建一个类似 Axios 错误的对象
            const error = new Error(errorResult.message)
            ;(error as any).errorResult = errorResult
            ;(error as any).response = {
              data,
              status: response.status,
              statusText: response.statusText,
              headers: response.headers,
              config: response.config,
            }
            ;(error as any).config = response.config

            // 如果是认证错误，标记为认证错误
            if (errorResult.isAuthError) {
              ;(error as any).isAuthError = true
            }

            // 直接在这里处理认证错误，不依赖错误拦截器
            if (shouldShowAuthExpiredDialog(error)) {
              // 异步调用认证过期弹窗，不阻塞错误抛出
              this.showAuthExpiredDialog().catch((err) => {
                console.error('显示认证过期弹窗失败:', err)
              })
            }

            throw error
          }
        }

        return data
      },
      async (error) => {
        const { config } = error

        // 检查是否为认证相关错误
        if (isAuthenticationError(error) && !config?.skipAuth) {
          // 对于需要直接显示认证过期弹窗的错误，不尝试刷新token
          if (shouldShowAuthExpiredDialog(error)) {
            // 只有在不是登录页面时才显示弹窗
            const route = useRoute()
            if (route.path !== '/login' && route.meta?.ignoreAccess !== true) {
              await this.showAuthExpiredDialog()
            }
            return Promise.reject(error)
          }
          // 其他认证错误尝试刷新token
          return this.handleUnauthorized(error)
        }

        // 处理其他错误
        return this.handleError(error)
      },
    )
  }

  private async handleUnauthorized(error: any) {
    const { config } = error

    if (config.__isRetryRequest) {
      // 避免无限重试，直接显示认证过期对话框
      await this.showAuthExpiredDialog()
      return Promise.reject(error)
    }

    if (this.isRefreshing) {
      // 如果正在刷新 token，将请求加入队列
      return new Promise((resolve) => {
        this.refreshTokenQueue.push((newToken: string) => {
          config.headers.Authorization = `Bearer ${newToken}`
          resolve(this.instance.request(config))
        })
      })
    }

    this.isRefreshing = true
    config.__isRetryRequest = true

    try {
      const newToken = await this.doRefreshToken()

      // 处理队列中的请求
      this.refreshTokenQueue.forEach(callback => callback(newToken))
      this.refreshTokenQueue = []

      // 重试原请求
      config.headers.Authorization = `Bearer ${newToken}`
      return this.instance.request(config)
    }
    catch (refreshError) {
      this.refreshTokenQueue = []
      await this.showAuthExpiredDialog()
      return Promise.reject(refreshError)
    }
    finally {
      this.isRefreshing = false
    }
  }

  private async doRefreshToken(): Promise<string> {
    if (import.meta.client) {
      const authStore = useAuthStore()
      return await authStore.refreshToken()
    }
    throw new Error('Cannot refresh token on server side')
  }

  /**
   * 显示认证过期对话框
   */
  private async showAuthExpiredDialog(): Promise<void> {
    if (import.meta.client) {
      // 防止重复显示认证过期弹窗
      if (this.isShowingAuthDialog) {
        return
      }

      // 检查当前是否在登录页面，如果在登录页面则不显示弹窗
      const route = useRoute()
      if (route.path === '/login' || route.meta?.ignoreAccess === true) {
        return
      }

      this.isShowingAuthDialog = true

      try {
        // 使用统一的弹窗系统
        const { useModal } = await import('~/composables/useModal')
        const modal = useModal()
        await modal.showAuthExpiredModal()
      }
      catch (error) {
        console.error('显示认证过期弹窗失败:', error)
      }
      finally {
        // 重置标志，允许下次显示（但通常用户会被重定向到登录页）
        this.isShowingAuthDialog = false
      }
    }
  }

  private async handleError(error: any) {
    const { config } = error

    // 使用新的错误处理机制
    const errorResult = ErrorCodeHandler.handleApiError(error)

    // 显示错误消息和调试信息
    if (import.meta.client && !config?.skipErrorHandler) {
      console.error('Request Error:', {
        errorResult,
        url: config?.url,
        method: config?.method,
        originalError: error,
      })

      // 如果是认证错误，显示认证过期弹窗
      if (errorResult.isAuthError) {
        // 只有在不是登录页面时才显示弹窗
        const route = useRoute()
        if (route.path !== '/login' && route.meta?.ignoreAccess !== true) {
          await this.showAuthExpiredDialog()
        }
      }
      // 根据错误类型决定是否显示用户提示
      else if (errorResult.shouldShowModal) {
        this.showErrorNotification(errorResult)
      }
    }

    // 将错误结果附加到错误对象上
    ;(error as any).errorResult = errorResult

    return Promise.reject(error)
  }

  /**
   * 显示错误通知
   */
  private async showErrorNotification(errorResult: any) {
    if (import.meta.client) {
      try {
        // 动态导入通知组件，避免服务端渲染问题
        const { useNotification } = await import('~/composables/useNotification')
        const notification = useNotification()

        // 根据错误类型显示不同的通知
        switch (errorResult.type) {
          case 'error':
            notification.error(errorResult.message, '操作失败')
            break
          case 'warning':
            notification.warning(errorResult.message, '注意')
            break
          case 'info':
            notification.info(errorResult.message, '提示')
            break
          default:
            notification.error(errorResult.message, '操作失败')
        }
      }
      catch {
        // 如果组件加载失败，使用 console.error 作为后备
        console.error('Error notification failed:', errorResult.message)
      }
    }
  }

  // HTTP 方法
  get<T = any>(url: string, config?: AxiosRequestConfig & RequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig & RequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig & RequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  delete<T = any>(url: string, config?: AxiosRequestConfig & RequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }

  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig & RequestConfig): Promise<T> {
    return this.instance.patch(url, data, config)
  }
}

// 创建请求客户端实例
export const requestClient = new RequestClient({
  baseURL: '/api',
})
export const baseRequestClient = new RequestClient({
  baseURL: '/api',
  skipAuth: true,
} as any)
