/**
 * 错误码映射配置
 * 统一管理所有API错误码和对应的错误消息
 */

export interface ErrorCodeConfig {
  code: number
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  category: 'general' | 'auth' | 'permission' | 'data' | 'system'
  /** 是否为认证错误 */
  isAuthError?: boolean
  /** 是否需要直接显示认证过期弹窗（不尝试刷新token） */
  showAuthExpiredDialog?: boolean
  /** 是否显示错误通知 */
  showNotification?: boolean
  /** 是否需要重定向到登录页 */
  redirectToLogin?: boolean
}

/**
 * 错误码映射表
 */
export const ERROR_CODE_MAP: Record<number, ErrorCodeConfig> = {
  // 成功状态
  0: {
    code: 0,
    message: '成功',
    type: 'success',
    category: 'general',
  },
  20000: {
    code: 20000,
    message: '请求成功',
    type: 'success',
    category: 'general',
  },

  // 数据相关错误 (1000-1999)
  1001: {
    code: 1001,
    message: '查询失败，未查询到相关数据',
    type: 'warning',
    category: 'data',
  },
  1002: {
    code: 1002,
    message: '查询失败，参数错误',
    type: 'error',
    category: 'data',
  },
  1003: {
    code: 1003,
    message: '插入失败',
    type: 'error',
    category: 'data',
  },
  1004: {
    code: 1004,
    message: '更新失败',
    type: 'error',
    category: 'data',
  },
  1005: {
    code: 1005,
    message: '删除失败',
    type: 'error',
    category: 'data',
  },
  1006: {
    code: 1006,
    message: '数据库连接失败',
    type: 'error',
    category: 'system',
  },
  1007: {
    code: 1007,
    message: '分页参数错误',
    type: 'error',
    category: 'data',
  },
  1008: {
    code: 1008,
    message: '定时任务创建失败',
    type: 'error',
    category: 'system',
  },
  1009: {
    code: 1009,
    message: '参数错误',
    type: 'error',
    category: 'data',
  },

  // 认证相关错误 (2000-2999)
  2001: {
    code: 2001,
    message: '用户名或密码错误',
    type: 'error',
    category: 'auth',
    isAuthError: true,
    showNotification: true,
  },
  2002: {
    code: 2002,
    message: '用户未登录',
    type: 'warning',
    category: 'auth',
    isAuthError: true,
    showAuthExpiredDialog: true,
    redirectToLogin: true,
  },
  2003: {
    code: 2003,
    message: 'Token为空',
    type: 'warning',
    category: 'auth',
    isAuthError: true,
    showNotification: true,
  },
  2004: {
    code: 2004,
    message: 'Token无效',
    type: 'warning',
    category: 'auth',
    isAuthError: true,
    showAuthExpiredDialog: true,
    redirectToLogin: true,
  },
  2005: {
    code: 2005,
    message: '无权限编辑',
    type: 'error',
    category: 'permission',
  },
  2006: {
    code: 2006,
    message: '无权限删除',
    type: 'error',
    category: 'permission',
  },
  2007: {
    code: 2007,
    message: '无权限创建',
    type: 'error',
    category: 'permission',
  },
  2008: {
    code: 2008,
    message: '权限操作失败',
    type: 'error',
    category: 'permission',
  },

  // 系统错误 (负数)
  [-1]: {
    code: -1,
    message: '未知错误，请联系管理员',
    type: 'error',
    category: 'system',
  },
  [-2]: {
    code: -2,
    message: '登录功能异常，请联系管理员',
    type: 'error',
    category: 'system',
  },
  [-3]: {
    code: -3,
    message: '数据异常，请联系管理员',
    type: 'error',
    category: 'system',
  },
}

/**
 * 认证相关错误码
 */
export const AUTH_ERROR_CODES = [2002, 2003, 2004]

/**
 * 权限相关错误码
 */
export const PERMISSION_ERROR_CODES = [2005, 2006, 2007, 2008]

/**
 * 系统错误码
 */
export const SYSTEM_ERROR_CODES = [-1, -2, -3, 1006, 1008]

/**
 * 数据相关错误码
 */
export const DATA_ERROR_CODES = [1001, 1002, 1003, 1004, 1005, 1007, 1009]

/**
 * HTTP状态码配置
 */
export const HTTP_STATUS_CODE_CONFIG: Record<number, Partial<ErrorCodeConfig>> = {
  401: {
    code: 401,
    message: 'Unauthorized',
    type: 'warning',
    category: 'auth',
    isAuthError: true,
    showAuthExpiredDialog: true,
    redirectToLogin: true,
  },
  403: {
    code: 403,
    message: 'Forbidden',
    type: 'error',
    category: 'permission',
    showNotification: true,
  },
  404: {
    code: 404,
    message: 'Not Found',
    type: 'error',
    category: 'general',
    showNotification: true,
  },
  500: {
    code: 500,
    message: 'Internal Server Error',
    type: 'error',
    category: 'system',
    showNotification: true,
  },
}

/**
 * 错误消息关键词配置
 * 当错误消息包含这些关键词时，会触发相应的行为
 */
export const ERROR_MESSAGE_KEYWORDS = {
  // 认证过期相关关键词
  authExpired: [
    'token 过期',
    'token过期',
    'Token过期',
    'Token 过期',
    'token无效',
    'Token无效',
    'token invalid',
    'Token invalid',
    'unauthorized',
    'Unauthorized',
    '登录过期',
    '登录状态过期',
    '认证过期',
    '身份验证过期',
    '未登录',
    '认证失败',
    'Authentication failed',
    'Token expired',
    'Invalid token',
    'token空白',
    'Token空白',
  ],
  // 权限相关关键词
  permission: [
    '无权限',
    '权限不足',
    'Permission denied',
    'Access denied',
    'Forbidden',
  ],
  // 系统错误关键词
  system: [
    '系统错误',
    'System error',
    'Internal error',
    '服务器错误',
    'Server error',
  ],
}

/**
 * 获取错误码配置
 */
export function getErrorCodeConfig(code: number): ErrorCodeConfig | null {
  return ERROR_CODE_MAP[code] || null
}

/**
 * 获取HTTP状态码配置
 */
export function getHttpStatusConfig(status: number): Partial<ErrorCodeConfig> | null {
  return HTTP_STATUS_CODE_CONFIG[status] || null
}

/**
 * 检查是否为认证错误
 */
export function isAuthenticationError(error: any): boolean {
  // 1. 检查HTTP状态码
  const httpStatus = error?.response?.status
  if (httpStatus) {
    const statusConfig = getHttpStatusConfig(httpStatus)
    if (statusConfig?.isAuthError) {
      return true
    }
  }

  // 2. 检查响应数据中的错误码
  const responseData = error?.response?.data
  if (responseData && typeof responseData === 'object') {
    const responseCode = responseData.code
    if (responseCode) {
      const codeConfig = getErrorCodeConfig(responseCode)
      if (codeConfig?.isAuthError) {
        return true
      }
    }
  }

  // 3. 检查错误结果中的错误码
  const errorResultCode = error?.errorResult?.code
  if (errorResultCode) {
    const codeConfig = getErrorCodeConfig(errorResultCode)
    if (codeConfig?.isAuthError) {
      return true
    }
  }

  // 4. 检查错误对象直接的code属性
  const errorCode = error?.code
  if (errorCode) {
    const codeConfig = getErrorCodeConfig(errorCode)
    if (codeConfig?.isAuthError) {
      return true
    }
  }

  // 5. 检查错误消息关键词
  const errorMessage = error?.message || error?.response?.data?.msg || error?.response?.data?.message || ''
  if (errorMessage && typeof errorMessage === 'string') {
    return checkMessageKeywords(errorMessage, ERROR_MESSAGE_KEYWORDS.authExpired)
  }

  return false
}

/**
 * 检查是否需要显示认证过期弹窗
 */
export function shouldShowAuthExpiredDialog(error: any): boolean {
  // 1. 检查HTTP状态码
  const httpStatus = error?.response?.status
  if (httpStatus) {
    const statusConfig = getHttpStatusConfig(httpStatus)
    if (statusConfig?.showAuthExpiredDialog) {
      return true
    }
  }

  // 2. 检查响应数据中的错误码
  const responseData = error?.response?.data
  if (responseData && typeof responseData === 'object') {
    const responseCode = responseData.code
    if (responseCode) {
      const codeConfig = getErrorCodeConfig(responseCode)
      if (codeConfig?.showAuthExpiredDialog) {
        return true
      }
    }
  }

  // 3. 检查错误结果中的错误码
  const errorResultCode = error?.errorResult?.code
  if (errorResultCode) {
    const codeConfig = getErrorCodeConfig(errorResultCode)
    if (codeConfig?.showAuthExpiredDialog) {
      return true
    }
  }

  // 4. 检查错误对象直接的code属性
  const errorCode = error?.code
  if (errorCode) {
    const codeConfig = getErrorCodeConfig(errorCode)
    if (codeConfig?.showAuthExpiredDialog) {
      return true
    }
  }

  // 5. 检查错误消息关键词
  const errorMessage = error?.message || error?.response?.data?.msg || error?.response?.data?.message || ''
  if (errorMessage && typeof errorMessage === 'string') {
    return checkMessageKeywords(errorMessage, ERROR_MESSAGE_KEYWORDS.authExpired)
  }

  return false
}

/**
 * 检查错误消息是否包含指定关键词
 */
function checkMessageKeywords(message: string, keywords: string[]): boolean {
  if (!message || !keywords.length)
    return false

  const lowerMessage = message.toLowerCase()
  return keywords.some(keyword =>
    lowerMessage.includes(keyword.toLowerCase()),
  )
}

/**
 * 获取错误处理行为配置
 */
export function getErrorBehaviorConfig(error: any): {
  isAuthError: boolean
  showAuthExpiredDialog: boolean
  showNotification: boolean
  redirectToLogin: boolean
  config: ErrorCodeConfig | Partial<ErrorCodeConfig> | null
} {
  let config: ErrorCodeConfig | Partial<ErrorCodeConfig> | null = null

  // 优先检查错误码配置
  const responseCode = error?.response?.data?.code || error?.errorResult?.code || error?.code
  if (responseCode) {
    config = getErrorCodeConfig(responseCode)
  }

  // 如果没有错误码配置，检查HTTP状态码配置
  if (!config) {
    const httpStatus = error?.response?.status
    if (httpStatus) {
      config = getHttpStatusConfig(httpStatus)
    }
  }

  // 如果有配置，直接返回
  if (config) {
    return {
      isAuthError: config.isAuthError || false,
      showAuthExpiredDialog: config.showAuthExpiredDialog || false,
      showNotification: config.showNotification || false,
      redirectToLogin: config.redirectToLogin || false,
      config,
    }
  }

  // 如果没有配置，通过消息关键词判断
  const errorMessage = error?.message || error?.response?.data?.msg || error?.response?.data?.message || ''
  if (errorMessage && typeof errorMessage === 'string') {
    const isAuthExpired = checkMessageKeywords(errorMessage, ERROR_MESSAGE_KEYWORDS.authExpired)
    const isPermissionError = checkMessageKeywords(errorMessage, ERROR_MESSAGE_KEYWORDS.permission)
    const isSystemError = checkMessageKeywords(errorMessage, ERROR_MESSAGE_KEYWORDS.system)

    if (isAuthExpired) {
      return {
        isAuthError: true,
        showAuthExpiredDialog: true,
        showNotification: false,
        redirectToLogin: true,
        config: null,
      }
    }

    if (isPermissionError) {
      return {
        isAuthError: false,
        showAuthExpiredDialog: false,
        showNotification: true,
        redirectToLogin: false,
        config: null,
      }
    }

    if (isSystemError) {
      return {
        isAuthError: false,
        showAuthExpiredDialog: false,
        showNotification: true,
        redirectToLogin: false,
        config: null,
      }
    }
  }

  // 默认行为
  return {
    isAuthError: false,
    showAuthExpiredDialog: false,
    showNotification: true,
    redirectToLogin: false,
    config: null,
  }
}
