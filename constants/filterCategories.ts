import type { FilterCategory } from '~/types/ticket'
import {
  Activity,
  AlertTriangle,
  Archive,
  Briefcase,
  CheckCircle,
  CheckCircle2,
  Clock,
  Code,
  Crown,
  Folder,
  FolderTree,
  Gamepad2,
  Home,
  Layers,
  Mic,
  Shield,
  Smartphone,
  Spade,
  Tag,
  User,
  UserCheck,
  Users,
  XCircle,
  Zap,
} from 'lucide-vue-next'

/**
 * 筛选字段配置 - 定义哪些字段需要生成筛选选项
 */
export interface FilterFieldConfig {
  label: string
  key: string
  /** 数据字段路径，支持嵌套字段如 'owner.0' */
  dataField: string
  /** 是否为数组字段 */
  isArray?: boolean
  /** 静态选项配置（如果提供，则不从数据中动态生成） */
  staticOptions?: Array<{ label: string, value: string, color?: string, icon?: any }>
  /** 是否启用（默认true） */
  enabled?: boolean
  /** 最大显示选项数量（默认无限制） */
  maxOptions?: number
  /** 排序方式：'count'(按数量), 'alpha'(按字母), 'custom'(自定义) */
  sortBy?: 'count' | 'alpha' | 'custom'
  /** 类别图标 */
  icon?: any
}

/**
 * 筛选字段配置
 */
export const filterFieldConfigs: FilterFieldConfig[] = [
  {
    label: '状态',
    key: 'status',
    dataField: 'status', // 对应 ticket.status 或 ticket.stage
    icon: Activity,
    staticOptions: [
      { label: '待处理', value: '待处理', color: '#D73737', icon: Clock },
      { label: '处理中', value: '处理中', color: '#C98600', icon: Zap },
      { label: '已处理', value: '已处理', color: '#28A745', icon: CheckCircle },
      { label: '已归档', value: '已归档', color: '#646469', icon: CheckCircle2 },
    ],
    sortBy: 'custom',
  },
  {
    label: '处理人',
    key: 'handler',
    dataField: 'handler',
    icon: UserCheck,
    sortBy: 'alpha',
  },
  {
    label: '负责人',
    key: 'owner',
    dataField: 'owner',
    icon: Users,
    isArray: true,
    sortBy: 'count',
  },
  {
    label: '业务负责人',
    key: 'businessOwner',
    dataField: 'businessOwner',
    icon: Briefcase,
    isArray: true,
    sortBy: 'count',
  },
  {
    label: '开发负责人',
    key: 'devOwner',
    dataField: 'devOwner',
    icon: Code,
    isArray: true,
    sortBy: 'count',
  },
  {
    label: '标签',
    key: 'labels',
    dataField: 'labels',
    icon: Tag,
    isArray: true,
    sortBy: 'count',
    maxOptions: 20,
  },
  {
    label: '功能类型',
    key: 'functionType',
    dataField: 'functionType',
    icon: Layers,
    isArray: true,
    sortBy: 'count',
  },
  {
    label: '应用',
    key: 'apps',
    dataField: 'apps',
    icon: Smartphone,
    isArray: true,
    sortBy: 'count',
  },
  {
    label: '严重程度',
    key: 'severityLevel',
    dataField: 'severityLevel',
    icon: AlertTriangle,
    sortBy: 'custom',
  },
  {
    label: '一级分类',
    key: 'firstLevelCategory',
    dataField: 'firstLevelCategory',
    icon: FolderTree,
    sortBy: 'alpha',
  },
  {
    label: '二级分类',
    key: 'secondLevelCategory',
    dataField: 'secondLevelCategory',
    icon: Folder,
    sortBy: 'alpha',
  },
]

/**
 * 静态筛选类别配置（向后兼容）
 * @deprecated 建议使用 useDynamicFilters 生成动态筛选
 */
export const filterCategories: FilterCategory[] = [
  {
    label: '状态',
    key: 'status',
    options: [
      { label: '待处理', value: '待处理', color: '#D73737' },
      { label: '处理中', value: '处理中', color: '#C98600' },
      { label: '已处理', value: '已处理', color: '#28A745' },
      { label: '已归档', value: '已归档', color: '#646469' },
    ],
  },
  {
    label: '处理人',
    key: 'handler',
    options: [
      { label: '李四', value: '李四' },
      { label: '赵六', value: '赵六' },
      { label: '张三', value: '张三' },
      { label: '王五', value: '王五' },
      { label: '陈婷', value: '陈婷' },
      { label: '詹明俊', value: '詹明俊' },
      { label: '陈伟贤', value: '陈伟贤' },
    ],
  },
  {
    label: '标签',
    key: 'labels',
    options: [
      { label: '登录问题', value: '登录问题' },
      { label: '网络异常', value: '网络异常' },
      { label: '界面优化', value: '界面优化' },
      { label: 'UI问题', value: 'UI问题' },
      { label: '房间玩法', value: '房间玩法' },
      { label: '游戏体验', value: '游戏体验' },
      { label: '性能问题', value: '性能问题' },
      { label: '功能建议', value: '功能建议' },
    ],
  },
  {
    label: '应用',
    key: 'apps',
    options: [
      { label: 'TT语音', value: 'TT语音' },
      { label: '游戏大厅', value: '游戏大厅' },
      { label: '麻将', value: '麻将' },
      { label: '斗地主', value: '斗地主' },
      { label: '德州扑克', value: '德州扑克' },
      { label: '象棋', value: '象棋' },
    ],
  },
]

/**
 * 状态颜色映射
 */
export const STATUS_COLORS = {
  待处理: '#D73737',
  处理中: '#C98600',
  已处理: '#28A745',
  已归档: '#646469',
  default: '#0078FF',
}

/**
 * 状态颜色映射（RGB格式）
 */
export const STATUS_COLORS_RGB = {
  待处理: 'rgb(215,55,55)',
  处理中: 'rgb(201,134,0)',
  已处理: 'rgb(40,167,69)',
  已归档: 'rgb(100,100,105)',
  default: 'rgb(0,122,255)',
}

/**
 * 获取状态颜色
 * @param status 状态值
 * @returns 对应的颜色代码
 */
export function getStatusColor(status: string): string {
  return (
    STATUS_COLORS[status as keyof typeof STATUS_COLORS] || STATUS_COLORS.default
  )
}

/**
 * 获取状态颜色（RGB格式）
 * @param status 状态值
 * @returns 对应的RGB颜色代码
 */
export function getStatusColorRGB(status: string): string {
  return (
    STATUS_COLORS_RGB[status as keyof typeof STATUS_COLORS_RGB]
    || STATUS_COLORS_RGB.default
  )
}

/**
 * 应用图标映射
 */
export const APP_ICONS = {
  TT语音: Mic,
  游戏大厅: Home,
  麻将: Gamepad2,
  斗地主: Spade,
  德州扑克: Crown,
  象棋: Shield,
  default: Smartphone,
}

/**
 * 标签图标映射
 */
export const LABEL_ICONS = {
  登录问题: User,
  网络异常: XCircle,
  界面优化: Layers,
  UI问题: Layers,
  房间玩法: Gamepad2,
  游戏体验: Gamepad2,
  性能问题: Zap,
  功能建议: Tag,
  default: Tag,
}

/**
 * 严重程度图标映射
 */
export const SEVERITY_ICONS = {
  高: AlertTriangle,
  中: Activity,
  低: Activity,
  default: Activity,
}

/**
 * 获取应用图标
 * @param appName 应用名称
 * @returns 对应的图标组件
 */
export function getAppIcon(appName: string) {
  return APP_ICONS[appName as keyof typeof APP_ICONS] || APP_ICONS.default
}

/**
 * 获取标签图标
 * @param labelName 标签名称
 * @returns 对应的图标组件
 */
export function getLabelIcon(labelName: string) {
  return LABEL_ICONS[labelName as keyof typeof LABEL_ICONS] || LABEL_ICONS.default
}

/**
 * 获取严重程度图标
 * @param severity 严重程度
 * @returns 对应的图标组件
 */
export function getSeverityIcon(severity: string) {
  return SEVERITY_ICONS[severity as keyof typeof SEVERITY_ICONS] || SEVERITY_ICONS.default
}

/**
 * 获取类别标签
 * @param key 类别键
 * @returns 类别标签
 */
export function getCategoryLabel(key: string): string {
  // 首先从动态配置中查找
  const dynamicConfig = filterFieldConfigs.find(config => config.key === key)
  if (dynamicConfig) {
    return dynamicConfig.label
  }

  // 向后兼容：从静态配置中查找
  const category = filterCategories.find(cat => cat.key === key)
  return category ? category.label : key
}

/**
 * 获取类别图标
 * @param key 类别键
 * @returns 类别图标组件
 */
export function getCategoryIcon(key: string) {
  // 首先从动态配置中查找
  const dynamicConfig = filterFieldConfigs.find(config => config.key === key)
  if (dynamicConfig && dynamicConfig.icon) {
    return dynamicConfig.icon
  }

  // 默认图标
  return Activity
}
