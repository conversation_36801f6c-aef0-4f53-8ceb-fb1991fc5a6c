import type { LinkProp } from '~/components/feedback/Nav.vue'
import type { TicketFilterKey } from '~/composables/useTicketFilters'

/**
 * 菜单项配置接口
 */
export interface TicketMenuConfig {
  key: TicketFilterKey
  title: string
  icon: string
  variant: 'default' | 'ghost'
  group: 'status' | 'personal'
  order: number
}

/**
 * 菜单配置数据
 */
export const TICKET_MENU_CONFIGS: TicketMenuConfig[] = [
  // 状态相关菜单
  {
    key: 'all',
    title: '所有工单',
    icon: 'lucide:inbox',
    variant: 'ghost',
    group: 'status',
    order: 1,
  },
  {
    key: 'pending',
    title: '待处理',
    icon: 'lucide:alert-circle',
    variant: 'ghost',
    group: 'status',
    order: 2,
  },
  {
    key: 'processing',
    title: '处理中',
    icon: 'lucide:loader',
    variant: 'ghost',
    group: 'status',
    order: 3,
  },
  {
    key: 'done',
    title: '已处理',
    icon: 'lucide:check-circle',
    variant: 'ghost',
    group: 'status',
    order: 4,
  },
  {
    key: 'archived',
    title: '已归档',
    icon: 'lucide:check-circle-2',
    variant: 'ghost',
    group: 'status',
    order: 5,
  },

  // 个人相关菜单
  {
    key: 'created',
    title: '我创建的',
    icon: 'lucide:user',
    variant: 'ghost',
    group: 'personal',
    order: 1,
  },
  {
    key: 'todo',
    title: '我待办的',
    icon: 'lucide:clock',
    variant: 'ghost',
    group: 'personal',
    order: 2,
  },
  {
    key: 'handled',
    title: '我处理的',
    icon: 'lucide:user-check',
    variant: 'ghost',
    group: 'personal',
    order: 3,
  },
]

/**
 * 根据组获取菜单配置
 */
export function getMenusByGroup(group: 'status' | 'personal'): TicketMenuConfig[] {
  return TICKET_MENU_CONFIGS
    .filter(config => config.group === group)
    .sort((a, b) => a.order - b.order)
}

/**
 * 根据key获取菜单配置
 */
export function getMenuConfig(key: TicketFilterKey): TicketMenuConfig | undefined {
  return TICKET_MENU_CONFIGS.find(config => config.key === key)
}

/**
 * 将菜单配置转换为LinkProp格式
 */
export function configToLinkProp(
  config: TicketMenuConfig,
  count: number,
): LinkProp {
  return {
    title: config.title,
    label: count.toString(),
    icon: config.icon,
    variant: config.variant as const,
    key: config.key,
  }
}

/**
 * 生成状态菜单链接
 */
export function generateStatusLinks(counts: Record<string, number>): LinkProp[] {
  const statusMenus = getMenusByGroup('status')
  return statusMenus.map(config => configToLinkProp(config, counts[config.key] || 0))
}

/**
 * 生成个人菜单链接
 */
export function generatePersonalLinks(counts: Record<string, number>): LinkProp[] {
  const personalMenus = getMenusByGroup('personal')
  return personalMenus.map(config => configToLinkProp(config, counts[config.key] || 0))
}

/**
 * 获取菜单图标
 */
export function getMenuIcon(key: TicketFilterKey): string {
  const config = getMenuConfig(key)
  return config?.icon || 'lucide:inbox'
}

/**
 * 获取菜单标题
 */
export function getMenuTitle(key: TicketFilterKey): string {
  const config = getMenuConfig(key)
  return config?.title || '工单列表'
}

/**
 * 检查菜单是否存在
 */
export function isValidMenuKey(key: string): key is TicketFilterKey {
  return TICKET_MENU_CONFIGS.some(config => config.key === key)
}

/**
 * 获取所有菜单键
 */
export function getAllMenuKeys(): TicketFilterKey[] {
  return TICKET_MENU_CONFIGS.map(config => config.key)
}

/**
 * 获取默认菜单键
 */
export function getDefaultMenuKey(): TicketFilterKey {
  return 'all'
}
