/**
 * 统一的模态对话框系统
 * 用于管理项目中所有的弹窗功能
 */

import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-vue-next'

// 弹窗类型定义
export type ModalType = 'info' | 'success' | 'warning' | 'error' | 'confirm' | 'custom'

// 弹窗配置接口
export interface ModalOptions {
  title?: string
  message?: string
  type?: ModalType
  showCancel?: boolean
  showConfirm?: boolean
  cancelText?: string
  confirmText?: string
  persistent?: boolean
  closable?: boolean
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
  customContent?: any // Vue 组件
  customClass?: string
  // 自定义组件支持
  component?: string
  props?: Record<string, any>
  events?: Record<string, (...args: any[]) => void>
}

// 认证过期弹窗特定选项
export interface AuthExpiredModalOptions extends Omit<ModalOptions, 'type'> {
  redirectPath?: string
  showCancel?: boolean
}

// 全局弹窗状态
interface ModalState {
  isOpen: boolean
  isLoading: boolean
  options: ModalOptions | null
}

// 全局状态，确保同时只有一个弹窗
const modalState = ref<ModalState>({
  isOpen: false,
  isLoading: false,
  options: null,
})

// 防止重复显示弹窗
let isModalShowing = false

// 弹窗解析函数，用于 Vue 组件弹窗
let modalResolve: ((value: boolean) => void) | null = null

export function useModal() {
  /**
   * 显示通用弹窗
   */
  async function showModal(options: ModalOptions): Promise<boolean> {
    // 防止重复显示
    if (isModalShowing || modalState.value.isOpen) {
      return false
    }

    isModalShowing = true
    modalState.value.isOpen = true
    modalState.value.options = {
      showCancel: true,
      showConfirm: true,
      cancelText: '取消',
      confirmText: '确定',
      persistent: false,
      closable: true,
      ...options,
    }

    return new Promise((resolve) => {
      // 设置弹窗解析函数，供 Modal 组件使用
      modalResolve = resolve

      // 弹窗状态已经在上面设置，Modal 组件会自动响应
      // 不再需要区分客户端和服务端，统一使用 Vue 组件方式
    })
  }

  /**
   * 显示确认对话框
   */
  async function confirm(
    message: string,
    title?: string,
    options?: Partial<ModalOptions>,
  ): Promise<boolean> {
    return showModal({
      title: title || '确认',
      message,
      type: 'confirm',
      ...options,
    })
  }

  /**
   * 显示信息对话框
   */
  async function info(
    message: string,
    title?: string,
    options?: Partial<ModalOptions>,
  ): Promise<boolean> {
    return showModal({
      title: title || '提示',
      message,
      type: 'info',
      showCancel: false,
      ...options,
    })
  }

  /**
   * 显示成功对话框
   */
  async function success(
    message: string,
    title?: string,
    options?: Partial<ModalOptions>,
  ): Promise<boolean> {
    return showModal({
      title: title || '成功',
      message,
      type: 'success',
      showCancel: false,
      ...options,
    })
  }

  /**
   * 显示警告对话框
   */
  async function warning(
    message: string,
    title?: string,
    options?: Partial<ModalOptions>,
  ): Promise<boolean> {
    return showModal({
      title: title || '警告',
      message,
      type: 'warning',
      ...options,
    })
  }

  /**
   * 显示错误对话框
   */
  async function error(
    message: string,
    title?: string,
    options?: Partial<ModalOptions>,
  ): Promise<boolean> {
    return showModal({
      title: title || '错误',
      message,
      type: 'error',
      showCancel: false,
      ...options,
    })
  }

  /**
   * 显示认证过期对话框
   */
  async function showAuthExpiredModal(
    options?: AuthExpiredModalOptions,
  ): Promise<boolean> {
    return showModal({
      title: '登录状态已过期',
      message: '您的登录状态已过期，请重新登录以继续使用系统功能。',
      type: 'warning',
      cancelText: '稍后处理',
      confirmText: '立即登录',
      persistent: true,
      closable: false,
      onConfirm: async () => {
        const authStore = useAuthStore()
        const route = useRoute()

        // 清除认证数据
        authStore.clearAuthData()
        authStore.setLoginExpired(true)

        // 跳转到登录页
        await navigateTo({
          path: options?.redirectPath || '/login',
          query: {
            redirect: route.fullPath,
            expired: '1',
          },
        })
      },
      ...options,
    })
  }

  /**
   * 关闭当前弹窗
   */
  function closeModal() {
    modalState.value.isOpen = false
    modalState.value.options = null
    isModalShowing = false

    // 注意：不在这里调用 modalResolve，因为它应该在具体的确认/取消操作中调用
    // 只清理解析函数引用
    modalResolve = null
  }

  /**
   * 隐藏当前弹窗（别名）
   */
  function hideModal() {
    closeModal()
  }

  /**
   * 获取弹窗图标和样式
   */
  function getModalIcon(type: ModalType) {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          iconClass: 'text-green-600',
          bgClass: 'bg-green-50',
        }
      case 'error':
        return {
          icon: XCircle,
          iconClass: 'text-red-600',
          bgClass: 'bg-red-50',
        }
      case 'warning':
        return {
          icon: AlertTriangle,
          iconClass: 'text-yellow-600',
          bgClass: 'bg-yellow-50',
        }
      case 'info':
      case 'confirm':
      default:
        return {
          icon: Info,
          iconClass: 'text-blue-600',
          bgClass: 'bg-blue-50',
        }
    }
  }

  return {
    // 状态
    modalState: readonly(modalState),

    // 方法
    showModal,
    confirm,
    info,
    success,
    warning,
    error,
    showAuthExpiredModal,
    closeModal,
    hideModal,
    getModalIcon,

    // 内部访问器
    getModalResolve: () => modalResolve,
    setModalResolve: (resolve: ((value: boolean) => void) | null) => {
      modalResolve = resolve
    },
  }
}

// 注意：不再需要 createVueModal 和 createNativeModal 函数
// 统一使用 Vue 组件方式，通过全局状态管理

// 原生 DOM 弹窗相关函数已删除，统一使用 Vue 组件方式

// 全局弹窗实例
export const globalModal = useModal()
