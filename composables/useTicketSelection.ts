import type { Ticket } from '~/types/ticket'

/**
 * 工单选择 Composable
 * 管理工单选择状态和相关逻辑
 */
export function useTicketSelection(
  tickets: Ref<Ticket[]>,
  initialTicketId?: string,
) {
  const selectedTicket = ref<string | undefined>(initialTicketId)

  // 监听工单列表变化，当列表为空时清除选中状态
  watch(
    tickets,
    (newTickets) => {
      // 如果工单列表为空，清除选中状态
      if (!newTickets || newTickets.length === 0) {
        selectedTicket.value = undefined
        return
      }

      // 如果当前选中的工单在新列表中不存在，清除选中状态
      if (selectedTicket.value && !findTicketById(selectedTicket.value)) {
        selectedTicket.value = undefined
      }
    },
    { immediate: true },
  )

  /**
   * 根据ID查找工单
   */
  function findTicketById(ticketId: string): Ticket | undefined {
    return tickets.value.find(item =>
      item
      && (
        // 优先使用ticketID进行匹配
        item.ticketID === ticketId
        || item.ticketID?.toString() === ticketId
        || ticketId === item.ticketID?.toString()
        // 兼容性：使用id进行匹配
        || item.id === ticketId
        || item.id.toString() === ticketId
        || ticketId === item.id.toString()
      ),
    )
  }

  /**
   * 检查工单是否存在
   */
  function ticketExists(ticketId: string): boolean {
    return !!findTicketById(ticketId)
  }

  /**
   * 获取当前选中的工单数据
   */
  const selectedTicketData = computed(() => {
    // 如果工单列表为空，直接返回undefined
    if (!tickets.value || tickets.value.length === 0) {
      return undefined
    }

    // 如果没有选中的工单，返回undefined
    if (!selectedTicket.value) {
      return undefined
    }

    // 查找选中的工单
    const foundTicket = findTicketById(selectedTicket.value)

    // 如果选中的工单在当前列表中不存在，返回undefined
    if (!foundTicket) {
      return undefined
    }

    return foundTicket
  })

  /**
   * 设置选中的工单
   */
  function setSelectedTicket(ticketId: string | undefined) {
    if (!ticketId) {
      selectedTicket.value = undefined
      return
    }

    if (ticketExists(ticketId)) {
      selectedTicket.value = ticketId
    }
    else {
      console.warn(`Ticket with ID ${ticketId} not found`)
    }
  }

  /**
   * 选择第一个可用的工单
   */
  function selectFirstTicket(ticketList?: Ticket[]) {
    const list = ticketList || tickets.value
    if (list && list.length > 0 && list[0]?.id) {
      selectedTicket.value = list[0].id
    }
  }

  /**
   * 自动选择工单逻辑
   */
  function autoSelectTicket(
    ticketList: Ticket[],
    hasSpecifiedTicket: boolean = false,
  ) {
    try {
      // 只有在没有指定工单的情况下，才自动选择第一个工单
      if (
        !hasSpecifiedTicket
        && ticketList
        && Array.isArray(ticketList)
        && ticketList.length > 0
        && (!selectedTicket.value
          || !ticketList.some(item =>
            item
            && (
              item.id === selectedTicket.value
              || item.id.toString() === selectedTicket.value
              || selectedTicket.value === item.id.toString()
            ),
          ))
          && ticketList[0]
          && ticketList[0].id
      ) {
        selectedTicket.value = ticketList[0].id
      }
    }
    catch (error) {
      console.error('Error in autoSelectTicket:', error)
    }
  }

  /**
   * 清除选中的工单
   */
  function clearSelection() {
    selectedTicket.value = undefined
  }

  /**
   * 选择下一个工单
   */
  function selectNextTicket(ticketList?: Ticket[]) {
    const list = ticketList || tickets.value
    if (!list || list.length === 0 || !selectedTicket.value)
      return

    const currentIndex = list.findIndex(ticket =>
      ticket.id === selectedTicket.value
      || ticket.id.toString() === selectedTicket.value,
    )

    if (currentIndex >= 0 && currentIndex < list.length - 1) {
      selectedTicket.value = list[currentIndex + 1].id
    }
  }

  /**
   * 选择上一个工单
   */
  function selectPreviousTicket(ticketList?: Ticket[]) {
    const list = ticketList || tickets.value
    if (!list || list.length === 0 || !selectedTicket.value)
      return

    const currentIndex = list.findIndex(ticket =>
      ticket.id === selectedTicket.value
      || ticket.id.toString() === selectedTicket.value,
    )

    if (currentIndex > 0) {
      selectedTicket.value = list[currentIndex - 1].id
    }
  }

  /**
   * 获取选择状态信息
   */
  const selectionInfo = computed(() => {
    const list = tickets.value
    const current = selectedTicketData.value

    if (!current || !list || list.length === 0) {
      return {
        hasSelection: false,
        currentIndex: -1,
        total: list?.length || 0,
        canGoNext: false,
        canGoPrevious: false,
      }
    }

    const currentIndex = list.findIndex(ticket =>
      ticket.id === selectedTicket.value
      || ticket.id.toString() === selectedTicket.value,
    )

    return {
      hasSelection: true,
      currentIndex,
      total: list.length,
      canGoNext: currentIndex >= 0 && currentIndex < list.length - 1,
      canGoPrevious: currentIndex > 0,
    }
  })

  return {
    // 响应式状态
    selectedTicket,
    selectedTicketData,
    selectionInfo,

    // 方法
    findTicketById,
    ticketExists,
    setSelectedTicket,
    selectFirstTicket,
    autoSelectTicket,
    clearSelection,
    selectNextTicket,
    selectPreviousTicket,
  }
}
