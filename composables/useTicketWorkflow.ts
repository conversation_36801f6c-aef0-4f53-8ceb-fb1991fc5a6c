import type { Ticket } from '~/types/ticket'

/**
 * 工单流程步骤定义
 */
export interface WorkflowStep {
  id: string
  title: string
  description: string
  status: 'completed' | 'active' | 'pending' | 'error' | 'skipped'
  timestamp?: string
  operator?: string
  actions?: string[]
  icon?: string
  color?: string
}

/**
 * 工单状态枚举
 */
export enum TicketStatus {
  CREATED = '已创建',
  PENDING = '待处理',
  CLAIMED = '已认领',
  PROCESSING = '处理中',
  PROCESSED = '已处理',
  ARCHIVED = '已归档',
  REJECTED = '已驳回',
  DELETED = '已删除',
}

/**
 * 工单状态映射到流程步骤
 */
export const TICKET_STATUS_MAP = {
  [TicketStatus.CREATED]: 'created',
  [TicketStatus.PENDING]: 'pending',
  [TicketStatus.CLAIMED]: 'claimed',
  [TicketStatus.PROCESSING]: 'processing',
  [TicketStatus.PROCESSED]: 'processed',
  [TicketStatus.ARCHIVED]: 'archived',
  [TicketStatus.REJECTED]: 'rejected',
} as const

/**
 * 工单流程步骤配置接口
 */
interface WorkflowStepConfig {
  id: string
  title: string
  description: string
  icon: string
  color: string
  allowedActions: string[]
  allowedRoles: string[]
}

/**
 * 工单流程步骤配置 - 基于itmp-frontend的6步流程
 */
export const WORKFLOW_STEPS: WorkflowStepConfig[] = [
  {
    id: 'created',
    title: '已创建',
    description: '工单已创建，等待分配处理人',
    icon: 'FileText',
    color: 'blue',
    allowedActions: ['delete', 'assign', 'claim'],
    allowedRoles: ['creator', 'admin', 'manager'],
  },
  {
    id: 'pending',
    title: '待处理',
    description: '工单等待认领或指派处理人',
    icon: 'Clock',
    color: 'orange',
    allowedActions: ['delete', 'assign', 'claim', 'urge'],
    allowedRoles: ['any'],
  },
  {
    id: 'claimed',
    title: '已认领',
    description: '工单已被认领，准备开始处理',
    icon: 'Hand',
    color: 'yellow',
    allowedActions: ['assign', 'start', 'reject'],
    allowedRoles: ['handler', 'admin', 'manager'],
  },
  {
    id: 'processing',
    title: '处理中',
    description: '工单正在处理中',
    icon: 'Play',
    color: 'blue',
    allowedActions: ['complete', 'assign', 'reject', 'urge'],
    allowedRoles: ['handler', 'admin', 'manager'],
  },
  {
    id: 'processed',
    title: '已处理',
    description: '工单处理完成，等待确认归档',
    icon: 'CheckCircle',
    color: 'green',
    allowedActions: ['archive', 'reject'],
    allowedRoles: ['creator', 'admin', 'manager'],
  },
  {
    id: 'archived',
    title: '已归档',
    description: '工单已确认并归档，流程结束',
    icon: 'Archive',
    color: 'gray',
    allowedActions: [],
    allowedRoles: [],
  },
]

/**
 * 工单流程管理 Composable
 */
export function useTicketWorkflow() {
  /**
   * 获取工单当前状态对应的步骤索引
   */
  function getCurrentStepIndex(ticket: Ticket): number {
    const status = ticket.stage || ticket.status || TicketStatus.PENDING

    // 直接根据状态映射查找对应的步骤索引
    const statusKey = TICKET_STATUS_MAP[status as keyof typeof TICKET_STATUS_MAP]

    if (!statusKey) {
      // 如果状态不在映射中，默认返回待处理状态
      return WORKFLOW_STEPS.findIndex(step => step.id === 'pending')
    }

    const stepIndex = WORKFLOW_STEPS.findIndex(step => step.id === statusKey)
    return stepIndex >= 0 ? stepIndex : 1 // 默认为待处理状态索引
  }

  /**
   * 判断工单是否处于特定状态
   */
  function isTicketInStatus(ticket: Ticket, targetStatus: string): boolean {
    const currentStatus = ticket.stage || ticket.status || TicketStatus.PENDING
    return currentStatus === targetStatus
  }

  /**
   * 获取工单的实际状态（优先使用stage字段）
   */
  function getTicketStatus(ticket: Ticket): string {
    return ticket.stage || ticket.status || TicketStatus.PENDING
  }

  /**
   * 获取工单流程步骤列表
   */
  function getWorkflowSteps(ticket: Ticket): WorkflowStep[] {
    const currentStepIndex = getCurrentStepIndex(ticket)
    const ticketStatus = getTicketStatus(ticket)

    return WORKFLOW_STEPS.map((step, index) => {
      let status: WorkflowStep['status'] = 'pending'

      // 确定步骤状态
      if (ticketStatus === TicketStatus.REJECTED) {
        // 驳回状态：当前步骤显示为错误，之前步骤为完成，之后步骤为待处理
        if (index < currentStepIndex) {
          status = 'completed'
        }
        else if (index === currentStepIndex) {
          status = 'error'
        }
        else {
          status = 'pending'
        }
      }
      else if (ticketStatus === TicketStatus.ARCHIVED) {
        // 已归档状态：所有步骤都显示为已完成
        status = 'completed'
      }
      else if (index < currentStepIndex) {
        status = 'completed'
      }
      else if (index === currentStepIndex) {
        status = 'active'
      }
      else {
        status = 'pending'
      }

      // 设置时间戳和操作人信息
      const timestamp = getStepTimestamp(ticket, step.id, status)
      const operator = getStepOperator(ticket, step.id, status)

      return {
        id: step.id,
        title: step.title,
        description: step.description,
        status,
        timestamp,
        operator,
        actions: [...step.allowedActions],
        icon: step.icon,
        color: step.color,
      }
    })
  }

  /**
   * 获取步骤的时间戳
   */
  function getStepTimestamp(ticket: Ticket, stepId: string, status: WorkflowStep['status']): string | undefined {
    // 只有已完成或当前活跃的步骤才显示时间戳
    if (status !== 'completed' && status !== 'active') {
      return undefined
    }

    switch (stepId) {
      case 'created':
        return ticket.enterTime || ticket.createdAt || ticket.date
      case 'pending':
        return ticket.enterTime || ticket.createdAt
      case 'claimed':
        return ticket.responseTime || ticket.enterTime
      case 'processing':
        return ticket.responseTime
      case 'processed':
        return ticket.endTime
      case 'archived':
        return ticket.endTime
      default:
        return undefined
    }
  }

  /**
   * 获取步骤的操作人
   */
  function getStepOperator(ticket: Ticket, stepId: string, status: WorkflowStep['status']): string | undefined {
    // 只有已完成或当前活跃的步骤才显示操作人
    if (status !== 'completed' && status !== 'active') {
      return undefined
    }

    switch (stepId) {
      case 'created':
        return ticket.feedbackPerson || ticket.creator
      case 'pending':
        // 待处理阶段显示"待认领"
        return status === 'active' && !ticket.handler ? '待认领' : ticket.handler || ticket.devProcessor
      case 'claimed':
      case 'processing':
        return ticket.handler || ticket.devProcessor || ticket.owner?.[0]
      case 'processed':
      case 'archived':
        return ticket.handler || ticket.devProcessor
      default:
        return undefined
    }
  }

  /**
   * 获取当前步骤可执行的操作
   */
  function getCurrentStepActions(ticket: Ticket): string[] {
    const currentStepIndex = getCurrentStepIndex(ticket)
    const currentStep = WORKFLOW_STEPS[currentStepIndex]
    return currentStep ? [...currentStep.allowedActions] : []
  }

  /**
   * 检查操作是否在当前步骤允许
   */
  function isActionAllowedInCurrentStep(ticket: Ticket, action: string): boolean {
    const allowedActions = getCurrentStepActions(ticket)
    return allowedActions.includes(action)
  }

  /**
   * 获取步骤进度百分比
   */
  function getProgressPercentage(ticket: Ticket): number {
    const currentStepIndex = getCurrentStepIndex(ticket)
    const totalSteps = WORKFLOW_STEPS.length
    const ticketStatus = getTicketStatus(ticket)

    // 如果是已归档状态，进度为100%
    if (ticketStatus === TicketStatus.ARCHIVED) {
      return 100
    }

    // 如果是已驳回状态，进度为当前步骤的百分比
    if (ticketStatus === TicketStatus.REJECTED) {
      return Math.round((currentStepIndex / totalSteps) * 100)
    }

    // 正常情况：当前步骤完成度
    return Math.round(((currentStepIndex + 1) / totalSteps) * 100)
  }

  /**
   * 获取下一个步骤
   */
  function getNextStep(ticket: Ticket): WorkflowStep | null {
    const currentStepIndex = getCurrentStepIndex(ticket)
    const nextStepIndex = currentStepIndex + 1

    if (nextStepIndex < WORKFLOW_STEPS.length) {
      const nextStep = WORKFLOW_STEPS[nextStepIndex]
      return {
        id: nextStep.id,
        title: nextStep.title,
        description: nextStep.description,
        status: 'pending',
        actions: [...nextStep.allowedActions],
      }
    }

    return null
  }

  /**
   * 获取工单状态的显示信息
   */
  function getStatusDisplay(ticket: Ticket) {
    const status = getTicketStatus(ticket)
    const currentStepIndex = getCurrentStepIndex(ticket)
    const totalSteps = WORKFLOW_STEPS.length

    return {
      status,
      currentStep: currentStepIndex + 1,
      totalSteps,
      percentage: getProgressPercentage(ticket),
      isCompleted: status === TicketStatus.ARCHIVED,
      isRejected: status === TicketStatus.REJECTED,
    }
  }

  /**
   * 根据操作预测下一个状态
   */
  function predictNextStatus(currentStatus: string, action: string): string {
    const statusTransitions: Record<string, Record<string, string>> = {
      [TicketStatus.CREATED]: {
        assign: TicketStatus.CLAIMED,
        claim: TicketStatus.CLAIMED,
        delete: TicketStatus.DELETED,
      },
      [TicketStatus.PENDING]: {
        assign: TicketStatus.CLAIMED,
        claim: TicketStatus.CLAIMED,
        delete: TicketStatus.DELETED,
      },
      [TicketStatus.CLAIMED]: {
        start: TicketStatus.PROCESSING,
        assign: TicketStatus.CLAIMED,
        reject: TicketStatus.REJECTED,
      },
      [TicketStatus.PROCESSING]: {
        complete: TicketStatus.PROCESSED,
        assign: TicketStatus.PROCESSING,
        reject: TicketStatus.REJECTED,
      },
      [TicketStatus.PROCESSED]: {
        archive: TicketStatus.ARCHIVED,
        reject: TicketStatus.REJECTED,
      },
    }

    return statusTransitions[currentStatus]?.[action] || currentStatus
  }

  /**
   * 检查工单是否可以执行特定操作
   */
  function canPerformAction(ticket: Ticket, action: string, userRole?: string): boolean {
    const currentStepIndex = getCurrentStepIndex(ticket)
    const currentStep = WORKFLOW_STEPS[currentStepIndex]

    if (!currentStep)
      return false

    // 检查操作是否在当前步骤的允许操作列表中
    if (!currentStep.allowedActions.includes(action)) {
      return false
    }

    // 检查用户角色权限（如果提供了角色信息）
    if (userRole && currentStep.allowedRoles.length > 0 && !currentStep.allowedRoles.includes('any')) {
      return currentStep.allowedRoles.includes(userRole)
    }

    return true
  }

  return {
    TicketStatus,
    WORKFLOW_STEPS,
    TICKET_STATUS_MAP,
    getCurrentStepIndex,
    getWorkflowSteps,
    getCurrentStepActions,
    isActionAllowedInCurrentStep,
    getProgressPercentage,
    getNextStep,
    getStatusDisplay,
    predictNextStatus,
    canPerformAction,
    isTicketInStatus,
    getTicketStatus,
  }
}
