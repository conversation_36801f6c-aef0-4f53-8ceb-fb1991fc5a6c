import type { FilterFieldConfig } from '~/constants/filterCategories'
import type { FilterCategory, FilterOption, Ticket } from '~/types/ticket'
import {
  filterFieldConfigs,
  getAppIcon,
  getLabelIcon,
  getSeverityIcon,
} from '~/constants/filterCategories'

/**
 * 动态筛选生成器
 * 从票据数据中动态生成筛选选项
 */
export function useDynamicFilters() {
  /**
   * 从对象中获取嵌套字段的值
   * @param obj 对象
   * @param path 字段路径，如 'owner.0' 或 'status'
   * @returns 字段值
   */
  function getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  }

  /**
   * 从票据数组中提取字段的所有唯一值
   * @param tickets 票据数组
   * @param config 字段配置
   * @returns 唯一值数组及其统计信息
   */
  function extractUniqueValues(
    tickets: Ticket[],
    config: FilterFieldConfig,
  ): Array<{ value: string, count: number }> {
    const valueCountMap = new Map<string, number>()

    tickets.forEach((ticket) => {
      // 获取字段值，支持多个可能的字段名
      let fieldValue = getNestedValue(ticket, config.dataField)

      // 特殊处理状态字段：优先使用 stage，然后是 status
      if (config.key === 'status') {
        fieldValue = ticket.stage || ticket.status
      }

      if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
        if (config.isArray && Array.isArray(fieldValue)) {
          // 处理数组字段
          fieldValue.forEach((item: any) => {
            const stringValue = String(item).trim()
            if (stringValue) {
              valueCountMap.set(stringValue, (valueCountMap.get(stringValue) || 0) + 1)
            }
          })
        }
        else {
          // 处理单值字段
          const stringValue = String(fieldValue).trim()
          if (stringValue) {
            valueCountMap.set(stringValue, (valueCountMap.get(stringValue) || 0) + 1)
          }
        }
      }
    })

    return Array.from(valueCountMap.entries()).map(([value, count]) => ({
      value,
      count,
    }))
  }

  /**
   * 对选项进行排序
   * @param options 选项数组
   * @param sortBy 排序方式
   * @param staticOptions 静态选项（用于自定义排序）
   * @returns 排序后的选项数组
   */
  function sortOptions(
    options: Array<{ value: string, count: number }>,
    sortBy: FilterFieldConfig['sortBy'] = 'count',
    staticOptions?: FilterFieldConfig['staticOptions'],
  ): Array<{ value: string, count: number }> {
    switch (sortBy) {
      case 'alpha':
        return options.sort((a, b) => a.value.localeCompare(b.value, 'zh-CN'))

      case 'custom':
        if (staticOptions) {
          // 按照静态选项的顺序排序
          const orderMap = new Map(staticOptions.map((opt, index) => [opt.value, index]))
          return options.sort((a, b) => {
            const orderA = orderMap.get(a.value) ?? Number.MAX_SAFE_INTEGER
            const orderB = orderMap.get(b.value) ?? Number.MAX_SAFE_INTEGER
            return orderA - orderB
          })
        }
        return options.sort((a, b) => b.count - a.count)

      case 'count':
      default:
        return options.sort((a, b) => b.count - a.count)
    }
  }

  /**
   * 生成筛选选项
   * @param tickets 票据数组
   * @param config 字段配置
   * @returns 筛选选项数组
   */
  function generateFilterOptions(
    tickets: Ticket[],
    config: FilterFieldConfig,
  ): FilterOption[] {
    // 如果有静态选项配置，使用静态选项
    if (config.staticOptions) {
      return config.staticOptions.map(option => ({
        ...option,
        count: tickets.filter((ticket) => {
          let fieldValue = getNestedValue(ticket, config.dataField)

          // 特殊处理状态字段
          if (config.key === 'status') {
            fieldValue = ticket.stage || ticket.status
          }

          if (config.isArray && Array.isArray(fieldValue)) {
            return fieldValue.includes(option.value)
          }
          return String(fieldValue) === option.value
        }).length,
      }))
    }

    // 从数据中动态生成选项
    const uniqueValues = extractUniqueValues(tickets, config)
    const sortedValues = sortOptions(uniqueValues, config.sortBy, config.staticOptions)

    // 限制选项数量
    const limitedValues = config.maxOptions
      ? sortedValues.slice(0, config.maxOptions)
      : sortedValues

    return limitedValues.map(({ value, count }) => {
      // 根据字段类型获取合适的图标
      let icon: any
      switch (config.key) {
        case 'apps':
          icon = getAppIcon(value)
          break
        case 'labels':
          icon = getLabelIcon(value)
          break
        case 'severityLevel':
          icon = getSeverityIcon(value)
          break
        default:
          icon = undefined
      }

      return {
        label: value,
        value,
        count,
        icon,
      }
    })
  }

  /**
   * 生成所有筛选类别
   * @param tickets 票据数组
   * @param configs 字段配置数组（可选，默认使用全部配置）
   * @returns 筛选类别数组
   */
  function generateFilterCategories(
    tickets: Ticket[],
    configs: FilterFieldConfig[] = filterFieldConfigs,
  ): FilterCategory[] {
    return configs
      .filter(config => config.enabled !== false)
      .map((config) => {
        const options = generateFilterOptions(tickets, config)

        return {
          label: config.label,
          key: config.key,
          icon: config.icon,
          options: options.filter(option => option.count > 0), // 只显示有数据的选项
        }
      })
      .filter(category => category.options.length > 0) // 只返回有选项的类别
  }

  /**
   * 获取字段的所有可能值（用于搜索建议等）
   * @param tickets 票据数组
   * @param fieldKey 字段键
   * @returns 所有可能值的数组
   */
  function getFieldValues(tickets: Ticket[], fieldKey: string): string[] {
    const config = filterFieldConfigs.find(c => c.key === fieldKey)
    if (!config)
      return []

    const uniqueValues = extractUniqueValues(tickets, config)
    return uniqueValues.map(item => item.value)
  }

  /**
   * 获取筛选统计信息
   * @param tickets 票据数组
   * @returns 统计信息对象
   */
  function getFilterStats(tickets: Ticket[]) {
    const stats: Record<string, Record<string, number>> = {}

    filterFieldConfigs.forEach((config) => {
      if (config.enabled === false)
        return

      const uniqueValues = extractUniqueValues(tickets, config)
      stats[config.key] = Object.fromEntries(
        uniqueValues.map(({ value, count }) => [value, count]),
      )
    })

    return stats
  }

  /**
   * 创建筛选器函数，用于根据筛选条件过滤票据
   * @param filters 筛选条件
   * @returns 筛选器函数
   */
  function createTicketFilter(filters: Record<string, string[]>) {
    return (ticket: Ticket): boolean => {
      return Object.entries(filters).every(([key, values]) => {
        if (!values || values.length === 0)
          return true

        const config = filterFieldConfigs.find(c => c.key === key)
        if (!config)
          return true

        // 获取票据字段值
        let fieldValue = getNestedValue(ticket, config.dataField)

        // 特殊处理状态字段
        if (config.key === 'status') {
          fieldValue = ticket.stage || ticket.status
        }

        if (fieldValue === undefined || fieldValue === null)
          return false

        // 检查匹配
        if (config.isArray && Array.isArray(fieldValue)) {
          return values.some(value => fieldValue.includes(value))
        }
        else {
          return values.includes(String(fieldValue))
        }
      })
    }
  }

  return {
    generateFilterCategories,
    generateFilterOptions,
    getFieldValues,
    getFilterStats,
    extractUniqueValues,
    createTicketFilter,
  }
}
