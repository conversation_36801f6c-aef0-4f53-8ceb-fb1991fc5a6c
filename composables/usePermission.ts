/**
 * 权限检查 composable
 */
export function usePermission() {
  const accessStore = useAccessStore()
  const userStore = useUserStore()

  /**
   * 检查是否有权限码
   */
  const hasPermission = (codes: string | string[]) => {
    return accessStore.hasPermission(codes)
  }

  /**
   * 检查是否有角色
   */
  const hasRole = (roles: string | string[]) => {
    return userStore.hasRole(roles)
  }

  /**
   * 检查是否有任意一个角色
   */
  const hasAnyRole = (roles: string[]) => {
    return userStore.hasAnyRole(roles)
  }

  /**
   * 检查是否有所有角色
   */
  const hasAllRoles = (roles: string[]) => {
    return userStore.hasAllRoles(roles)
  }

  /**
   * 检查是否有访问权限（权限码或角色）
   */
  const hasAccess = (options: {
    codes?: string | string[]
    roles?: string | string[]
    mode?: 'and' | 'or' // 权限码和角色的关系
  }) => {
    const { codes, roles, mode = 'or' } = options

    const hasCodePermission = codes ? hasPermission(codes) : true
    const hasRolePermission = roles ? hasRole(roles) : true

    if (mode === 'and') {
      return hasCodePermission && hasRolePermission
    }
    else {
      return hasCodePermission || hasRolePermission
    }
  }

  /**
   * 权限指令 - 用于 v-permission
   */
  const permissionDirective = {
    mounted(el: HTMLElement, binding: any) {
      const { value } = binding
      if (!value)
        return

      let hasAuth = false

      if (typeof value === 'string') {
        hasAuth = hasPermission(value)
      }
      else if (Array.isArray(value)) {
        hasAuth = hasPermission(value)
      }
      else if (typeof value === 'object') {
        hasAuth = hasAccess(value)
      }

      if (!hasAuth) {
        el.style.display = 'none'
      }
    },
    updated(el: HTMLElement, binding: any) {
      const { value } = binding
      if (!value)
        return

      let hasAuth = false

      if (typeof value === 'string') {
        hasAuth = hasPermission(value)
      }
      else if (Array.isArray(value)) {
        hasAuth = hasPermission(value)
      }
      else if (typeof value === 'object') {
        hasAuth = hasAccess(value)
      }

      el.style.display = hasAuth ? '' : 'none'
    },
  }

  return {
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    hasAccess,
    permissionDirective,
  }
}
