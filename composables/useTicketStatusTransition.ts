import type { Ticket } from '~/types/ticket'
import { TicketStatus, useTicketWorkflow } from '~/composables/useTicketWorkflow'

/**
 * 状态转换规则定义
 */
export interface StatusTransitionRule {
  from: string
  to: string
  action: string
  requiredRoles?: string[]
  conditions?: (ticket: Ticket) => boolean
  description: string
}

/**
 * 工单状态转换管理
 */
export function useTicketStatusTransition() {
  const { getTicketStatus, canPerformAction } = useTicketWorkflow()

  /**
   * 状态转换规则配置
   */
  const transitionRules: StatusTransitionRule[] = [
    // 从已创建状态的转换
    {
      from: TicketStatus.CREATED,
      to: TicketStatus.PENDING,
      action: 'submit',
      description: '提交工单到待处理状态',
    },
    {
      from: TicketStatus.CREATED,
      to: TicketStatus.CLAIMED,
      action: 'assign',
      description: '直接指派工单',
    },

    // 从待处理状态的转换
    {
      from: TicketStatus.PENDING,
      to: TicketStatus.CLAIMED,
      action: 'claim',
      description: '认领工单',
    },
    {
      from: TicketStatus.PENDING,
      to: TicketStatus.CLAIMED,
      action: 'assign',
      description: '指派工单给处理人',
    },

    // 从已认领状态的转换
    {
      from: TicketStatus.CLAIMED,
      to: TicketStatus.PROCESSING,
      action: 'start',
      description: '开始处理工单',
    },
    {
      from: TicketStatus.CLAIMED,
      to: TicketStatus.REJECTED,
      action: 'reject',
      description: '驳回工单',
    },

    // 从处理中状态的转换
    {
      from: TicketStatus.PROCESSING,
      to: TicketStatus.PROCESSED,
      action: 'complete',
      description: '完成工单处理',
      conditions: (ticket) => {
        // 完成工单需要填写处理结果
        return !!(ticket.result && ticket.cause)
      },
    },
    {
      from: TicketStatus.PROCESSING,
      to: TicketStatus.REJECTED,
      action: 'reject',
      description: '驳回工单',
    },

    // 从已处理状态的转换
    {
      from: TicketStatus.PROCESSED,
      to: TicketStatus.ARCHIVED,
      action: 'archive',
      description: '归档工单',
      requiredRoles: ['creator', 'admin', 'manager'],
    },
    {
      from: TicketStatus.PROCESSED,
      to: TicketStatus.REJECTED,
      action: 'reject',
      description: '驳回工单重新处理',
      requiredRoles: ['creator', 'admin', 'manager'],
    },

    // 删除操作（从任何非归档状态）
    {
      from: TicketStatus.CREATED,
      to: TicketStatus.DELETED,
      action: 'delete',
      description: '删除工单',
      requiredRoles: ['creator', 'admin'],
    },
    {
      from: TicketStatus.PENDING,
      to: TicketStatus.DELETED,
      action: 'delete',
      description: '删除工单',
      requiredRoles: ['creator', 'admin'],
    },
    {
      from: TicketStatus.CLAIMED,
      to: TicketStatus.DELETED,
      action: 'delete',
      description: '删除工单',
      requiredRoles: ['creator', 'admin'],
    },
    {
      from: TicketStatus.PROCESSING,
      to: TicketStatus.DELETED,
      action: 'delete',
      description: '删除工单',
      requiredRoles: ['creator', 'admin'],
    },
    {
      from: TicketStatus.PROCESSED,
      to: TicketStatus.DELETED,
      action: 'delete',
      description: '删除工单',
      requiredRoles: ['creator', 'admin'],
    },
  ]

  /**
   * 获取工单可执行的状态转换
   */
  function getAvailableTransitions(ticket: Ticket, userRole?: string): StatusTransitionRule[] {
    const currentStatus = getTicketStatus(ticket)
    
    return transitionRules.filter(rule => {
      // 检查当前状态是否匹配
      if (rule.from !== currentStatus) {
        return false
      }

      // 检查角色权限
      if (rule.requiredRoles && userRole && !rule.requiredRoles.includes(userRole)) {
        return false
      }

      // 检查自定义条件
      if (rule.conditions && !rule.conditions(ticket)) {
        return false
      }

      // 检查工单流程权限
      return canPerformAction(ticket, rule.action, userRole)
    })
  }

  /**
   * 检查是否可以执行特定的状态转换
   */
  function canTransition(ticket: Ticket, action: string, userRole?: string): boolean {
    const availableTransitions = getAvailableTransitions(ticket, userRole)
    return availableTransitions.some(rule => rule.action === action)
  }

  /**
   * 获取状态转换的目标状态
   */
  function getTransitionTarget(currentStatus: string, action: string): string | null {
    const rule = transitionRules.find(r => r.from === currentStatus && r.action === action)
    return rule ? rule.to : null
  }

  /**
   * 获取状态转换的描述
   */
  function getTransitionDescription(currentStatus: string, action: string): string {
    const rule = transitionRules.find(r => r.from === currentStatus && r.action === action)
    return rule ? rule.description : ''
  }

  /**
   * 验证状态转换是否有效
   */
  function validateTransition(ticket: Ticket, action: string, userRole?: string): {
    valid: boolean
    reason?: string
  } {
    const currentStatus = getTicketStatus(ticket)
    const rule = transitionRules.find(r => r.from === currentStatus && r.action === action)

    if (!rule) {
      return {
        valid: false,
        reason: `当前状态 ${currentStatus} 不支持操作 ${action}`,
      }
    }

    if (rule.requiredRoles && userRole && !rule.requiredRoles.includes(userRole)) {
      return {
        valid: false,
        reason: `用户角色 ${userRole} 无权限执行此操作`,
      }
    }

    if (rule.conditions && !rule.conditions(ticket)) {
      return {
        valid: false,
        reason: '不满足执行条件',
      }
    }

    if (!canPerformAction(ticket, action, userRole)) {
      return {
        valid: false,
        reason: '工单流程不允许此操作',
      }
    }

    return { valid: true }
  }

  /**
   * 获取状态的显示信息
   */
  function getStatusDisplayInfo(status: string) {
    const statusInfo = {
      [TicketStatus.CREATED]: { label: '已创建', color: 'blue', description: '工单已创建' },
      [TicketStatus.PENDING]: { label: '待处理', color: 'orange', description: '等待处理' },
      [TicketStatus.CLAIMED]: { label: '已认领', color: 'yellow', description: '已被认领' },
      [TicketStatus.PROCESSING]: { label: '处理中', color: 'blue', description: '正在处理' },
      [TicketStatus.PROCESSED]: { label: '已处理', color: 'green', description: '处理完成' },
      [TicketStatus.ARCHIVED]: { label: '已归档', color: 'gray', description: '已归档' },
      [TicketStatus.REJECTED]: { label: '已驳回', color: 'red', description: '已驳回' },
      [TicketStatus.DELETED]: { label: '已删除', color: 'red', description: '已删除' },
    }

    return statusInfo[status] || { label: status, color: 'gray', description: status }
  }

  return {
    transitionRules,
    getAvailableTransitions,
    canTransition,
    getTransitionTarget,
    getTransitionDescription,
    validateTransition,
    getStatusDisplayInfo,
  }
}
