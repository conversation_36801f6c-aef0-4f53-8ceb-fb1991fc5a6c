import type { Ticket } from '~/types/ticket'

/**
 * 工单筛选器类型定义
 */
export type TicketFilterKey =
  | 'all'
  | 'created'
  | 'todo'
  | 'handled'
  | 'pending'
  | 'processing'
  | 'done'
  | 'archived'

export type TicketFilterFunction = (item: Ticket) => boolean

/**
 * 工单筛选 Composable
 * 提供统一的工单筛选逻辑，避免重复代码
 */
export function useTicketFilters() {
  const authStore = useAuthStore()
  const currentUser = computed(() => authStore.userInfo)

  /**
   * 定义所有筛选器函数
   */
  const ticketFilters: Record<TicketFilterKey, TicketFilterFunction> = {
    all: () => true,
    created: (item: Ticket) =>
      item.feedbackPerson?.toLowerCase() === currentUser.value?.username?.toLowerCase(),
    todo: (item: Ticket) =>
      (item.devProcessor?.toLowerCase() === currentUser.value?.username?.toLowerCase())
      && (item.stage === '待处理' || item.status === '处理中'),
    handled: (item: Ticket) =>
      item.devProcessor?.toLowerCase() === currentUser.value?.username?.toLowerCase(),
    pending: (item: Ticket) => item.stage === '待处理',
    processing: (item: Ticket) => item.stage === '处理中',
    done: (item: Ticket) => item.stage === '已处理',
    archived: (item: Ticket) => item.stage === '已归档',
  }

  /**
   * 根据筛选器key获取筛选函数
   */
  function getFilterFunction(key: TicketFilterKey): TicketFilterFunction {
    return ticketFilters[key] || ticketFilters.all
  }

  /**
   * 应用筛选器到工单列表
   */
  function applyFilter(tickets: Ticket[], filterKey: TicketFilterKey): Ticket[] {
    if (filterKey === 'all') {
      return tickets
    }
    const filterFn = getFilterFunction(filterKey)
    return tickets.filter(filterFn)
  }

  /**
   * 计算筛选后的工单数量
   */
  function getFilteredCount(tickets: Ticket[], filterKey: TicketFilterKey): number {
    return applyFilter(tickets, filterKey).length
  }

  /**
   * 批量计算所有筛选器的工单列表和数量
   */
  function computeTicketData(
    filteredTickets: Ticket[],
    originalTickets: Ticket[],
  ): { lists: Record<string, Ticket[]>, counts: Record<string, number> } {
    const lists: Record<string, Ticket[]> = {}
    const counts: Record<string, number> = {}

    Object.keys(ticketFilters).forEach((key) => {
      const filterKey = key as TicketFilterKey

      // 对于列表，使用已经筛选过的数据
      lists[key] = applyFilter(filteredTickets, filterKey)

      // 对于计数，使用原始数据
      counts[key] = getFilteredCount(originalTickets, filterKey)
    })

    return { lists, counts }
  }

  /**
   * 检查用户是否有权限查看某个筛选器
   */
  function canAccessFilter(filterKey: TicketFilterKey): boolean {
    // 基础筛选器所有人都可以访问
    const publicFilters: TicketFilterKey[] = ['all', 'pending', 'processing', 'done', 'archived']

    if (publicFilters.includes(filterKey)) {
      return true
    }

    // 个人相关筛选器需要登录
    return !!currentUser.value?.username
  }

  /**
   * 获取可用的筛选器列表
   */
  function getAvailableFilters(): TicketFilterKey[] {
    return Object.keys(ticketFilters).filter(key =>
      canAccessFilter(key as TicketFilterKey),
    ) as TicketFilterKey[]
  }

  return {
    ticketFilters,
    getFilterFunction,
    applyFilter,
    getFilteredCount,
    computeTicketData,
    canAccessFilter,
    getAvailableFilters,
    currentUser,
  }
}
