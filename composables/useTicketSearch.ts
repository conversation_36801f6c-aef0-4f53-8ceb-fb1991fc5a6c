import type { Ticket } from '~/types/ticket'

/**
 * 工单搜索 Composable
 * 提供工单搜索和筛选功能
 */
export function useTicketSearch() {
  const searchValue = ref('')
  const activeFilters = ref<Record<string, string[]>>({})

  // 使用动态筛选
  const { createTicketFilter } = useDynamicFilters()

  /**
   * 防抖搜索值
   */
  const debouncedSearch = computed(() => searchValue.value)

  /**
   * 是否有活跃的筛选条件
   */
  const hasActiveFilters = computed(() => {
    return Object.values(activeFilters.value).some(
      arr => Array.isArray(arr) && arr.length > 0,
    )
  })

  /**
   * 搜索字段配置
   */
  const searchFields = [
    'id',
    'ticketID',
    'title',
    'text',
    'problemDescription',
    'date',
    'status',
    'stage',
    'creator',
    'handler',
    'devProcessor',
  ] as const

  /**
   * 数组字段配置
   */
  const arrayFields = [
    'labels',
    'functionType',
  ] as const

  /**
   * 执行文本搜索
   */
  function searchInText(text: string | undefined, searchTerm: string): boolean {
    if (!text)
      return false
    return text.toLowerCase().includes(searchTerm.toLowerCase())
  }

  /**
   * 在数组字段中搜索
   */
  function searchInArray(array: string[] | undefined, searchTerm: string): boolean {
    if (!array || !Array.isArray(array))
      return false
    return array.some(item =>
      item.toLowerCase().includes(searchTerm.toLowerCase()),
    )
  }

  /**
   * 对单个工单执行搜索
   */
  function searchTicket(ticket: Ticket, searchTerm: string): boolean {
    const trimmedSearch = searchTerm.trim().toLowerCase()

    if (!trimmedSearch)
      return true

    // 搜索基础字段
    for (const field of searchFields) {
      const value = ticket[field]
      if (searchInText(value?.toString(), trimmedSearch)) {
        return true
      }
    }

    // 搜索数组字段
    for (const field of arrayFields) {
      const value = ticket[field]
      if (searchInArray(value, trimmedSearch)) {
        return true
      }
    }

    return false
  }

  /**
   * 应用筛选条件到工单列表
   */
  function applyFilters(tickets: Ticket[]): Ticket[] {
    if (!hasActiveFilters.value) {
      return tickets
    }

    const filterFn = createTicketFilter(activeFilters.value)
    return tickets.filter(filterFn)
  }

  /**
   * 应用搜索到工单列表
   */
  function applySearch(tickets: Ticket[]): Ticket[] {
    const searchTerm = debouncedSearch.value?.trim()

    if (!searchTerm) {
      return tickets
    }

    return tickets.filter(ticket => searchTicket(ticket, searchTerm))
  }

  /**
   * 应用所有筛选和搜索条件
   */
  function filterAndSearchTickets(tickets: Ticket[]): Ticket[] {
    // 先应用筛选条件
    let filtered = applyFilters(tickets)

    // 再应用搜索
    filtered = applySearch(filtered)

    return filtered
  }

  /**
   * 处理筛选事件
   */
  function handleFilter(filters: Record<string, string[]>) {
    activeFilters.value = filters
  }

  /**
   * 清除搜索
   */
  function clearSearch() {
    searchValue.value = ''
  }

  /**
   * 清除筛选
   */
  function clearFilters() {
    activeFilters.value = {}
  }

  /**
   * 清除所有筛选和搜索
   */
  function clearAll() {
    clearSearch()
    clearFilters()
  }

  /**
   * 获取搜索统计信息
   */
  function getSearchStats(originalTickets: Ticket[], filteredTickets: Ticket[]) {
    return {
      total: originalTickets.length,
      filtered: filteredTickets.length,
      hasSearch: !!debouncedSearch.value?.trim(),
      hasFilters: hasActiveFilters.value,
      searchTerm: debouncedSearch.value?.trim() || '',
    }
  }

  return {
    // 响应式状态
    searchValue,
    activeFilters,
    debouncedSearch,
    hasActiveFilters,

    // 方法
    searchTicket,
    applyFilters,
    applySearch,
    filterAndSearchTickets,
    handleFilter,
    clearSearch,
    clearFilters,
    clearAll,
    getSearchStats,
  }
}
