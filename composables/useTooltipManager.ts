import { ref, onMounted, onUnmounted, nextTick } from 'vue'

/**
 * 全局tooltip管理器
 * 解决tooltip失效问题
 */
export function useTooltipManager() {
  const isTooltipEnabled = ref(true)
  const tooltipInstances = ref(new Set())
  
  // 重置所有tooltip状态
  const resetTooltips = async () => {
    // 强制关闭所有打开的tooltip
    const tooltipElements = document.querySelectorAll('[data-radix-tooltip-content]')
    tooltipElements.forEach(element => {
      if (element.parentNode) {
        element.parentNode.removeChild(element)
      }
    })
    
    // 清理tooltip触发器状态
    const triggerElements = document.querySelectorAll('[data-radix-tooltip-trigger]')
    triggerElements.forEach(element => {
      element.removeAttribute('data-state')
      element.removeAttribute('aria-describedby')
    })
    
    // 短暂禁用后重新启用tooltip
    isTooltipEnabled.value = false
    await nextTick()
    setTimeout(() => {
      isTooltipEnabled.value = true
    }, 100)
  }
  
  // 检查tooltip是否正常工作
  const checkTooltipHealth = () => {
    const providers = document.querySelectorAll('[data-radix-tooltip-provider]')
    if (providers.length === 0) {
      console.warn('No TooltipProvider found, tooltips may not work properly')
      return false
    }
    
    // 检查是否有多个provider可能造成冲突
    if (providers.length > 1) {
      console.warn(`Multiple TooltipProviders detected (${providers.length}), this may cause conflicts`)
    }
    
    return true
  }
  
  // 修复tooltip状态
  const fixTooltipState = async () => {
    try {
      await resetTooltips()
      await nextTick()
      checkTooltipHealth()
    } catch (error) {
      console.error('Failed to fix tooltip state:', error)
    }
  }
  
  // 监听页面可见性变化，在页面重新可见时修复tooltip
  const handleVisibilityChange = () => {
    if (!document.hidden) {
      setTimeout(fixTooltipState, 100)
    }
  }
  
  // 监听路由变化，在路由变化时修复tooltip
  const handleRouteChange = () => {
    setTimeout(fixTooltipState, 200)
  }
  
  onMounted(() => {
    // 页面加载完成后检查tooltip健康状态
    setTimeout(checkTooltipHealth, 1000)
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 监听自定义路由变化事件
    window.addEventListener('urlchange', handleRouteChange)
    
    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', handleRouteChange)
  })
  
  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('urlchange', handleRouteChange)
    window.removeEventListener('popstate', handleRouteChange)
  })
  
  return {
    isTooltipEnabled,
    resetTooltips,
    fixTooltipState,
    checkTooltipHealth,
  }
}

/**
 * 全局tooltip修复函数
 * 可以在任何地方调用来修复tooltip问题
 */
export const fixGlobalTooltips = async () => {
  const { fixTooltipState } = useTooltipManager()
  await fixTooltipState()
}
