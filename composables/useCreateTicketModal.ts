/**
 * 创建工单模态框管理
 */
export function useCreateTicketModal() {
  const { showModal, hideModal } = useModal()

  /**
   * 显示创建工单模态框
   */
  function showCreateTicketModal() {
    return new Promise<boolean>((resolve) => {
      showModal({
        component: 'CreateTicketForm',
        props: {
          open: true,
        },
        events: {
          'update:open': (open: boolean) => {
            if (!open) {
              hideModal()
              resolve(false)
            }
          },
          'success': () => {
            hideModal()
            resolve(true)
          },
        },
      })
    })
  }

  return {
    showCreateTicketModal,
  }
}
