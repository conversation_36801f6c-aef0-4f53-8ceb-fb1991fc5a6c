import { toast } from '~/components/ui/toast'

interface NotificationOptions {
  title?: string
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  persistent?: boolean
}

export function useNotification() {
  function addNotification(options: NotificationOptions): string {
    const { title, message, type = 'info', duration = 4000, persistent = false } = options

    // 映射类型到 toast 变体
    const getVariant = (type: string) => {
      switch (type) {
        case 'success':
          return 'success' as const
        case 'error':
          return 'destructive' as const
        case 'warning':
          return 'warning' as const
        case 'info':
        default:
          return 'info' as const
      }
    }

    const toastResult = toast({
      title,
      description: message,
      variant: getVariant(type),
      duration: persistent ? Infinity : duration,
      showIcon: true, // 使用 Toast 组件内置的图标
    })

    return toastResult.id
  }

  function success(message: string, title?: string, options?: Partial<NotificationOptions>) {
    return addNotification({
      title,
      message,
      type: 'success',
      ...options,
    })
  }

  function error(message: string, title?: string, options?: Partial<NotificationOptions>) {
    return addNotification({
      title,
      message,
      type: 'error',
      ...options,
    })
  }

  function warning(message: string, title?: string, options?: Partial<NotificationOptions>) {
    return addNotification({
      title,
      message,
      type: 'warning',
      ...options,
    })
  }

  function info(message: string, title?: string, options?: Partial<NotificationOptions>) {
    return addNotification({
      title,
      message,
      type: 'info',
      ...options,
    })
  }

  return {
    addNotification,
    success,
    error,
    warning,
    info,
  }
}

// 全局通知实例
export const globalNotification = useNotification()
