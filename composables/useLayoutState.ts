import type { TicketFilterKey } from './useTicketFilters'
import { getDefaultMenuKey, isValidMenuKey } from '~/constants/ticketMenus'

/**
 * 布局状态管理 Composable
 * 管理侧边栏折叠、菜单选择等布局相关状态
 */
export function useLayoutState(
  initialMenu?: string,
  initialCollapsed?: boolean
) {
  // 侧边栏折叠状态
  const isCollapsed = ref(initialCollapsed || false)
  
  // 移动设备检测
  const isMobile = ref(false)
  
  // 当前选中的菜单
  const selectedMenu = ref<TicketFilterKey>(
    (initialMenu && isValidMenuKey(initialMenu)) 
      ? initialMenu as TicketFilterKey 
      : getDefaultMenuKey()
  )

  // 响应式媒体查询
  const defaultCollapse = useMediaQuery('(max-width: 768px)')

  /**
   * 初始化移动设备检测
   */
  function initMobileDetection() {
    if (import.meta.client) {
      isMobile.value = window.innerWidth < 768
      
      const handleResize = () => {
        isMobile.value = window.innerWidth < 768
      }
      
      window.addEventListener('resize', handleResize)
      
      // 返回清理函数
      return () => {
        window.removeEventListener('resize', handleResize)
      }
    }
    return () => {}
  }

  /**
   * 折叠侧边栏
   */
  function collapse() {
    isCollapsed.value = true
  }

  /**
   * 展开侧边栏
   */
  function expand() {
    isCollapsed.value = false
  }

  /**
   * 切换侧边栏折叠状态
   */
  function toggleCollapse() {
    isCollapsed.value = !isCollapsed.value
  }

  /**
   * 设置菜单选择
   */
  function setSelectedMenu(menu: string) {
    if (isValidMenuKey(menu)) {
      selectedMenu.value = menu as TicketFilterKey
    } else {
      console.warn(`Invalid menu key: ${menu}`)
      selectedMenu.value = getDefaultMenuKey()
    }
  }

  /**
   * 重置到默认菜单
   */
  function resetToDefaultMenu() {
    selectedMenu.value = getDefaultMenuKey()
  }

  /**
   * 监听默认折叠状态变化
   */
  watch(defaultCollapse, (newValue) => {
    isCollapsed.value = newValue
  })

  /**
   * 获取布局配置
   */
  const layoutConfig = computed(() => ({
    isCollapsed: isCollapsed.value,
    isMobile: isMobile.value,
    selectedMenu: selectedMenu.value,
    canCollapse: !isMobile.value, // 移动设备上不允许手动折叠
  }))

  /**
   * 获取面板尺寸配置
   */
  function getPanelSizes(defaultLayout: number[] = [15, 30, 55]) {
    return {
      nav: defaultLayout[0],
      list: defaultLayout[1], 
      detail: defaultLayout[2],
    }
  }

  /**
   * 获取导航面板配置
   */
  const navPanelConfig = computed(() => ({
    defaultSize: 15,
    collapsedSize: 4,
    minSize: 15,
    maxSize: 20,
    collapsible: true,
  }))

  /**
   * 获取列表面板配置
   */
  const listPanelConfig = computed(() => ({
    defaultSize: 30,
    minSize: 25,
  }))

  /**
   * 获取详情面板配置
   */
  const detailPanelConfig = computed(() => ({
    defaultSize: 55,
    minSize: 40,
    visible: !isMobile.value, // 移动设备上隐藏详情面板
  }))

  /**
   * 处理窗口可见性变化
   */
  function handleVisibilityChange() {
    if (import.meta.client && document.hidden) {
      // 页面隐藏时的处理逻辑
      console.debug('Page hidden')
    }
  }

  /**
   * 初始化布局
   */
  function initLayout() {
    const cleanupMobile = initMobileDetection()
    
    if (import.meta.client) {
      document.addEventListener('visibilitychange', handleVisibilityChange)
    }
    
    // 返回清理函数
    return () => {
      cleanupMobile()
      if (import.meta.client) {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
      }
    }
  }

  return {
    // 响应式状态
    isCollapsed,
    isMobile,
    selectedMenu,
    layoutConfig,
    navPanelConfig,
    listPanelConfig,
    detailPanelConfig,
    
    // 方法
    collapse,
    expand,
    toggleCollapse,
    setSelectedMenu,
    resetToDefaultMenu,
    getPanelSizes,
    initLayout,
  }
}
