import type { Ticket } from '~/types/ticket'

import {
  assignTicket,
  checkTicket,
  completeTicket,
  deleteTicket,
  handleTicket,
  rejectTicket,
  urgeTicket,
} from '~/api/feedback/ticket'
import { useNotification } from '~/composables/useNotification'

/**
 * 工单操作权限配置
 */
interface TicketActionPermissions {
  /** 删除工单权限 */
  delete: string[]
  /** 认领工单权限 */
  handle: string[]
  /** 指派工单权限 */
  assign: string[]
  /** 完成工单权限 */
  complete: string[]
  /** 归档工单权限 */
  check: string[]
  /** 驳回工单权限 */
  reject: string[]
  /** 加急工单权限 */
  urge: string[]
}

/**
 * 默认权限配置 - 根据项目实际角色调整
 */
const DEFAULT_PERMISSIONS: TicketActionPermissions = {
  // 删除：超级管理员、管理员、创建人可以删除任何状态的工单
  delete: ['Super', 'Admin'],

  // 认领：所有用户都可以认领待处理的工单
  handle: ['*'],

  // 指派：管理员、创建人可以指派工单给其他人
  assign: ['Super', 'Admin'],

  // 完成：处理人、管理员可以完成工单
  complete: ['*'],

  // 归档：管理员、创建人可以归档已处理的工单
  check: ['Super', 'Admin'],

  // 驳回：只有创建人可以驳回已处理的工单（通过特殊权限检查）
  reject: [],

  // 加急：所有用户都可以加急工单
  urge: ['*'],
}

/**
 * 工单操作 Composable - 统一的工单操作管理系统
 * 集成了权限检查、状态验证、操作处理和刷新管理
 */
export function useTicketOperations(permissions: Partial<TicketActionPermissions> = {}) {
  const { success, error, info, warning } = useNotification()

  // 获取用户信息
  const userStore = useUserStore()

  // 合并权限配置
  const actionPermissions = { ...DEFAULT_PERMISSIONS, ...permissions }

  /**
   * 获取用户角色信息 - 统一的角色获取逻辑
   */
  function getUserRoles(): string[] {
    // 优先从 userStore 获取
    if (userStore.userRoles && userStore.userRoles.length > 0) {
      return userStore.userRoles
    }

    // 从 userStore.userInfo.roles 获取
    if (userStore.userInfo?.roles && userStore.userInfo.roles.length > 0) {
      return userStore.userInfo.roles
    }

    // 从 authStore 获取
    try {
      const authStore = useAuthStore()
      if (authStore.userInfo?.roles && authStore.userInfo.roles.length > 0) {
        return authStore.userInfo.roles
      }
      if (authStore.userInfo?.role) {
        return [authStore.userInfo.role]
      }
    }
    catch (error) {
      console.warn('Failed to get user roles from authStore:', error)
    }

    // 默认返回普通用户角色
    return ['User']
  }

  /**
   * 获取当前用户名
   */
  function getCurrentUsername(): string {
    return userStore.username || userStore.userInfo?.username || ''
  }

  /**
   * 检查用户是否是工单创建人
   */
  function isCreator(ticket: Ticket): boolean {
    const currentUser = getCurrentUsername()
    return ticket.creator === currentUser || ticket.feedbackPerson === currentUser
  }

  /**
   * 检查操作权限 - 根据项目实际需求调整
   */
  function checkPermission(action: keyof TicketActionPermissions, ticket?: Ticket): boolean {
    const requiredRoles = actionPermissions[action]

    // 如果权限配置为 ['*']，表示所有用户都有权限
    if (requiredRoles.includes('*')) {
      // 对于需要特殊检查的操作，即使是 '*' 权限也要进行额外验证
      if (action === 'complete' && ticket) {
        return checkCompletePermission(ticket)
      }
      return true
    }

    // 获取用户角色
    const userRoles = getUserRoles()

    // 检查用户角色权限
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role))

    // 创建人特殊权限检查
    if (ticket && isCreator(ticket)) {
      // 创建人可以指派、归档自己创建的工单
      if (['assign', 'check'].includes(action)) {
        return true
      }
      // 驳回操作需要特殊检查
      if (action === 'reject') {
        return checkRejectPermission(ticket)
      }
    }

    if (!hasRequiredRole) {
      return false
    }

    // 特殊权限检查
    switch (action) {
      case 'complete':
        return ticket ? checkCompletePermission(ticket) : false
      case 'delete':
        return ticket ? checkDeletePermission(ticket) : false
      case 'reject':
        return ticket ? checkRejectPermission(ticket) : false
      default:
        return true
    }
  }

  /**
   * 检查完成工单权限 - 需要是处理人或管理员
   */
  function checkCompletePermission(ticket: Ticket): boolean {
    const currentUser = getCurrentUsername()
    const userRoles = getUserRoles()

    // 管理员和超级管理员可以完成任何工单
    if (userRoles.includes('Super') || userRoles.includes('Admin')) {
      return true
    }

    // 处理人可以完成自己的工单
    const isHandler = ticket.handler === currentUser || ticket.devProcessor === currentUser
    return isHandler
  }

  /**
   * 检查删除工单权限 - 超级管理员、管理员和创建人可以删除任何状态的工单
   */
  function checkDeletePermission(ticket: Ticket): boolean {
    const currentUser = getCurrentUsername()
    const userRoles = getUserRoles()

    // 管理员和超级管理员可以删除任何状态的工单
    if (userRoles.includes('Super') || userRoles.includes('Admin')) {
      return true
    }

    // 创建人可以删除自己创建的任何状态的工单
    const isTicketCreator = ticket.creator === currentUser || ticket.feedbackPerson === currentUser

    return isTicketCreator
  }

  /**
   * 检查驳回工单权限 - 只有创建人可以驳回已处理状态的工单
   */
  function checkRejectPermission(ticket: Ticket): boolean {
    // 只有创建人可以驳回
    if (!isCreator(ticket)) {
      return false
    }

    // 只有已处理状态的工单可以驳回
    const status = ticket.stage || ticket.status || '待处理'
    return status === '已处理'
  }

  /**
   * 检查工单状态是否允许执行指定操作 - 基于 itmp-frontend 项目的状态流转规则
   */
  function checkTicketStatus(action: keyof TicketActionPermissions, ticket: Ticket): boolean {
    const status = ticket.stage || ticket.status || '待处理'

    // 基于项目实际需求的状态流转规则
    const statusRules: Record<string, string[]> = {
      // 删除：任何状态都可以删除（权限由角色控制）
      delete: ['待处理', '已创建', '处理中', '已处理', '已归档'],

      // 认领：只能认领待处理状态的工单
      handle: ['待处理', '已创建'],

      // 指派：可以在待处理和处理中状态下指派
      assign: ['待处理', '已创建', '处理中'],

      // 完成：认领后就可以完成工单（待处理和处理中状态）
      complete: ['待处理', '处理中'],

      // 归档：只能归档已处理状态的工单
      check: ['已处理'],

      // 驳回：只有已处理状态的工单可以驳回
      reject: ['已处理'],

      // 加急：可以在待处理和处理中状态下加急
      urge: ['待处理', '已创建', '处理中'],
    }

    const allowedStatuses = statusRules[action] || []
    const isAllowed = allowedStatuses.includes(status)

    // 额外的状态检查逻辑
    // 注意：认领后就可以完成工单，不需要额外的处理人检查

    if (action === 'check' && isAllowed) {
      // 归档工单时，必须有处理结果
      const hasResult = !!(ticket.result || ticket.cause)
      return hasResult
    }

    return isAllowed
  }

  /**
   * 刷新工单数据
   */
  async function refreshTicketData(
    refreshListCallback?: () => void | Promise<void>,
    currentTicketId?: string,
    updateCurrentTicketCallback?: (ticket: Ticket) => void,
  ) {
    try {
      // 1. 刷新工单列表
      if (refreshListCallback) {
        await refreshListCallback()
      }

      // 2. 如果有当前工单ID，重新获取工单详情
      if (currentTicketId && updateCurrentTicketCallback) {
        // 这里可以调用API重新获取工单详情
        // 暂时通过刷新列表来更新数据
      }
    }
    catch (error) {
      console.error('刷新工单数据失败:', error)
    }
  }

  /**
   * 操作成功后的统一刷新处理
   */
  function handleOperationSuccess(
    _operationName: string,
    refreshListCallback?: () => void | Promise<void>,
    currentTicketId?: string,
    updateCurrentTicketCallback?: (ticket: Ticket) => void,
  ) {
    // 延迟一点时间确保后端数据已更新
    setTimeout(() => {
      refreshTicketData(refreshListCallback, currentTicketId, updateCurrentTicketCallback)
    }, 500)
  }

  /**
   * 通用的工单操作处理函数
   */
  async function handleTicketOperation(
    action: keyof TicketActionPermissions,
    apiFunction: (...args: any[]) => Promise<any>,
    ticket: Ticket,
    options: {
      onBeforeMessage: string
      onSuccessMessage: string
      onErrorMessage: string
      onFinally?: () => void
      extraData?: any
      skipPermissionCheck?: boolean
      skipStatusCheck?: boolean
    },
  ): Promise<boolean> {
    // 权限检查
    if (!options.skipPermissionCheck && !checkPermission(action, ticket)) {
      error('您没有执行此操作的权限', '权限不足')
      return false
    }

    // 状态检查
    if (!options.skipStatusCheck && !checkTicketStatus(action, ticket)) {
      warning('当前工单状态不允许执行此操作', '状态错误')
      return false
    }
    try {
      // 显示操作开始提示
      info(options.onBeforeMessage, '工单操作')

      // 准备请求数据
      const requestData = options.extraData ? { ...ticket, ...options.extraData } : ticket

      // 调用API
      const response = await apiFunction(requestData)

      // 处理响应
      // 注意：响应拦截器已经处理过数据，成功时返回的是 data.data
      // 如果到这里说明API调用成功（没有抛出异常）

      if (apiFunction === urgeTicket) {
        // 加急工单有特殊的处理逻辑
        // 检查是否有加急限制信息
        if (response && typeof response === 'object' && 'lastUrgeTime' in response) {
          error(`加急失败，距离上次加急 ${response.lastUrgeTime} 小于 1 小时，请 ${response.availableTime} 后再使用。`)

          return false
        }
        else {
          // 没有限制信息，说明加急成功
          success(options.onSuccessMessage, '操作成功')
          return true
        }
      }
      else {
        // 其他操作的通用处理
        // 由于响应拦截器已经处理过，能到这里说明操作成功
        success(options.onSuccessMessage, '操作成功')
        return true
      }
    }
    catch (err: any) {
      console.error('工单操作错误:', err)
      const errorMsg = err?.response?.data?.msg || err?.message || options.onErrorMessage
      error(errorMsg, '操作失败')
      return false
    }
    finally {
      // 延迟执行完成回调，确保用户看到操作提示
      if (options.onFinally) {
        setTimeout(() => {
          options.onFinally?.()
        }, 500)
      }
    }
  }

  /**
   * 认领工单
   */
  async function handleTicketAction(ticket: Ticket, onFinally?: () => void): Promise<boolean> {
    return handleTicketOperation('handle', handleTicket, ticket, {
      onBeforeMessage: '认领中，请稍后...',
      onSuccessMessage: '认领完成',
      onErrorMessage: '认领失败',
      onFinally,
    })
  }

  /**
   * 驳回工单
   */
  async function rejectTicketAction(ticket: Ticket, reason: string = '', onFinally?: () => void): Promise<boolean> {
    // 驳回不需要填写原因，直接执行
    return handleTicketOperation('reject', rejectTicket, ticket, {
      onBeforeMessage: '驳回中，请稍后...',
      onSuccessMessage: '驳回完成',
      onErrorMessage: '驳回失败',
      extraData: { reason: reason || '' },
      onFinally,
    })
  }

  /**
   * 归档工单
   */
  async function checkTicketAction(ticket: Ticket, onFinally?: () => void): Promise<boolean> {
    return handleTicketOperation('check', checkTicket, ticket, {
      onBeforeMessage: '归档中，请稍后...',
      onSuccessMessage: '归档完成',
      onErrorMessage: '归档失败',
      onFinally,
    })
  }

  /**
   * 加急工单
   */
  async function urgeTicketAction(ticket: Ticket, onFinally?: () => void): Promise<boolean> {
    return handleTicketOperation('urge', urgeTicket, ticket, {
      onBeforeMessage: '加急中，请稍后...',
      onSuccessMessage: '加急完成',
      onErrorMessage: '加急失败',
      onFinally,
    })
  }

  /**
   * 删除工单
   */
  async function deleteTicketAction(ticket: Ticket, onFinally?: () => void): Promise<boolean> {
    return handleTicketOperation('delete', deleteTicket, ticket, {
      onBeforeMessage: '删除中，请稍后...',
      onSuccessMessage: '删除完成',
      onErrorMessage: '删除失败',
      onFinally,
    })
  }

  /**
   * 指派工单
   */
  async function assignTicketAction(
    ticket: Ticket,
    assigneeParam: { label: string, value: string, key: string, option: any, originLabel: string },
    apps?: string[],
    onFinally?: () => void,
  ): Promise<boolean> {
    if (!assigneeParam || !assigneeParam.option?.user_name) {
      error('请选择指派人', '参数错误')
      return false
    }

    // 指派使用完整的用户信息对象格式
    return handleTicketOperation('assign', assignTicket, ticket, {
      onBeforeMessage: '指派中，请稍后...',
      onSuccessMessage: '指派完成',
      onErrorMessage: '指派失败',
      extraData: {
        assignee: assigneeParam,
        ticketID: ticket.ticketID,
        apps: apps || ticket.apps,
      },
      onFinally,
    })
  }

  /**
   * 完成工单
   */
  async function completeTicketAction(
    ticket: Ticket,
    result?: string,
    cause?: string,
    onFinally?: () => void,
  ): Promise<boolean> {
    // 完成工单需要填写问题原因和处理结果
    if (!result?.trim()) {
      error('请填写处理结果', '参数错误')
      return false
    }

    if (!cause?.trim()) {
      error('请填写问题原因', '参数错误')
      return false
    }

    return handleTicketOperation('complete', completeTicket, ticket, {
      onBeforeMessage: '完成中，请稍后...',
      onSuccessMessage: '完成工单成功',
      onErrorMessage: '完成工单失败',
      extraData: { result, cause },
      onFinally,
    })
  }

  /**
   * 批量操作工单
   */
  async function batchOperation(
    tickets: Ticket[],
    operation: (ticket: Ticket) => Promise<boolean>,
    operationName: string,
  ): Promise<{ success: number, failed: number }> {
    let successCount = 0
    let failedCount = 0

    info(`开始批量${operationName}，共 ${tickets.length} 个工单`, '批量操作')

    for (const ticket of tickets) {
      const result = await operation(ticket)
      if (result) {
        successCount++
      }
      else {
        failedCount++
      }
    }

    if (failedCount === 0) {
      success(`批量${operationName}完成，成功处理 ${successCount} 个工单`, '批量操作成功')
    }
    else {
      error(`批量${operationName}完成，成功 ${successCount} 个，失败 ${failedCount} 个`, '批量操作完成')
    }

    return { success: successCount, failed: failedCount }
  }

  /**
   * 获取工单可执行的操作列表
   */
  function getAvailableActions(ticket: Ticket): Array<{
    action: keyof TicketActionPermissions
    label: string
    icon: string
    variant: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
    disabled: boolean
    reason: string
  }> {
    const actions = [
      { action: 'handle' as const, label: '认领', icon: 'Hand', variant: 'default' as const },
      { action: 'assign' as const, label: '指派', icon: 'UserPlus', variant: 'default' as const },
      { action: 'complete' as const, label: '完成', icon: 'CheckCircle', variant: 'default' as const },
      { action: 'reject' as const, label: '驳回', icon: 'X', variant: 'destructive' as const },
      { action: 'check' as const, label: '归档', icon: 'Archive', variant: 'secondary' as const },
      { action: 'urge' as const, label: '加急', icon: 'Zap', variant: 'outline' as const },
      { action: 'delete' as const, label: '删除', icon: 'Trash2', variant: 'destructive' as const },
    ]

    return actions.map(({ action, label, icon, variant }) => {
      const hasPermission = checkPermission(action, ticket)
      const hasValidStatus = checkTicketStatus(action, ticket)
      const disabled = !hasPermission || !hasValidStatus

      let reason = ''
      if (!hasPermission) {
        reason = `无${label}权限`
      }
      else if (!hasValidStatus) {
        reason = `当前状态不允许${label}`
      }
      else {
        reason = `${label}工单`
      }

      return {
        action,
        label,
        icon,
        variant,
        disabled,
        reason,
      }
    })
  }

  /**
   * 获取详细的操作提示信息
   */
  function getActionTooltip(action: keyof TicketActionPermissions, ticket: Ticket): string {
    const hasPermission = checkPermission(action, ticket)
    const hasValidStatus = checkTicketStatus(action, ticket)
    const currentUser = getCurrentUsername()
    const userRoles = getUserRoles()
    const status = ticket.stage || ticket.status || '待处理'

    // 如果可以执行操作，返回操作说明
    if (hasPermission && hasValidStatus) {
      const actionLabels = {
        handle: '认领工单',
        assign: '指派工单给其他人处理',
        complete: '完成工单处理',
        reject: '驳回工单到处理中状态',
        check: '归档工单',
        urge: '加急处理工单',
        delete: '删除工单',
      }
      return actionLabels[action] || '执行操作'
    }

    // 权限限制提示
    if (!hasPermission) {
      const requiredRoles = actionPermissions[action]

      // 特殊权限检查的详细提示
      if (action === 'complete') {
        if (!checkCompletePermission(ticket)) {
          const isHandler = ticket.handler === currentUser || ticket.devProcessor === currentUser
          if (!isHandler) {
            return `权限限制：只有工单处理人(${ticket.handler || ticket.devProcessor || '未分配'})或管理员可以完成工单`
          }
        }
      }

      if (action === 'delete') {
        if (!checkDeletePermission(ticket)) {
          const isCreator = ticket.creator === currentUser || ticket.feedbackPerson === currentUser
          if (!isCreator && !userRoles.includes('Super') && !userRoles.includes('Admin')) {
            return `权限限制：只有工单创建人(${ticket.feedbackPerson || '未知'})、管理员或超级管理员可以删除工单`
          }
        }
      }

      if (action === 'reject') {
        if (!checkRejectPermission(ticket)) {
          const isCreator = ticket.creator === currentUser || ticket.feedbackPerson === currentUser
          if (!isCreator) {
            return `权限限制：只有工单创建人(${ticket.feedbackPerson || '未知'})可以驳回工单`
          }
          if (status !== '已处理') {
            return `流程限制：只有"已处理"状态的工单可以驳回，当前状态：${status}`
          }
        }
      }

      // 通用权限提示
      if (requiredRoles.includes('*')) {
        return `权限限制：当前操作受特殊条件限制`
      }
      else {
        const roleNames: Record<string, string> = {
          Super: '超级管理员',
          Admin: '管理员',
          User: '普通用户',
        }
        const requiredRoleNames = requiredRoles.map(role => roleNames[role] || role).join('、')
        const currentRoleNames = userRoles.map(role => roleNames[role] || role).join('、') || '无角色'
        return `权限限制：需要${requiredRoleNames}权限，当前权限：${currentRoleNames}`
      }
    }

    // 流程限制提示
    if (!hasValidStatus) {
      const statusRules: Record<string, string[]> = {
        delete: ['已创建', '待处理', '处理中', '已处理', '已归档'],
        handle: ['已创建', '待处理'],
        assign: ['已创建', '待处理', '处理中'],
        complete: ['待处理', '处理中'],
        check: ['已处理'],
        reject: ['已处理'],
        urge: ['已创建', '待处理', '处理中'],
      }

      const allowedStatuses = statusRules[action] || []
      const allowedStatusText = allowedStatuses.join('、')

      // 特殊的流程检查
      // 注意：认领后就可以完成工单，不需要额外的处理人检查

      if (action === 'check' && allowedStatuses.includes(status)) {
        const hasResult = !!(ticket.result || ticket.cause)
        if (!hasResult) {
          return `流程限制：归档工单前需要先填写处理结果`
        }
      }

      return `流程限制：当前状态"${status}"不允许此操作，允许的状态：${allowedStatusText}`
    }

    return '无法执行此操作'
  }

  /**
   * 检查工单是否可以执行指定操作
   */
  function canExecuteAction(action: keyof TicketActionPermissions, ticket: Ticket): {
    allowed: boolean
    reason: string
  } {
    const hasPermission = checkPermission(action, ticket)
    const hasValidStatus = checkTicketStatus(action, ticket)

    if (!hasPermission) {
      return { allowed: false, reason: '您没有执行此操作的权限' }
    }

    if (!hasValidStatus) {
      return { allowed: false, reason: '当前工单状态不允许执行此操作' }
    }

    return { allowed: true, reason: '可以执行此操作' }
  }

  return {
    // 权限和状态检查
    checkPermission,
    checkTicketStatus,
    getUserRoles,
    getCurrentUsername,
    getAvailableActions,
    getActionTooltip,
    canExecuteAction,
    isCreator,

    // 刷新管理
    refreshTicketData,
    handleOperationSuccess,

    // 单个操作
    handleTicketAction,
    rejectTicketAction,
    checkTicketAction,
    urgeTicketAction,
    deleteTicketAction,
    assignTicketAction,
    completeTicketAction,

    // 批量操作
    batchOperation,

    // 通用操作处理器
    handleTicketOperation,
  }
}
