# 工单选择逻辑优化

## 问题描述

1. **通过链接访问工单详情页需要自动选中对应的工单**
   - 当用户直接访问 `http://localhost:3000/feedback/ticket-detail/1749714907000030` 时，需要自动选中对应的工单

2. **切换菜单时自动选中第一个，但某个菜单内工单数为0时，工单详情应为空**
   - 当切换到没有工单的菜单时，工单详情页应该显示空状态
   - 当切换到有工单的菜单时，应该自动选中第一个工单

## 解决方案

### 1. 修改 `TicketList.vue`

**主要改进：**
- 添加了工单列表变化的监听器，当列表为空时自动清除选中状态
- 优化了工单选择逻辑，确保在路由参数变化时能正确选中工单
- 改进了URL更新逻辑，当没有选中工单时重置URL到 `/feedback/`

**关键代码：**
```vue
// 监听工单列表变化，处理工单选择逻辑
watch(
  () => props.items,
  (newItems) => {
    // 如果工单列表为空，清除选中状态
    if (!newItems || newItems.length === 0) {
      if (selectedTicket.value) {
        selectedTicket.value = null
        // 清除URL中的工单ID
        if (import.meta.client) {
          const currentPath = window.location.pathname
          if (currentPath.includes('/feedback/ticket-detail/')) {
            window.history.replaceState({}, '', '/feedback/')
            window.dispatchEvent(new CustomEvent('urlchange'))
          }
        }
      }
      return
    }

    // 重新初始化选中的工单
    initializeSelectedTicket()
  },
  { immediate: true }
)
```

### 2. 修改 `Layout.vue`

**主要改进：**
- 优化了工单列表变化时的选择逻辑
- 改进了对指定工单ID的处理
- 确保当工单列表为空时，正确清除选中状态并更新URL

**关键代码：**
```vue
// 监听当前列表变化，更新选中的工单
watch(
  currentTicketList,
  (newList) => {
    // 如果列表为空，清除选中的工单
    if (!newList || !Array.isArray(newList) || newList.length === 0) {
      // 清除选中状态
      selectedTicket.value = undefined
      
      // 清除URL中的工单ID，重置到 /feedback/
      if (import.meta.client && !hasSpecifiedTicket) {
        const currentPath = window.location.pathname
        if (currentPath.includes('/feedback/ticket-detail/')) {
          window.history.replaceState({}, '', '/feedback/')
          window.dispatchEvent(new CustomEvent('urlchange'))
        }
      }
      return
    }

    // 如果有指定的工单ID，优先使用它
    if (hasSpecifiedTicket) {
      const specifiedTicket = newList.find(
        item =>
          item
          && (item.ticketID === props.selectedTicketId
            || item.ticketID?.toString() === props.selectedTicketId
            || item.id === props.selectedTicketId
            || item.id.toString() === props.selectedTicketId),
      )
      if (specifiedTicket) {
        const ticketId = specifiedTicket.ticketID || specifiedTicket.id
        selectedTicket.value = ticketId
        return
      }
    }

    // 自动选择第一个工单
    if (newList[0] && (newList[0].ticketID || newList[0].id)) {
      const firstTicket = newList[0]
      const ticketId = firstTicket.ticketID || firstTicket.id
      selectedTicket.value = ticketId
    }
  },
  { immediate: true },
)
```

### 3. 修改独立工单详情页面 `pages/feedback/ticket-detail/[id].vue`

**主要改进：**
- 添加了工单列表变化的监听器
- 确保当前工单被正确选中
- 优化了URL正确性检查

**关键代码：**
```vue
// 监听工单列表变化，确保当前工单被正确选中
watch(
  tickets,
  (newTickets) => {
    if (newTickets && newTickets.length > 0) {
      // 检查当前工单是否在列表中
      const foundTicket = newTickets.find(ticket =>
        (ticket.ticketID || ticket.id)?.toString() === ticketId,
      )
      
      if (foundTicket) {
        // 工单存在，确保URL正确
        const correctPath = `/feedback/ticket-detail/${foundTicket.ticketID || foundTicket.id}`
        if (window.location.pathname !== correctPath) {
          window.history.replaceState({}, '', correctPath)
          window.dispatchEvent(new CustomEvent('urlchange'))
        }
      }
    }
  },
  { immediate: true }
)
```

### 4. 修改主页面 `pages/feedback/index.vue`

**主要改进：**
- 优化了工单ID的获取逻辑
- 优先从路由参数中获取工单ID

**关键代码：**
```vue
// 从 URL 参数中获取工单 ID（如果有的话）
const selectedTicketId = computed(() => {
  // 优先从路由参数中获取工单ID（用于工单详情页面）
  if (route.params.id && typeof route.params.id === 'string') {
    return route.params.id
  }
  // 其次从查询参数中获取
  return route.query.ticketId as string || ''
})
```

## 测试验证

创建了测试页面 `pages/test/ticket-selection-test.vue` 来验证修改的正确性：

1. **直接访问测试**：访问 `http://localhost:3000/feedback/ticket-detail/1749714907000030` 应该自动选中对应工单
2. **空列表测试**：切换到没有工单的菜单时，工单详情应该为空
3. **菜单切换测试**：切换菜单时应该自动选中第一个工单（如果有的话）

## 预期效果

1. ✅ 通过链接直接访问工单详情页时，对应的工单会被自动选中
2. ✅ 切换到有工单的菜单时，会自动选中第一个工单
3. ✅ 切换到没有工单的菜单时，工单详情页显示空状态，URL重置为 `/feedback/`
4. ✅ 菜单切换时，URL会正确更新
5. ✅ 工单选择状态与URL保持同步

## 技术要点

- 使用 `watch` 监听器来响应数据变化
- 使用 `window.history.replaceState` 来更新URL而不触发页面刷新
- 使用自定义事件 `urlchange` 来通知其他组件URL变化
- 优先使用 `ticketID` 字段进行工单匹配，兼容 `id` 字段
- 确保在客户端环境下才执行DOM操作
