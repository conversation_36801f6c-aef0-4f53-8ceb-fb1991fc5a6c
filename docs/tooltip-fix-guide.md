# Tooltip 修复方案

## 问题描述

项目中的tooltip有较大概率无法显示，需要重新刷新页面才能正常工作。这个问题主要出现在以下情况：

1. 页面长时间使用后tooltip失效
2. 路由切换后tooltip不显示
3. 页面失去焦点后重新获得焦点时tooltip失效
4. 动态内容变化后tooltip失效

## 解决方案

### 1. 全局TooltipProvider配置

在 `app.vue` 中添加了全局的TooltipProvider，确保整个应用只有一个tooltip上下文：

```vue
<TooltipProvider 
  :delay-duration="300"
  :skip-delay-duration="100"
>
  <!-- 应用内容 -->
</TooltipProvider>
```

### 2. 增强的Tooltip组件

创建了 `EnhancedTooltip` 组件，提供以下功能：

- 自动修复tooltip状态
- 防抖机制避免频繁DOM操作
- 更好的z-index管理
- 自动清理孤立的tooltip元素

### 3. 全局Tooltip管理器

`useTooltipManager` composable 提供：

- `resetTooltips()` - 重置所有tooltip状态
- `fixTooltipState()` - 修复tooltip状态
- `checkTooltipHealth()` - 检查tooltip健康状态

### 4. 自动修复插件

`plugins/tooltip-manager.client.ts` 提供：

- 监听页面可见性变化
- 监听路由变化
- 监听DOM变化
- 定期健康检查
- 全局修复函数 `window.$fixTooltips()`

## 使用方法

### 基础使用

```vue
<template>
  <Tooltip>
    <TooltipTrigger as-child>
      <Button>悬停我</Button>
    </TooltipTrigger>
    <TooltipContent>
      提示内容
    </TooltipContent>
  </Tooltip>
</template>
```

### 增强版使用

```vue
<template>
  <EnhancedTooltip content="提示内容">
    <Button>悬停我</Button>
  </EnhancedTooltip>
</template>
```

### 手动修复

```javascript
// 使用composable
const { fixTooltipState } = useTooltipManager()
await fixTooltipState()

// 使用全局函数
window.$fixTooltips()
```

## 配置选项

### TooltipProvider 配置

- `delayDuration`: 显示延迟时间（默认300ms）
- `skipDelayDuration`: 跳过延迟时间（默认100ms）

### EnhancedTooltip 配置

- `content`: 提示内容
- `side`: 显示位置（top/right/bottom/left）
- `sideOffset`: 偏移距离
- `disabled`: 是否禁用
- `maxWidth`: 最大宽度
- `autoFix`: 是否启用自动修复

## 最佳实践

1. **使用单一TooltipProvider**: 在应用根部使用一个TooltipProvider
2. **优先使用EnhancedTooltip**: 对于复杂场景使用增强版组件
3. **定期健康检查**: 在关键操作后调用健康检查
4. **路由变化后修复**: 在路由变化后手动触发修复
5. **避免嵌套TooltipProvider**: 不要在组件内部再次包装TooltipProvider

## 测试页面

访问 `/test/tooltip-fix` 页面可以测试tooltip修复功能：

- 健康检查
- 重置功能测试
- 修复功能测试
- 压力测试

## 技术细节

### 问题根因

1. **多个TooltipProvider冲突**: 嵌套的TooltipProvider会导致状态混乱
2. **DOM状态残留**: 页面变化后tooltip DOM元素没有正确清理
3. **事件监听器失效**: 页面失去焦点后事件监听器可能失效
4. **z-index层级问题**: tooltip被其他元素遮挡

### 修复机制

1. **状态重置**: 强制清理所有tooltip状态和DOM元素
2. **事件重新绑定**: 重新初始化事件监听器
3. **防抖处理**: 避免频繁的修复操作
4. **自动监听**: 监听各种可能导致问题的事件

## 兼容性

- Vue 3.x
- Nuxt 3.x
- radix-vue 1.9+
- 现代浏览器（支持MutationObserver）

## 故障排除

如果tooltip仍然不工作：

1. 检查控制台是否有JavaScript错误
2. 确认只有一个TooltipProvider
3. 手动调用 `window.$fixTooltips()`
4. 检查z-index是否被其他元素遮挡
5. 确认tooltip内容不为空
