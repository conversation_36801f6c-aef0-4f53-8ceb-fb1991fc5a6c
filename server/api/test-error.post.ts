export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { errorCode, message } = body

  // 模拟不同的错误响应
  switch (errorCode) {
    case 2004:
      // Token无效
      setResponseStatus(event, 401)
      return {
        code: 2004,
        msg: 'Token无效',
        data: null,
        status: 'error'
      }
    
    case 2002:
      // 用户未登录
      setResponseStatus(event, 401)
      return {
        code: 2002,
        msg: '用户未登录',
        data: null,
        status: 'error'
      }
    
    case 2005:
      // 无权限编辑
      setResponseStatus(event, 403)
      return {
        code: 2005,
        msg: '无权限编辑',
        data: null,
        status: 'error'
      }
    
    case 1002:
      // 参数错误
      setResponseStatus(event, 400)
      return {
        code: 1002,
        msg: '查询失败，参数错误',
        data: null,
        status: 'error'
      }
    
    case -1:
      // 系统错误
      setResponseStatus(event, 500)
      return {
        code: -1,
        msg: '未知错误，请联系管理员',
        data: null,
        status: 'error'
      }
    
    case 1006:
      // DB连接失败
      setResponseStatus(event, 500)
      return {
        code: 1006,
        msg: '数据库连接失败',
        data: null,
        status: 'error'
      }
    
    case 20000:
      // 成功
      return {
        code: 20000,
        msg: '请求成功',
        data: {
          message: message || '操作成功',
          timestamp: new Date().toISOString()
        },
        status: 'ok'
      }
    
    default:
      // 默认错误
      setResponseStatus(event, 400)
      return {
        code: 1009,
        msg: '参数错误',
        data: null,
        status: 'error'
      }
  }
})
