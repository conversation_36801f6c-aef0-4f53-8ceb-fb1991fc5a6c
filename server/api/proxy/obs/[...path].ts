export default defineEventHandler(async (event) => {
  console.log('OBS代理路由被调用:', getRequestURL(event).href)

  // 设置CORS头部
  setHeader(event, 'Access-Control-Allow-Origin', '*')
  setHeader(event, 'Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS')
  setHeader(event, 'Access-Control-Allow-Headers', 'Content-Type, Authorization, Range')

  // 处理OPTIONS预检请求
  if (getMethod(event) === 'OPTIONS') {
    setResponseStatus(event, 200)
    return ''
  }

  const path = getRouterParam(event, 'path')
  console.log('提取的路径参数:', path)
  console.log('路径类型:', typeof path, '是否为数组:', Array.isArray(path))

  if (!path) {
    return {
      error: 'Missing file path',
      url: getRequestURL(event).href,
    }
  }

  // 构建目标URL
  const obsBaseUrl = 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com'
  // 确保路径正确拼接，避免双斜杠
  const cleanPath = Array.isArray(path) ? path.join('/') : path
  const targetUrl = `${obsBaseUrl}/${cleanPath}`

  console.log('代理到:', targetUrl)

  try {
    // 构建请求头，支持Range请求（视频分片加载）
    const requestHeaders: Record<string, string> = {
      'User-Agent': 'Nuxt-Proxy/1.0',
      'Accept': '*/*',
    }

    // 传递Range头部以支持视频分片加载
    const rangeHeader = getHeader(event, 'range')
    if (rangeHeader) {
      requestHeaders.Range = rangeHeader
    }

    const response = await fetch(targetUrl, {
      method: getMethod(event),
      headers: requestHeaders,
    })

    console.log('OBS响应状态:', response.status, response.statusText)
    console.log('响应头:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: response.statusText,
      })
    }

    // 复制重要的响应头
    const contentType = response.headers.get('content-type')
    const contentLength = response.headers.get('content-length')
    const acceptRanges = response.headers.get('accept-ranges')
    const contentRange = response.headers.get('content-range')

    if (contentType)
      setHeader(event, 'Content-Type', contentType)
    if (contentLength)
      setHeader(event, 'Content-Length', contentLength)
    if (acceptRanges)
      setHeader(event, 'Accept-Ranges', acceptRanges)
    if (contentRange)
      setHeader(event, 'Content-Range', contentRange)

    // 设置缓存和CORS头部
    setHeader(event, 'Cache-Control', 'public, max-age=3600')

    // 设置正确的状态码（特别是206 Partial Content）
    if (response.status === 206) {
      setResponseStatus(event, 206)
    }

    // 处理HEAD请求，不返回响应体
    if (getMethod(event) === 'HEAD') {
      return null
    }

    // 返回响应流
    try {
      // 直接使用 sendStream，让 h3 处理流
      return sendStream(event, response.body!)
    }
    catch (streamError) {
      console.warn('流处理失败，尝试使用 arrayBuffer:', streamError)

      // 如果流处理失败，尝试使用 arrayBuffer
      try {
        const arrayBuffer = await response.arrayBuffer()
        return new Uint8Array(arrayBuffer)
      }
      catch (bufferError) {
        console.error('arrayBuffer 处理也失败:', bufferError)
        throw createError({
          statusCode: 500,
          statusMessage: `Response processing failed: ${bufferError.message}`,
        })
      }
    }
  }
  catch (error: any) {
    console.error('代理错误:', error)
    throw createError({
      statusCode: 500,
      statusMessage: `Proxy error: ${error.message}`,
    })
  }
})
