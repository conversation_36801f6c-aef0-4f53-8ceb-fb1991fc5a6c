{"cSpell.words": ["changya", "Customizer", "deptname", "feishu", "itmp", "kapi", "lisi", "lucide", "nuxt", "nuxtjs", "OPPO", "overlayscrollbars", "persistedstate", "shadcn", "<PERSON><PERSON>", "Ttid", "unocss", "vueuse", "wangwu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.format.enable": true, "prettier.enable": false, "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[json]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}}