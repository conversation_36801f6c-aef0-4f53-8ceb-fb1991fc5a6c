<script setup lang="ts">
interface ErrorProps {
  statusCode: number
  statusMessage?: string
  stack?: string
}

// 接收错误信息
const props = defineProps<{
  error: ErrorProps
}>()

const { theme, radius } = useCustomize()
const router = useRouter()

// 计算属性
const errorIcon = computed(() => {
  switch (props.error?.statusCode) {
    case 403:
      return 'lucide:shield-x'
    case 404:
      return 'lucide:file-x'
    case 500:
      return 'lucide:server-crash'
    default:
      return 'lucide:alert-triangle'
  }
})

const errorTitle = computed(() => {
  switch (props.error?.statusCode) {
    case 403:
      return 'Access Denied'
    case 404:
      return 'Page Not Found'
    case 500:
      return 'Server Error'
    default:
      return 'Error Occurred'
  }
})

const errorMessage = computed(() => {
  switch (props.error?.statusCode) {
    case 403:
      return 'You don\'t have permission to access this page. Please contact administrator or login with proper credentials.'
    case 404:
      return 'It seems like the page you\'re looking for does not exist or might have been removed.'
    case 500:
      return 'Internal server error occurred. Please try again later or contact technical support.'
    default:
      return props.error?.statusMessage || 'An unknown error occurred. Please try again later.'
  }
})

const isDev = ref(false)

// 在客户端设置开发环境标志
onMounted(() => {
  isDev.value = import.meta.dev
})

// 设置页面头部
useHead({
  bodyAttrs: {
    class: computed(() => `theme-${theme.value}`),
    style: computed(() => `--radius: ${radius.value}rem;`),
  },
  title: computed(() => `${props.error?.statusCode || 404} - ${errorTitle.value}`),
})

// 方法
function handleRetry() {
  window.location.reload()
}

function goHome() {
  const authStore = useAuthStore()
  const homePath = authStore.isAuthenticated ? authStore.homePath : '/'
  router.push(homePath)
}

function goLogin() {
  const route = useRoute()
  router.push({
    path: '/login',
    query: { redirect: route.fullPath },
  })
}
</script>

<template>
  <div class="h-svh">
    <div class="m-auto h-full max-w-md w-full flex flex-col items-center justify-center gap-4 px-4">
      <div class="text-center">
        <Icon
          :name="errorIcon"
          class="mx-auto mb-4 h-16 w-16 text-muted-foreground"
        />
        <h1 class="text-[4rem] font-bold leading-tight">
          {{ error?.statusCode || 404 }}
        </h1>
        <h2 class="mb-2 text-xl font-semibold">
          {{ errorTitle }}
        </h2>
        <p class="mb-6 text-center text-muted-foreground">
          {{ errorMessage }}
        </p>
      </div>

      <div class="w-full flex flex-col gap-3">
        <Button class="w-full" @click="handleRetry">
          <Icon name="lucide:refresh-cw" class="mr-2 h-4 w-4" />
          Retry
        </Button>

        <Button variant="outline" class="w-full" @click="goHome">
          <Icon name="lucide:home" class="mr-2 h-4 w-4" />
          Back to Home
        </Button>

        <Button
          v-if="error?.statusCode === 403"
          variant="outline"
          class="w-full"
          @click="goLogin"
        >
          <Icon name="lucide:log-in" class="mr-2 h-4 w-4" />
          Login Again
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
