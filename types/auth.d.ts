export interface LoginParams {
  username: string
  password: string
  captcha?: boolean
}

export interface LoginResult {
  user_name: string
  email: string
  user_id: string
  TT_id: string
  deptname: string | null
  token: string
  role: string
}

export interface UserInfo {
  id: string
  username: string
  email: string
  avatar?: string
  roles: string[]
  permissions?: string[]
  homePath?: string
  realName?: string
  // 新增字段以兼容API响应
  user_id?: string
  TT_id?: string
  deptname?: string | null
  role?: string
}

/**
 * API返回的用户信息格式
 */
export interface ApiUserInfo {
  user_name: string
  email: string
  user_id: string
  TT_id: string
  deptname?: string | null
  roles?: Array<{
    roleName: string
    value: string
  }>
}

export interface RefreshTokenResult {
  accessToken?: string
  refreshToken?: string
  token?: string
}

export interface ApiResponse<T = any> {
  code: number
  data: T
  msg: string
  status: string
}

export interface RequestConfig {
  withCredentials?: boolean
  skipAuth?: boolean
  skipErrorHandler?: boolean
}

export interface ApiError {
  code: number
  msg: string
  message?: string
  status?: string
}

export interface ErrorResponse {
  response?: {
    status: number
    data: ApiError
  }
  code?: number
  message?: string
}
