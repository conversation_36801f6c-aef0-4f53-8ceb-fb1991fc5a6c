<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import TicketDetail from '~/components/feedback/TicketDetail.vue'

// 测试工单数据
const testTickets: Ticket[] = [
  {
    id: 'T001',
    ticketID: 'T001',
    problemDescription: '登录页面无法正常显示',
    stage: '待处理',
    status: '待处理',
    feedbackPerson: 'user1',
    handler: '',
    enterTime: '2024-01-15 09:00:00',
    createdAt: '2024-01-15 09:00:00',
    severityLevel: '高',
    apps: ['Web应用'],
    appVersion: ['v1.2.0'],
    osType: ['Windows'],
    mobileType: ['PC'],
    firstLevelCategory: '功能问题',
    secondLevelCategory: '登录问题',
    attachments: [],
    changeLogs: [],
  },
  {
    id: 'T002',
    ticketID: 'T002',
    problemDescription: '数据导出功能异常',
    stage: '处理中',
    status: '处理中',
    feedbackPerson: 'user2',
    handler: 'dev1',
    enterTime: '2024-01-14 14:30:00',
    createdAt: '2024-01-14 14:30:00',
    severityLevel: '中',
    apps: ['管理系统'],
    appVersion: ['v2.1.0'],
    osType: ['macOS'],
    mobileType: ['PC'],
    firstLevelCategory: '功能问题',
    secondLevelCategory: '导出问题',
    attachments: [],
    changeLogs: [],
  },
  {
    id: 'T003',
    ticketID: 'T003',
    problemDescription: '移动端界面显示错乱',
    stage: '已处理',
    status: '已处理',
    feedbackPerson: 'user3',
    handler: 'dev2',
    enterTime: '2024-01-13 16:45:00',
    createdAt: '2024-01-13 16:45:00',
    endTime: '2024-01-14 10:20:00',
    updatedAt: '2024-01-14 10:20:00',
    severityLevel: '低',
    cause: 'CSS样式冲突导致',
    result: '已修复样式问题，重新部署',
    apps: ['移动应用'],
    appVersion: ['v1.5.2'],
    osType: ['iOS', 'Android'],
    mobileType: ['手机'],
    firstLevelCategory: '界面问题',
    secondLevelCategory: '显示问题',
    attachments: [],
    changeLogs: [],
  },
]

const selectedTicket = ref<Ticket | undefined>()

function handleRefresh() {
  console.log('刷新工单列表')
}

function handleRefreshTicket() {
  console.log('刷新当前工单详情')
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <h1 class="mb-6 text-2xl font-bold">
      工单详情页工具栏测试
    </h1>

    <div class="space-y-6">
      <!-- 测试说明 -->
      <div class="border border-blue-200 rounded-lg bg-blue-50 p-4">
        <h2 class="mb-2 text-lg font-semibold">
          测试说明
        </h2>
        <p class="text-sm text-gray-600">
          此页面用于测试工单详情页顶部工具栏的修改效果：
        </p>
        <ul class="mt-2 list-disc list-inside text-sm text-gray-600 space-y-1">
          <li>所有操作按钮都会显示，不再根据权限隐藏</li>
          <li>无权限的按钮会显示为禁用状态</li>
          <li><strong>重要改进：</strong>即使在disabled状态下，鼠标悬停也会显示详细的权限提示信息</li>
          <li><strong>统一弹窗：</strong>所有操作（包括认领）都使用统一的modal组件进行确认</li>
          <li><strong>一致样式：</strong>所有按钮使用相同的样式（variant="ghost" size="icon"）</li>
          <li>提示信息会明确告知用户为什么按钮被禁用（无权限/状态不允许/无工单数据）</li>
          <li>已移除：返回、回复、转发、添加标签、静音工单按钮</li>
        </ul>
      </div>

      <!-- 工单选择器 -->
      <div class="border rounded-lg bg-white p-4">
        <h3 class="mb-4 text-lg font-semibold">
          选择测试工单
        </h3>
        <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
          <button
            v-for="ticket in testTickets"
            :key="ticket.id"
            class="border rounded-lg p-3 text-left transition-colors hover:bg-gray-50"
            :class="selectedTicket?.id === ticket.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
            @click="selectedTicket = ticket"
          >
            <div class="font-medium">
              {{ ticket.problemDescription }}
            </div>
            <div class="text-sm text-gray-500">
              状态: {{ ticket.stage }}
            </div>
            <div class="text-sm text-gray-500">
              ID: {{ ticket.id }}
            </div>
          </button>
        </div>
      </div>

      <!-- 工单详情展示 -->
      <div v-if="selectedTicket" class="overflow-hidden border rounded-lg bg-white">
        <div class="border-b bg-gray-50 p-4">
          <h3 class="text-lg font-semibold">
            工单详情工具栏预览
          </h3>
          <p class="text-sm text-gray-600">
            当前选中工单: {{ selectedTicket.problemDescription }}
          </p>
        </div>

        <!-- 这里嵌入工单详情组件 -->
        <div class="h-96">
          <TicketDetail
            :ticket="selectedTicket"
            @close="selectedTicket = undefined"
            @refresh="handleRefresh"
            @refresh-ticket="handleRefreshTicket"
          />
        </div>
      </div>

      <!-- 权限说明 -->
      <div class="border border-yellow-200 rounded-lg bg-yellow-50 p-4">
        <h3 class="mb-2 text-lg font-semibold">
          权限说明
        </h3>
        <div class="text-sm text-gray-600 space-y-2">
          <div><strong>认领工单:</strong> 所有用户，仅限"待处理"状态</div>
          <div><strong>指派工单:</strong> 管理员和经理，"待处理"和"处理中"状态</div>
          <div><strong>完成工单:</strong> 所有用户（需要是处理人），仅限"处理中"状态</div>
          <div><strong>驳回工单:</strong> 管理员和经理，"待处理"和"处理中"状态</div>
          <div><strong>归档工单:</strong> 管理员，仅限"已处理"状态</div>
          <div><strong>加急工单:</strong> 所有用户，"待处理"和"处理中"状态</div>
          <div><strong>删除工单:</strong> 超级管理员和管理员，仅限"待处理"状态</div>
        </div>
      </div>
    </div>
  </div>
</template>
