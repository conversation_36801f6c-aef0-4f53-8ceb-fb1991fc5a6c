<template>
  <div class="container mx-auto p-6">
    <Card>
      <CardHeader>
        <CardTitle>视频播放测试</CardTitle>
        <CardDescription>测试不同来源的视频播放功能</CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- 测试视频列表 -->
        <div class="grid gap-4">
          <div v-for="(video, index) in testVideos" :key="index" class="border rounded-lg p-4">
            <h3 class="font-medium mb-2">{{ video.name }}</h3>
            <p class="text-sm text-muted-foreground mb-3">{{ video.description }}</p>
            
            <!-- 视频信息 -->
            <div class="text-xs text-muted-foreground mb-3 space-y-1">
              <div><strong>URL:</strong> {{ video.url }}</div>
              <div><strong>类型:</strong> {{ video.type }}</div>
            </div>
            
            <!-- 视频播放器 -->
            <div class="relative">
              <video
                :id="`video-${index}`"
                :src="video.url"
                controls
                preload="metadata"
                class="w-full max-w-md rounded border"
                @loadstart="onLoadStart(index)"
                @canplay="onCanPlay(index)"
                @error="onError(index, $event)"
                @loadedmetadata="onLoadedMetadata(index)"
              >
                您的浏览器不支持视频播放。
              </video>
              
              <!-- 状态显示 -->
              <div class="mt-2 text-sm">
                <div class="flex items-center gap-2">
                  <span class="font-medium">状态:</span>
                  <Badge :variant="getStatusVariant(video.status)">
                    {{ video.status }}
                  </Badge>
                </div>
                <div v-if="video.error" class="text-red-500 text-xs mt-1">
                  错误: {{ video.error }}
                </div>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex gap-2 mt-3">
              <Button size="sm" variant="outline" @click="retryVideo(index)">
                重新加载
              </Button>
              <Button size="sm" variant="outline" @click="testDirectAccess(video.url)">
                直接访问
              </Button>
            </div>
          </div>
        </div>
        
        <!-- 网络请求日志 -->
        <div class="border rounded-lg p-4">
          <h3 class="font-medium mb-2">网络请求日志</h3>
          <div class="text-xs font-mono bg-muted p-2 rounded max-h-40 overflow-y-auto">
            <div v-for="(log, index) in networkLogs" :key="index" class="mb-1">
              <span class="text-muted-foreground">{{ log.timestamp }}</span>
              <span :class="log.type === 'error' ? 'text-red-500' : 'text-green-600'">
                {{ log.message }}
              </span>
            </div>
          </div>
          <Button size="sm" variant="outline" class="mt-2" @click="clearLogs">
            清空日志
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

definePageMeta({
  title: '视频播放测试',
  layout: 'default',
})

interface TestVideo {
  name: string
  description: string
  url: string
  type: string
  status: 'loading' | 'ready' | 'error' | 'idle'
  error?: string
}

interface NetworkLog {
  timestamp: string
  type: 'info' | 'error'
  message: string
}

const testVideos = ref<TestVideo[]>([
  {
    name: '华为云OBS视频',
    description: '通过代理访问的华为云OBS视频文件',
    url: '/api/proxy/obs/22870a5133574988be17e16071bdecf5.mp4',
    type: 'mp4',
    status: 'idle'
  },
  {
    name: '华为云OBS直接访问',
    description: '直接访问华为云OBS视频文件（可能有CORS问题）',
    url: 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4',
    type: 'mp4',
    status: 'idle'
  },
  {
    name: '在线测试视频',
    description: '公开的在线测试视频',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    type: 'mp4',
    status: 'idle'
  },
  {
    name: '本地测试视频',
    description: '简单的MP4测试视频',
    url: 'https://www.w3schools.com/html/mov_bbb.mp4',
    type: 'mp4',
    status: 'idle'
  }
])

const networkLogs = ref<NetworkLog[]>([])

function addLog(type: 'info' | 'error', message: string) {
  const timestamp = new Date().toLocaleTimeString()
  networkLogs.value.unshift({ timestamp, type, message })
  if (networkLogs.value.length > 50) {
    networkLogs.value = networkLogs.value.slice(0, 50)
  }
}

function onLoadStart(index: number) {
  testVideos.value[index].status = 'loading'
  testVideos.value[index].error = undefined
  addLog('info', `视频 ${index + 1} 开始加载: ${testVideos.value[index].name}`)
}

function onCanPlay(index: number) {
  testVideos.value[index].status = 'ready'
  addLog('info', `视频 ${index + 1} 可以播放: ${testVideos.value[index].name}`)
}

function onError(index: number, event: Event) {
  const video = event.target as HTMLVideoElement
  testVideos.value[index].status = 'error'
  
  let errorMessage = '未知错误'
  if (video.error) {
    switch (video.error.code) {
      case 1:
        errorMessage = 'MEDIA_ERR_ABORTED - 播放被中止'
        break
      case 2:
        errorMessage = 'MEDIA_ERR_NETWORK - 网络错误'
        break
      case 3:
        errorMessage = 'MEDIA_ERR_DECODE - 解码错误'
        break
      case 4:
        errorMessage = 'MEDIA_ERR_SRC_NOT_SUPPORTED - 不支持的媒体格式'
        break
    }
  }
  
  testVideos.value[index].error = errorMessage
  addLog('error', `视频 ${index + 1} 加载失败: ${testVideos.value[index].name} - ${errorMessage}`)
}

function onLoadedMetadata(index: number) {
  addLog('info', `视频 ${index + 1} 元数据加载完成: ${testVideos.value[index].name}`)
}

function retryVideo(index: number) {
  const video = document.getElementById(`video-${index}`) as HTMLVideoElement
  if (video) {
    testVideos.value[index].status = 'idle'
    testVideos.value[index].error = undefined
    video.load()
    addLog('info', `重新加载视频 ${index + 1}: ${testVideos.value[index].name}`)
  }
}

function testDirectAccess(url: string) {
  addLog('info', `测试直接访问: ${url}`)
  window.open(url, '_blank')
}

function getStatusVariant(status: string) {
  switch (status) {
    case 'loading':
      return 'secondary'
    case 'ready':
      return 'default'
    case 'error':
      return 'destructive'
    default:
      return 'outline'
  }
}

function clearLogs() {
  networkLogs.value = []
}
</script>
