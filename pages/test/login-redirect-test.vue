<script setup lang="ts">
const authStore = useAuthStore()
const accessStore = useAccessStore()
const route = useRoute()

const testResults = ref<string[]>([])

function addResult(message: string) {
  testResults.value.unshift(`[${new Date().toLocaleTimeString()}] ${message}`)
}

// 检查当前状态
function checkCurrentStatus() {
  addResult('=== 当前状态检查 ===')
  addResult(`当前路径: ${route.fullPath}`)
  addResult(`重定向参数: ${route.query.redirect || '无'}`)
  addResult(`认证状态: ${authStore.isAuthenticated}`)
  addResult(`用户信息: ${authStore.user?.username || '无'}`)
  addResult(`Access Token: ${accessStore.accessToken ? '有' : '无'}`)
  addResult(`Login Expired: ${accessStore.loginExpired}`)
}

// 模拟认证过期场景
async function simulateAuthExpired() {
  addResult('=== 模拟认证过期场景 ===')
  
  // 设置登录过期状态
  authStore.setLoginExpired(true)
  addResult('已设置 loginExpired = true')
  
  // 跳转到登录页面，带重定向参数
  await navigateTo({
    path: '/login',
    query: {
      redirect: '/feedback',
      expired: '1'
    }
  })
}

// 模拟登录成功
async function simulateLoginSuccess() {
  addResult('=== 模拟登录成功 ===')
  
  try {
    // 模拟登录参数
    const loginParams = {
      username: 'admin',
      password: 'admin'
    }
    
    addResult('开始登录...')
    await authStore.login(loginParams)
    addResult('登录成功！')
    
    // 检查是否跳转
    setTimeout(() => {
      addResult(`登录后当前路径: ${route.fullPath}`)
    }, 1000)
    
  } catch (error: any) {
    addResult(`登录失败: ${error.message}`)
  }
}

// 清除状态
function clearAuthState() {
  authStore.clearAuthData()
  addResult('已清除认证状态')
}

// 清除测试结果
function clearResults() {
  testResults.value = []
}

// 页面加载时检查状态
onMounted(() => {
  checkCurrentStatus()
})
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">登录跳转测试</h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 测试按钮区域 -->
        <div class="space-y-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h2 class="text-lg font-semibold text-blue-800 mb-3">测试操作</h2>
            
            <div class="space-y-3">
              <Button 
                @click="checkCurrentStatus"
                variant="outline"
                class="w-full"
              >
                检查当前状态
              </Button>
              
              <Button 
                @click="clearAuthState"
                variant="outline"
                class="w-full"
              >
                清除认证状态
              </Button>
              
              <Button 
                @click="simulateAuthExpired"
                variant="destructive"
                class="w-full"
              >
                模拟认证过期 → 跳转登录页
              </Button>
              
              <Button 
                @click="simulateLoginSuccess"
                class="w-full"
              >
                模拟登录成功
              </Button>
              
              <Button 
                @click="clearResults"
                variant="outline"
                class="w-full"
              >
                清除测试结果
              </Button>
            </div>
          </div>
          
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 class="font-medium mb-2 text-yellow-800">测试场景</h3>
            <ul class="text-sm text-yellow-700 space-y-1">
              <li>1. 清除认证状态</li>
              <li>2. 模拟认证过期，跳转到登录页</li>
              <li>3. 在登录页面登录成功</li>
              <li>4. 检查是否自动跳转到重定向页面</li>
            </ul>
          </div>
          
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 class="font-medium mb-2 text-green-800">预期结果</h3>
            <ul class="text-sm text-green-700 space-y-1">
              <li>• 登录成功后应该自动跳转到 /feedback</li>
              <li>• 不应该停留在登录页面</li>
              <li>• loginExpired 状态应该被清除</li>
            </ul>
          </div>
        </div>
        
        <!-- 测试结果区域 -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h2 class="text-lg font-semibold mb-3">测试结果</h2>
          
          <div class="bg-black text-green-400 p-3 rounded font-mono text-sm h-96 overflow-y-auto">
            <div v-if="testResults.length === 0" class="text-gray-500">
              暂无测试结果...
            </div>
            <div 
              v-for="(result, index) in testResults" 
              :key="index"
              class="mb-1"
            >
              {{ result }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
