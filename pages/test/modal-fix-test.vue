<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, HelpCircle, Info, XCircle } from 'lucide-vue-next'
import { useModal } from '~/composables/useModal'

const modal = useModal()
const lastResult = ref<string>('')

async function testInfo() {
  console.log('测试信息弹窗')
  await modal.info('这是一个信息弹窗测试，应该只显示一个弹窗。', '信息测试')
  lastResult.value = '信息弹窗测试完成'
}

async function testConfirm() {
  console.log('测试确认弹窗')
  const result = await modal.confirm('这是一个确认弹窗测试，应该只显示一个弹窗。', '确认测试')
  lastResult.value = result ? '用户点击了确定' : '用户点击了取消'
}

async function testSuccess() {
  console.log('测试成功弹窗')
  await modal.success('这是一个成功弹窗测试，应该只显示一个弹窗。', '成功测试')
  lastResult.value = '成功弹窗测试完成'
}

async function testError() {
  console.log('测试错误弹窗')
  await modal.error('这是一个错误弹窗测试，应该只显示一个弹窗。', '错误测试')
  lastResult.value = '错误弹窗测试完成'
}

definePageMeta({
  title: '弹窗修复测试',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">
          🔧 弹窗修复测试
        </h1>
        <p class="mt-2 text-muted-foreground">
          测试修复后的弹窗系统，确保只显示一个弹窗
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>🎯 双重弹窗修复测试</CardTitle>
          <CardDescription>
            点击按钮测试是否还会出现双重弹窗问题
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <Button variant="outline" @click="testInfo">
              <Info class="mr-2 h-4 w-4 text-blue-500" />
              信息弹窗
            </Button>
            <Button variant="outline" @click="testConfirm">
              <HelpCircle class="mr-2 h-4 w-4 text-blue-500" />
              确认弹窗
            </Button>
            <Button variant="outline" @click="testSuccess">
              <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
              成功弹窗
            </Button>
            <Button variant="outline" @click="testError">
              <XCircle class="mr-2 h-4 w-4 text-red-500" />
              错误弹窗
            </Button>
          </div>

          <div v-if="lastResult" class="rounded-lg bg-muted p-3">
            <p class="text-sm">
              <strong>测试结果:</strong> {{ lastResult }}
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>📋 测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-sm space-y-2">
            <p><strong>修复前的问题:</strong> 每次点击按钮会同时显示两个弹窗</p>
            <p><strong>修复后的效果:</strong> 每次点击按钮只显示一个弹窗</p>
            <p><strong>测试方法:</strong> 点击上面的按钮，观察是否只显示一个弹窗</p>
            <p><strong>预期结果:</strong> 只显示全局的 Modal 组件，不再显示原生DOM弹窗</p>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
