<script setup lang="ts">
import { getCategoryList } from '~/api/feedback/ticket'
import { CascadeDropdown } from '~/components/ui/cascade-dropdown'

// 模拟数据
const mockData = [
  {
    id: 1,
    name: '首页-娱乐',
    children: [
      {
        id: 11,
        name: '爱意房/魅力房',
        children: [
          { id: 111, name: '直播时长、直播送礼记录、直播数据' },
          { id: 112, name: '房间小时榜' },
          { id: 113, name: '娱乐房推荐列表' },
        ],
      },
      {
        id: 12,
        name: '语音直播',
        children: [
          { id: 121, name: 'CP战' },
          { id: 122, name: '投投票' },
        ],
      },
    ],
  },
  {
    id: 2,
    name: '首页',
    children: [
      {
        id: 21,
        name: '聊天页面',
        children: [
          { id: 211, name: '个人页面' },
        ],
      },
    ],
  },
  {
    id: 3,
    name: '房间玩法',
    children: [
      {
        id: 31,
        name: '动态广场',
        children: [
          { id: 311, name: '投投票' },
        ],
      },
    ],
  },
]

// 状态管理
const selectedValue = ref<string[]>([])
const apiData = ref<any[]>([])
const loading = ref(false)

// 获取真实API数据
async function fetchApiData() {
  try {
    loading.value = true
    const response = await getCategoryList()
    console.log('API响应:', response)

    if (response && Array.isArray(response)) {
      apiData.value = response.map((item: any) => ({
        id: item.id || item.value,
        name: item.name || item.label,
        children: item.children?.map((child: any) => ({
          id: child.id || child.value,
          name: child.name || child.label,
          children: child.children?.map((grandChild: any) => ({
            id: grandChild.id || grandChild.value,
            name: grandChild.name || grandChild.label,
          })) || [],
        })) || [],
      }))
    }
  }
  catch (error) {
    console.error('获取API数据失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 处理选择变化
function handleChange(value: string[], selectedItems: any[]) {
  console.log('选择的值:', value)
  console.log('选择的项目:', selectedItems)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchApiData()
})
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-4">
      <h1 class="text-2xl font-bold">
        级联选择器测试
      </h1>
      <p class="text-muted-foreground">
        测试功能类型的级联选择功能
      </p>
    </div>

    <!-- 模拟数据测试 -->
    <Card>
      <CardHeader>
        <CardTitle>模拟数据测试</CardTitle>
        <CardDescription>使用模拟的三级分类数据测试级联选择器</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>功能类型选择</Label>
          <CascadeDropdown
            v-model="selectedValue"
            :data="mockData"
            placeholder="请选择功能类型"
            search-placeholder="搜索功能"
            @change="handleChange"
          />
        </div>

        <div class="space-y-2">
          <Label>选择结果</Label>
          <div class="rounded-md bg-muted p-3">
            <pre class="text-sm">{{ JSON.stringify(selectedValue, null, 2) }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- API数据测试 -->
    <Card>
      <CardHeader>
        <CardTitle>API数据测试</CardTitle>
        <CardDescription>使用真实API数据测试级联选择器</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>功能类型选择（API数据）</Label>
          <CascadeDropdown
            :data="apiData"
            :loading="loading"
            placeholder="请选择功能类型"
            search-placeholder="搜索功能"
          />
        </div>

        <div class="space-y-2">
          <Label>API响应数据</Label>
          <div class="max-h-60 overflow-y-auto rounded-md bg-muted p-3">
            <pre class="text-sm">{{ JSON.stringify(apiData, null, 2) }}</pre>
          </div>
        </div>

        <Button :disabled="loading" @click="fetchApiData">
          <Icon v-if="loading" name="i-lucide-loader-2" class="mr-2 size-4 animate-spin" />
          重新获取API数据
        </Button>
      </CardContent>
    </Card>

    <!-- 创建工单表单测试 -->
    <Card>
      <CardHeader>
        <CardTitle>创建工单表单测试</CardTitle>
        <CardDescription>测试在实际表单中的使用效果</CardDescription>
      </CardHeader>
      <CardContent>
        <CreateTicketForm :open="false" />
        <p class="mt-4 text-sm text-muted-foreground">
          注意：这里只是展示组件，不会实际打开弹窗。请在实际页面中测试创建工单功能。
        </p>
      </CardContent>
    </Card>
  </div>
</template>
