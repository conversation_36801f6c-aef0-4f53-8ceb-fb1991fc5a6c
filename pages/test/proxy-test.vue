<script setup lang="ts">
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ref } from 'vue'

definePageMeta({
  title: '代理测试',
  layout: 'default',
})

const testResults = ref<any[]>([])

// 测试代理URL
async function testProxy() {
  const testUrl = '/api/proxy/obs/22870a5133574988be17e16071bdecf5.mp4'
  
  try {
    const response = await fetch(testUrl, {
      method: 'HEAD' // 只获取头部信息
    })
    
    testResults.value.push({
      url: testUrl,
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      success: response.ok,
      timestamp: new Date().toLocaleTimeString()
    })
  } catch (error) {
    testResults.value.push({
      url: testUrl,
      error: error instanceof Error ? error.message : '未知错误',
      success: false,
      timestamp: new Date().toLocaleTimeString()
    })
  }
}

// 测试原始URL（会失败）
async function testOriginalUrl() {
  const originalUrl = 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4'
  
  try {
    const response = await fetch(originalUrl, {
      method: 'HEAD',
      mode: 'cors'
    })
    
    testResults.value.push({
      url: originalUrl,
      status: response.status,
      statusText: response.statusText,
      success: response.ok,
      timestamp: new Date().toLocaleTimeString()
    })
  } catch (error) {
    testResults.value.push({
      url: originalUrl,
      error: error instanceof Error ? error.message : '未知错误',
      success: false,
      timestamp: new Date().toLocaleTimeString()
    })
  }
}

function clearResults() {
  testResults.value = []
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold">代理测试页面</h1>
      <p class="text-muted-foreground mt-2">
        测试华为云OBS代理是否正常工作
      </p>
    </div>

    <!-- 测试控制 -->
    <Card>
      <CardHeader>
        <CardTitle>测试控制</CardTitle>
        <CardDescription>
          点击按钮测试不同的URL访问方式
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex gap-2 flex-wrap">
          <Button @click="testProxy">
            测试代理URL
          </Button>
          <Button variant="outline" @click="testOriginalUrl">
            测试原始URL (会失败)
          </Button>
          <Button variant="secondary" @click="clearResults">
            清空结果
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 测试结果 -->
    <Card v-if="testResults.length > 0">
      <CardHeader>
        <CardTitle>测试结果</CardTitle>
        <CardDescription>
          显示各种URL的访问测试结果
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="(result, index) in testResults"
            :key="index"
            class="p-4 border rounded-lg"
            :class="result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium" :class="result.success ? 'text-green-800' : 'text-red-800'">
                {{ result.success ? '✅ 成功' : '❌ 失败' }}
              </span>
              <span class="text-sm text-muted-foreground">{{ result.timestamp }}</span>
            </div>
            
            <div class="text-sm space-y-1">
              <div><strong>URL:</strong> {{ result.url }}</div>
              
              <div v-if="result.status">
                <strong>状态:</strong> {{ result.status }} {{ result.statusText }}
              </div>
              
              <div v-if="result.error" class="text-red-600">
                <strong>错误:</strong> {{ result.error }}
              </div>
              
              <div v-if="result.headers" class="mt-2">
                <strong>响应头:</strong>
                <pre class="mt-1 p-2 bg-muted rounded text-xs overflow-auto">{{ JSON.stringify(result.headers, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 说明信息 -->
    <Card>
      <CardHeader>
        <CardTitle>说明</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="text-sm space-y-2">
          <p><strong>代理URL:</strong> 通过本地服务器代理访问华为云OBS，应该成功</p>
          <p><strong>原始URL:</strong> 直接访问华为云OBS，会因为CORS策略失败</p>
          <p><strong>预期结果:</strong> 代理URL成功，原始URL失败</p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
