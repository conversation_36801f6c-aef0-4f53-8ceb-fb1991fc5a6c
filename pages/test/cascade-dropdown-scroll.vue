<script setup lang="ts">
import { CascadeDropdown } from '~/components/ui/cascade-dropdown'
import { getCategoryList } from '~/api/feedback/ticket'

// 模拟数据 - 增加更多数据来测试滚动
const mockData = [
  {
    id: 1,
    name: '首页-娱乐',
    children: [
      {
        id: 11,
        name: '爱意房/魅力房',
        children: [
          { id: 111, name: '直播时长、直播送礼记录、直播数据' },
          { id: 112, name: '房间小时榜' },
          { id: 113, name: '娱乐房推荐列表' },
          { id: 114, name: '房间热度排行' },
          { id: 115, name: '主播等级系统' },
          { id: 116, name: '礼物特效展示' },
          { id: 117, name: '粉丝团功能' },
          { id: 118, name: '直播间互动游戏' },
          { id: 119, name: '直播回放功能' },
          { id: 120, name: '直播间装扮' },
          { id: 121, name: '弹幕管理' },
          { id: 122, name: '房管功能' },
          { id: 123, name: '禁言系统' },
          { id: 124, name: '举报功能' },
          { id: 125, name: '黑名单管理' },
        ]
      },
      {
        id: 12,
        name: '语音直播',
        children: [
          { id: 131, name: 'CP战' },
          { id: 132, name: '投投票' },
          { id: 133, name: '语音连麦' },
          { id: 134, name: '背景音乐' },
          { id: 135, name: '声音特效' },
          { id: 136, name: '语音房间管理' },
          { id: 137, name: '麦序管理' },
          { id: 138, name: '房间公告' },
          { id: 139, name: '语音质量设置' },
          { id: 140, name: '降噪功能' },
          { id: 141, name: '回音消除' },
          { id: 142, name: '音量调节' },
        ]
      },
      {
        id: 13,
        name: '娱乐互动',
        children: [
          { id: 151, name: '弹幕互动' },
          { id: 152, name: '表情包' },
          { id: 153, name: '点赞功能' },
          { id: 154, name: '分享功能' },
          { id: 155, name: '关注提醒' },
          { id: 156, name: '私信功能' },
          { id: 157, name: '礼物系统' },
          { id: 158, name: '红包功能' },
          { id: 159, name: '抽奖活动' },
          { id: 160, name: '签到系统' },
        ]
      }
    ]
  },
  {
    id: 2,
    name: '首页功能',
    children: [
      {
        id: 21,
        name: '聊天页面',
        children: [
          { id: 211, name: '个人页面' },
          { id: 212, name: '消息列表' },
          { id: 213, name: '好友管理' },
          { id: 214, name: '群聊功能' },
          { id: 215, name: '文件传输' },
          { id: 216, name: '语音消息' },
          { id: 217, name: '视频通话' },
          { id: 218, name: '表情贴纸' },
          { id: 219, name: '消息加密' },
          { id: 220, name: '阅后即焚' },
          { id: 221, name: '消息撤回' },
          { id: 222, name: '消息转发' },
        ]
      },
      {
        id: 22,
        name: '发现页面',
        children: [
          { id: 231, name: '推荐内容' },
          { id: 232, name: '热门话题' },
          { id: 233, name: '附近的人' },
          { id: 234, name: '活动中心' },
          { id: 235, name: '兴趣圈子' },
          { id: 236, name: '内容创作' },
          { id: 237, name: '直播推荐' },
          { id: 238, name: '短视频' },
        ]
      }
    ]
  },
  {
    id: 3,
    name: '房间玩法',
    children: [
      {
        id: 31,
        name: '动态广场',
        children: [
          { id: 311, name: '投投票' },
          { id: 312, name: '动态发布' },
          { id: 313, name: '图片分享' },
          { id: 314, name: '视频分享' },
          { id: 315, name: '话题讨论' },
          { id: 316, name: '点赞评论' },
          { id: 317, name: '转发分享' },
          { id: 318, name: '收藏功能' },
          { id: 319, name: '举报内容' },
          { id: 320, name: '屏蔽用户' },
        ]
      },
      {
        id: 32,
        name: '游戏中心',
        children: [
          { id: 331, name: '小游戏' },
          { id: 332, name: '竞技游戏' },
          { id: 333, name: '休闲游戏' },
          { id: 334, name: '排行榜' },
          { id: 335, name: '成就系统' },
          { id: 336, name: '游戏商城' },
          { id: 337, name: '游戏社区' },
          { id: 338, name: '比赛活动' },
        ]
      }
    ]
  }
]

// 状态管理
const selectedValue = ref<string[]>([])
const apiData = ref<any[]>([])
const loading = ref(false)

// 获取真实API数据
async function fetchApiData() {
  try {
    loading.value = true
    const response = await getCategoryList()
    console.log('API响应:', response)
    
    if (response && Array.isArray(response)) {
      apiData.value = response.map((item: any) => ({
        id: item.id || item.value,
        name: item.name || item.label,
        children: item.children?.map((child: any) => ({
          id: child.id || child.value,
          name: child.name || child.label,
          children: child.children?.map((grandChild: any) => ({
            id: grandChild.id || grandChild.value,
            name: grandChild.name || grandChild.label,
          })) || []
        })) || []
      }))
    }
  } catch (error) {
    console.error('获取API数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理选择变化
function handleChange(value: string[], selectedItems: any[]) {
  console.log('选择的值:', value)
  console.log('选择的项目:', selectedItems)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchApiData()
})
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-4">
      <h1 class="text-2xl font-bold">级联下拉框 ScrollArea 测试</h1>
      <p class="text-muted-foreground">测试级联下拉框中 ScrollArea 的滚动效果</p>
    </div>

    <!-- ScrollArea 测试 -->
    <Card>
      <CardHeader>
        <CardTitle>ScrollArea 滚动测试</CardTitle>
        <CardDescription>使用大量数据测试级联下拉框的滚动功能</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>功能类型选择（大量数据）</Label>
          <CascadeDropdown
            v-model="selectedValue"
            :data="mockData"
            placeholder="请选择功能类型"
            search-placeholder="搜索功能"
            @change="handleChange"
          />
        </div>
        
        <div class="space-y-2">
          <Label>选择结果</Label>
          <div class="p-3 bg-muted rounded-md">
            <pre class="text-sm">{{ JSON.stringify(selectedValue, null, 2) }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- API数据测试 -->
    <Card>
      <CardHeader>
        <CardTitle>API数据 ScrollArea 测试</CardTitle>
        <CardDescription>使用真实API数据测试滚动效果</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>功能类型选择（API数据）</Label>
          <CascadeDropdown
            :data="apiData"
            :loading="loading"
            placeholder="请选择功能类型"
            search-placeholder="搜索功能"
          />
        </div>
        
        <Button @click="fetchApiData" :disabled="loading">
          <Icon v-if="loading" name="i-lucide-loader-2" class="mr-2 size-4 animate-spin" />
          重新获取API数据
        </Button>
      </CardContent>
    </Card>

    <!-- 使用说明 -->
    <Card>
      <CardHeader>
        <CardTitle>ScrollArea 特性说明</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <h4 class="font-medium">主要改进：</h4>
          <ul class="list-disc list-inside space-y-1 text-sm text-muted-foreground">
            <li>主下拉菜单使用 <code>ScrollArea</code> 包裹，高度固定为 400px</li>
            <li>二级子菜单使用 <code>ScrollArea</code> 包裹，高度固定为 300px</li>
            <li>三级子菜单使用 <code>ScrollArea</code> 包裹，高度固定为 300px</li>
            <li>所有级别都支持平滑滚动和自定义滚动条样式</li>
            <li>恢复了 <code>ChevronRight</code> 图标显示</li>
          </ul>
        </div>
        
        <div class="space-y-2">
          <h4 class="font-medium">测试方法：</h4>
          <ul class="list-disc list-inside space-y-1 text-sm text-muted-foreground">
            <li>点击"首页-娱乐"查看大量子选项的滚动效果</li>
            <li>在"爱意房/魅力房"中查看三级菜单的滚动</li>
            <li>使用搜索功能过滤选项</li>
            <li>观察滚动条的样式和交互</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
