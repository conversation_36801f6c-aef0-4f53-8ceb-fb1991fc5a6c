<script setup lang="ts">
import { debugMediaUrl, formatMediaUrl } from '~/utils/mediaUrl'

// 测试URL
const testUrls = [
  'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4',
  'https://picsum.photos/800/600?random=1',
  'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
]

const testResults = ref<any[]>([])

function testUrlFormatting() {
  testResults.value = testUrls.map((url) => {
    const formatted = formatMediaUrl(url)
    const debug = debugMediaUrl(url)
    return {
      original: url,
      formatted,
      debug,
    }
  })
}

function testDirectProxy() {
  // 直接测试代理URL
  const proxyUrl = '/api/proxy/obs/22870a5133574988be17e16071bdecf5.mp4'
  console.log('测试直接代理URL:', proxyUrl)

  // 创建一个视频元素来测试
  const video = document.createElement('video')
  video.src = proxyUrl
  video.controls = true
  video.style.width = '400px'
  video.style.height = '300px'

  // 添加事件监听器
  video.addEventListener('loadstart', () => console.log('视频开始加载'))
  video.addEventListener('loadedmetadata', () => console.log('视频元数据加载完成'))
  video.addEventListener('canplay', () => console.log('视频可以播放'))
  video.addEventListener('error', e => console.error('视频加载错误:', e))

  // 添加到页面
  const container = document.getElementById('video-test-container')
  if (container) {
    container.innerHTML = ''
    container.appendChild(video)
  }
}

onMounted(() => {
  testUrlFormatting()
})
</script>

<template>
  <div class="mx-auto p-6 container space-y-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold">
        媒体代理测试页面
      </h1>
      <p class="mt-2 text-muted-foreground">
        测试媒体URL格式化和代理功能
      </p>
    </div>

    <!-- URL格式化测试 -->
    <Card>
      <CardHeader>
        <CardTitle>URL格式化测试</CardTitle>
        <CardDescription>
          测试不同类型URL的格式化结果
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button class="mb-4" @click="testUrlFormatting">
          重新测试URL格式化
        </Button>

        <div v-if="testResults.length > 0" class="space-y-4">
          <div v-for="(result, index) in testResults" :key="index" class="border rounded-lg p-4">
            <h4 class="mb-2 font-medium">
              测试 {{ index + 1 }}
            </h4>
            <div class="text-sm space-y-1">
              <div><strong>原始URL:</strong> <code class="rounded bg-muted px-1">{{ result.original }}</code></div>
              <div><strong>格式化URL:</strong> <code class="rounded bg-muted px-1">{{ result.formatted }}</code></div>
              <div><strong>是否使用代理:</strong> {{ result.debug.isUsingProxy ? '是' : '否' }}</div>
              <div><strong>是否OBS URL:</strong> {{ result.debug.isObsUrl ? '是' : '否' }}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 直接代理测试 -->
    <Card>
      <CardHeader>
        <CardTitle>直接代理测试</CardTitle>
        <CardDescription>
          直接测试代理URL是否可用
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Button class="mb-4" @click="testDirectProxy">
          测试直接代理URL
        </Button>

        <div id="video-test-container" class="min-h-[200px] border rounded-lg p-4">
          <p class="text-muted-foreground">
            点击上方按钮测试视频代理
          </p>
        </div>
      </CardContent>
    </Card>

    <!-- 手动测试链接 -->
    <Card>
      <CardHeader>
        <CardTitle>手动测试链接</CardTitle>
        <CardDescription>
          可以直接点击这些链接测试代理功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-2">
          <div>
            <a
              href="/api/proxy/obs/22870a5133574988be17e16071bdecf5.mp4"
              target="_blank"
              class="text-blue-600 hover:underline"
            >
              测试华为云OBS视频代理
            </a>
          </div>
          <div>
            <a
              href="https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4"
              target="_blank"
              class="text-blue-600 hover:underline"
            >
              直接访问华为云OBS视频（可能CORS错误）
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
