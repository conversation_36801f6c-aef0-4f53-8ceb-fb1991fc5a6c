<script setup lang="ts">
import { requestClient } from '~/utils/request'

const isLoading = ref(false)
const testResults = ref<string[]>([])

function addResult(message: string) {
  testResults.value.unshift(`[${new Date().toLocaleTimeString()}] ${message}`)
}

// 测试真实的用户信息API（应该会触发token无效）
async function testRealUserInfoAPI() {
  isLoading.value = true
  addResult('开始测试真实用户信息API...')
  
  try {
    // 先清除认证状态，模拟token无效
    const authStore = useAuthStore()
    authStore.clearAuthData()
    addResult('已清除认证数据')
    
    // 调用需要认证的API
    const result = await requestClient.get('/user/info')
    addResult(`API调用成功: ${JSON.stringify(result)}`)
  } catch (error: any) {
    addResult(`API调用失败: ${error.message}`)
    addResult(`错误详情: ${JSON.stringify(error.errorResult || error)}`)
    
    // 检查是否为认证错误
    if (error.errorResult?.isAuthError) {
      addResult('✅ 正确识别为认证错误')
    } else {
      addResult('❌ 未识别为认证错误')
    }
  } finally {
    isLoading.value = false
  }
}

// 测试模拟的token无效API
async function testMockTokenInvalidAPI() {
  isLoading.value = true
  addResult('开始测试模拟token无效API...')
  
  try {
    const result = await requestClient.post('/test/token-invalid-2004')
    addResult(`API调用成功: ${JSON.stringify(result)}`)
  } catch (error: any) {
    addResult(`API调用失败: ${error.message}`)
    addResult(`错误详情: ${JSON.stringify(error.errorResult || error)}`)
    
    // 检查是否为认证错误
    if (error.errorResult?.isAuthError) {
      addResult('✅ 正确识别为认证错误')
    } else {
      addResult('❌ 未识别为认证错误')
    }
  } finally {
    isLoading.value = false
  }
}

// 手动触发认证过期弹窗
async function showAuthModal() {
  addResult('手动触发认证过期弹窗...')
  try {
    const modal = useModal()
    const result = await modal.showAuthExpiredModal()
    addResult(`弹窗结果: ${result}`)
  } catch (error: any) {
    addResult(`弹窗失败: ${error.message}`)
  }
}

// 清除测试结果
function clearResults() {
  testResults.value = []
}

// 检查当前认证状态
function checkAuthStatus() {
  const authStore = useAuthStore()
  addResult(`当前认证状态: 登录=${authStore.isLoggedIn}, 用户=${authStore.user?.user_name || '无'}`)
}
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">认证错误处理测试</h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 测试按钮区域 -->
        <div class="space-y-4">
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h2 class="text-lg font-semibold text-blue-800 mb-3">测试操作</h2>
            
            <div class="space-y-3">
              <Button 
                @click="checkAuthStatus"
                variant="outline"
                class="w-full"
              >
                检查当前认证状态
              </Button>
              
              <Button 
                @click="testMockTokenInvalidAPI"
                :disabled="isLoading"
                class="w-full"
              >
                测试模拟Token无效API
              </Button>
              
              <Button 
                @click="testRealUserInfoAPI"
                :disabled="isLoading"
                class="w-full"
              >
                测试真实用户信息API
              </Button>
              
              <Button 
                @click="showAuthModal"
                :disabled="isLoading"
                variant="destructive"
                class="w-full"
              >
                手动显示认证过期弹窗
              </Button>
              
              <Button 
                @click="clearResults"
                variant="outline"
                class="w-full"
              >
                清除测试结果
              </Button>
            </div>
          </div>
          
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 class="font-medium mb-2 text-yellow-800">预期行为</h3>
            <ul class="text-sm text-yellow-700 space-y-1">
              <li>• API返回2004错误码时应显示认证过期弹窗</li>
              <li>• 弹窗应该是模态的，不能随意关闭</li>
              <li>• 点击"立即登录"应跳转到登录页</li>
              <li>• 错误应被正确识别为认证错误</li>
            </ul>
          </div>
        </div>
        
        <!-- 测试结果区域 -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h2 class="text-lg font-semibold mb-3">测试结果</h2>
          
          <div class="bg-black text-green-400 p-3 rounded font-mono text-sm h-96 overflow-y-auto">
            <div v-if="testResults.length === 0" class="text-gray-500">
              暂无测试结果...
            </div>
            <div 
              v-for="(result, index) in testResults" 
              :key="index"
              class="mb-1"
            >
              {{ result }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
