<script setup lang="ts">
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ref } from 'vue'

definePageMeta({
  title: '弹窗视频测试',
  layout: 'default',
})

// 测试附件数据
const testAttachments = ref<AttachmentItem[]>([
  {
    id: 'obs-video-1',
    name: '华为云OBS视频.mp4',
    url: 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4',
    type: 'mp4',
    size: 5242880,
  },
  {
    id: 'online-video-1',
    name: '在线测试视频.mp4',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    type: 'mp4',
    size: 15728640,
  },
  {
    id: 'test-image-1',
    name: '测试图片.jpg',
    url: 'https://picsum.photos/800/600?random=1',
    type: 'jpg',
    size: 245760,
  },
])

// 单个视频测试
const singleVideoAttachment = ref<AttachmentItem[]>([
  {
    id: 'single-obs-video',
    name: '单独华为云OBS视频.mp4',
    url: 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4',
    type: 'mp4',
    size: 5242880,
  },
])

// 处理下载事件
function handleDownload(attachment: AttachmentItem) {
  console.log('下载附件:', attachment)
}

// 测试日志
const testLogs = ref<string[]>([])

function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  testLogs.value.unshift(`[${timestamp}] ${message}`)
  console.log(message)
}

// 监听控制台日志
const originalConsoleLog = console.log
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

console.log = (...args) => {
  if (args[0] && typeof args[0] === 'string' && args[0].includes('视频')) {
    addLog(`LOG: ${args.join(' ')}`)
  }
  originalConsoleLog.apply(console, args)
}

console.error = (...args) => {
  if (args[0] && typeof args[0] === 'string' && (args[0].includes('视频') || args[0].includes('媒体'))) {
    addLog(`ERROR: ${args.join(' ')}`)
  }
  originalConsoleError.apply(console, args)
}

console.warn = (...args) => {
  if (args[0] && typeof args[0] === 'string' && args[0].includes('视频')) {
    addLog(`WARN: ${args.join(' ')}`)
  }
  originalConsoleWarn.apply(console, args)
}

function clearLogs() {
  testLogs.value = []
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold">
        弹窗视频测试页面
      </h1>
      <p class="mt-2 text-muted-foreground">
        专门测试弹窗预览模式下的视频播放问题
      </p>
    </div>

    <!-- 测试说明 -->
    <Card>
      <CardHeader>
        <CardTitle>测试说明</CardTitle>
        <CardDescription>
          点击下方的附件缩略图打开弹窗预览，观察视频是否正常播放
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="text-sm space-y-2">
          <p><strong>测试步骤:</strong></p>
          <ol class="ml-4 list-decimal list-inside space-y-1">
            <li>点击视频附件的缩略图</li>
            <li>观察弹窗是否正常打开</li>
            <li>检查视频是否能正常加载和播放</li>
            <li>查看下方的日志输出</li>
            <li>测试视频控制功能（播放、暂停、进度条等）</li>
          </ol>
          <p><strong>预期结果:</strong></p>
          <ul class="ml-4 list-disc list-inside space-y-1">
            <li>华为云OBS视频应该通过代理正常播放</li>
            <li>在线测试视频应该直接播放</li>
            <li>视频控制功能应该正常工作</li>
            <li>弹窗尺寸应该适配视频大小</li>
          </ul>
        </div>
      </CardContent>
    </Card>

    <!-- 多附件测试 -->
    <Card>
      <CardHeader>
        <CardTitle>多附件测试</CardTitle>
        <CardDescription>
          包含华为云OBS视频、在线视频和图片的混合测试
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          :attachments="testAttachments"
          :max-thumbnails="8"
          thumbnail-size="lg"
          :show-file-name="true"
          :allow-download="true"
          @download="handleDownload"
        />
      </CardContent>
    </Card>

    <!-- 单个视频测试 -->
    <Card>
      <CardHeader>
        <CardTitle>单个华为云OBS视频测试</CardTitle>
        <CardDescription>
          专门测试华为云OBS视频的弹窗预览
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          :attachments="singleVideoAttachment"
          :max-thumbnails="1"
          thumbnail-size="lg"
          :show-file-name="true"
          :allow-download="true"
          @download="handleDownload"
        />
      </CardContent>
    </Card>

    <!-- 测试日志 -->
    <Card>
      <CardHeader>
        <CardTitle>测试日志</CardTitle>
        <CardDescription>
          实时显示视频加载和播放相关的日志信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">日志数量: {{ testLogs.length }}</span>
            <Button variant="outline" size="sm" @click="clearLogs">
              清空日志
            </Button>
          </div>

          <div v-if="testLogs.length > 0" class="max-h-60 overflow-y-auto border rounded-lg bg-muted/30 p-3">
            <div
              v-for="(log, index) in testLogs"
              :key="index"
              class="py-1 text-xs font-mono"
              :class="{
                'text-red-600': log.includes('ERROR'),
                'text-yellow-600': log.includes('WARN'),
                'text-blue-600': log.includes('LOG'),
              }"
            >
              {{ log }}
            </div>
          </div>

          <div v-else class="py-8 text-center text-muted-foreground">
            暂无日志，点击附件预览开始测试
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 调试信息 -->
    <Card>
      <CardHeader>
        <CardTitle>调试信息</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="text-sm space-y-2">
          <div><strong>浏览器:</strong> {{ navigator.userAgent.split(' ').slice(-2).join(' ') }}</div>
          <div><strong>视口尺寸:</strong> {{ window.innerWidth }}x{{ window.innerHeight }}</div>
          <div><strong>支持的视频格式:</strong></div>
          <ul class="ml-4 list-disc list-inside space-y-1">
            <li>MP4: {{ document.createElement('video').canPlayType('video/mp4') || '不支持' }}</li>
            <li>WebM: {{ document.createElement('video').canPlayType('video/webm') || '不支持' }}</li>
            <li>OGG: {{ document.createElement('video').canPlayType('video/ogg') || '不支持' }}</li>
          </ul>
          <div class="mt-4 rounded-lg bg-muted p-3">
            <strong>故障排除提示:</strong>
            <ul class="mt-2 list-disc list-inside space-y-1">
              <li>如果视频无法播放，检查浏览器控制台的错误信息</li>
              <li>如果是华为云OBS视频，确认代理是否正常工作</li>
              <li>如果弹窗显示异常，检查CSS样式和布局</li>
              <li>如果视频控制失效，检查事件监听器</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
