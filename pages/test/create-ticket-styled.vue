<script setup lang="ts">
import { useCreateTicketModal } from '~/composables/useCreateTicketModal'

definePageMeta({
  title: '优化后的创建工单',
  layout: 'default',
})

const { showCreateTicketModal } = useCreateTicketModal()

async function handleCreateTicket() {
  try {
    const success = await showCreateTicketModal()
    if (success) {
      console.log('工单创建成功')
    }
    else {
      console.log('工单创建取消')
    }
  }
  catch (error) {
    console.error('创建工单失败:', error)
  }
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mx-auto max-w-2xl">
      <!-- 页面标题 -->
      <div class="mb-8 text-center">
        <h1 class="mb-2 text-3xl text-foreground font-bold">
          紧凑优化的创建工单表单
        </h1>
        <p class="text-muted-foreground">
          更紧凑的布局设计，统一的颜色方案，提供简洁高效的用户体验
        </p>
      </div>

      <!-- 功能特点 -->
      <div class="grid grid-cols-1 mb-8 gap-4 md:grid-cols-2">
        <div class="flex items-center gap-3 border rounded-lg bg-card p-4">
          <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary/10">
            <Icon name="i-lucide-layout" class="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 class="font-semibold">
              分组布局
            </h3>
            <p class="text-sm text-muted-foreground">
              相关字段分组，层次清晰
            </p>
          </div>
        </div>

        <div class="flex items-center gap-3 border rounded-lg bg-card p-4">
          <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-green-100">
            <Icon name="i-lucide-grid" class="h-5 w-5 text-green-600" />
          </div>
          <div>
            <h3 class="font-semibold">
              网格布局
            </h3>
            <p class="text-sm text-muted-foreground">
              多列布局，空间利用率高
            </p>
          </div>
        </div>

        <div class="flex items-center gap-3 border rounded-lg bg-card p-4">
          <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-blue-100">
            <Icon name="i-lucide-palette" class="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 class="font-semibold">
              视觉优化
            </h3>
            <p class="text-sm text-muted-foreground">
              图标、颜色、间距优化
            </p>
          </div>
        </div>

        <div class="flex items-center gap-3 border rounded-lg bg-card p-4">
          <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-purple-100">
            <Icon name="i-lucide-zap" class="h-5 w-5 text-purple-600" />
          </div>
          <div>
            <h3 class="font-semibold">
              交互增强
            </h3>
            <p class="text-sm text-muted-foreground">
              悬停效果、状态反馈
            </p>
          </div>
        </div>
      </div>

      <!-- 创建工单按钮 -->
      <div class="text-center">
        <Button
          size="lg"
          class="h-12 min-w-[200px] text-base font-semibold"
          @click="handleCreateTicket"
        >
          <Icon name="i-lucide-plus-circle" class="mr-2 h-5 w-5" />
          体验优化后的创建工单
        </Button>
      </div>

      <!-- 优化说明 -->
      <div class="mt-12 space-y-6">
        <h2 class="text-center text-xl font-semibold">
          紧凑优化内容
        </h2>

        <div class="grid gap-4">
          <div class="border border-primary/20 rounded-lg bg-primary/5 p-5">
            <h3 class="mb-3 flex items-center gap-2 text-primary font-semibold">
              <Icon name="i-lucide-minimize-2" class="h-4 w-4" />
              紧凑布局设计
            </h3>
            <p class="text-sm text-muted-foreground leading-relaxed">
              减少间距：组间距从12调整为8，内容padding从8调整为6，字段间距统一为3，创造更紧凑但仍然清晰的布局。
            </p>
          </div>

          <div class="border border-primary/20 rounded-lg bg-primary/5 p-5">
            <h3 class="mb-3 flex items-center gap-2 text-primary font-semibold">
              <Icon name="i-lucide-palette" class="h-4 w-4" />
              统一颜色方案
            </h3>
            <p class="text-sm text-muted-foreground leading-relaxed">
              简化为三种颜色：主色调(primary)用于所有图标和强调元素，错误色(destructive)用于必填标识，成功色(green)仅用于附件上传成功状态。
            </p>
          </div>

          <div class="border border-primary/20 rounded-lg bg-primary/5 p-5">
            <h3 class="mb-3 flex items-center gap-2 text-primary font-semibold">
              <Icon name="i-lucide-layout" class="h-4 w-4" />
              一致的分组样式
            </h3>
            <p class="text-sm text-muted-foreground leading-relaxed">
              所有分组使用相同的卡片样式：rounded-lg边框、统一的背景色、相同的图标容器尺寸(6x6)和分割线样式。
            </p>
          </div>

          <div class="border border-primary/20 rounded-lg bg-primary/5 p-5">
            <h3 class="mb-3 flex items-center gap-2 text-primary font-semibold">
              <Icon name="i-lucide-zap" class="h-4 w-4" />
              优化的交互反馈
            </h3>
            <p class="text-sm text-muted-foreground leading-relaxed">
              保持良好的交互体验：悬停效果使用统一的primary色彩，TagsInput标签使用主题色，选择框有清晰的选中状态。
            </p>
          </div>

          <div class="border border-primary/20 rounded-lg bg-primary/5 p-5">
            <h3 class="mb-3 flex items-center gap-2 text-primary font-semibold">
              <Icon name="i-lucide-eye" class="h-4 w-4" />
              简化的视觉层次
            </h3>
            <p class="text-sm text-muted-foreground leading-relaxed">
              减少视觉复杂度：移除渐变效果、统一边框样式、简化阴影效果，保持清晰的层次但更加简洁。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
