<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import { useModal } from '~/composables/useModal'
import { useTicketOperations } from '~/composables/useTicketOperations'

definePageMeta({
  title: '工单操作测试',
  layout: 'default',
})

// 模拟工单数据
const mockTickets: Ticket[] = [
  {
    id: 'test-1',
    ticketID: 'TK001',
    title: '测试工单 - 待处理',
    text: '这是一个待处理的测试工单',
    status: '待处理',
    stage: '待处理',
    creator: 'admin',
    handler: '',
    createdAt: '2024-01-01',
  },
  {
    id: 'test-2',
    ticketID: 'TK002',
    title: '测试工单 - 处理中',
    text: '这是一个处理中的测试工单',
    status: '处理中',
    stage: '处理中',
    creator: 'user1',
    handler: 'admin',
    createdAt: '2024-01-02',
  },
  {
    id: 'test-3',
    ticketID: 'TK003',
    title: '测试工单 - 已处理',
    text: '这是一个已处理的测试工单',
    status: '已处理',
    stage: '已处理',
    creator: 'user2',
    handler: 'admin',
    createdAt: '2024-01-03',
  },
]

const selectedTicket = ref<Ticket>(mockTickets[0])

// 统一的工单操作管理
const {
  // 权限和状态检查
  checkPermission,
  checkTicketStatus,

  // 工单操作
  deleteTicketAction,
  handleTicketAction,
  assignTicketAction,
  completeTicketAction,
  checkTicketAction,
  rejectTicketAction,
  urgeTicketAction,
} = useTicketOperations()

const modal = useModal()

// 操作方法
async function handleDeleteTicket() {
  const confirmed = await modal.confirm(
    `确定要删除"工单 #${selectedTicket.value.ticketID}"吗？此操作不可撤销。`,
    '确认删除',
    {
      type: 'error',
      confirmText: '删除',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    await deleteTicketAction(selectedTicket.value)
  }
}

async function handleClaimTicket() {
  await handleTicketAction(selectedTicket.value)
}

async function handleAssignTicket() {
  // 在实际应用中，这里应该使用模态框来选择用户
  // 这里仅用于测试，使用简单的输入方式
  // eslint-disable-next-line no-alert
  const assignee = window.prompt('请输入要指派给的用户名:')
  if (assignee) {
    await assignTicketAction(selectedTicket.value, assignee)
  }
}

async function handleCompleteTicket() {
  // 在实际应用中，这里应该使用模态框来输入详细信息
  // 这里仅用于测试，使用简单的输入方式
  // eslint-disable-next-line no-alert
  const result = window.prompt('请输入处理结果:')
  // eslint-disable-next-line no-alert
  const cause = window.prompt('请输入问题原因:')
  if (result && cause) {
    await completeTicketAction(selectedTicket.value, result, cause)
  }
}

async function handleArchiveTicket() {
  const confirmed = await modal.confirm(
    `确定要归档"工单 #${selectedTicket.value.ticketID}"吗？`,
    '确认归档',
    {
      type: 'warning',
      confirmText: '归档',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    await checkTicketAction(selectedTicket.value)
  }
}

async function handleRejectTicket() {
  // 在实际应用中，这里应该使用模态框来输入驳回原因
  // 这里仅用于测试，使用简单的输入方式
  // eslint-disable-next-line no-alert
  const reason = window.prompt('请输入驳回原因:')
  if (reason) {
    await rejectTicketAction(selectedTicket.value, reason)
  }
}

async function handleUrgeTicket() {
  const confirmed = await modal.confirm(
    `确定要加急"工单 #${selectedTicket.value.ticketID}"吗？`,
    '确认加急',
    {
      type: 'warning',
      confirmText: '加急',
      cancelText: '取消',
    },
  )
  if (confirmed) {
    await urgeTicketAction(selectedTicket.value)
  }
}

// 获取操作按钮状态
function getActionStatus(action: string) {
  const hasPermission = checkPermission(action as any, selectedTicket.value)
  const hasValidStatus = checkTicketStatus(action as any, selectedTicket.value)
  return {
    hasPermission,
    hasValidStatus,
    canExecute: hasPermission && hasValidStatus,
  }
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mx-auto max-w-4xl">
      <h1 class="mb-6 text-2xl font-bold">
        工单操作测试
      </h1>

      <!-- 工单选择 -->
      <div class="mb-6">
        <h2 class="mb-3 text-lg font-semibold">
          选择测试工单
        </h2>
        <div class="grid gap-3 md:grid-cols-3">
          <Card
            v-for="ticket in mockTickets"
            :key="ticket.id"
            class="cursor-pointer transition-colors"
            :class="selectedTicket.id === ticket.id ? 'ring-2 ring-primary' : ''"
            @click="selectedTicket = ticket"
          >
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="font-medium">
                    {{ ticket.ticketID }}
                  </h3>
                  <p class="text-sm text-muted-foreground">
                    {{ ticket.title }}
                  </p>
                </div>
                <Badge
                  :variant="ticket.status === '待处理' ? 'destructive'
                    : ticket.status === '处理中' ? 'default'
                      : ticket.status === '已处理' ? 'secondary' : 'outline'"
                >
                  {{ ticket.status }}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- 当前选中工单信息 -->
      <div class="mb-6">
        <h2 class="mb-3 text-lg font-semibold">
          当前工单信息
        </h2>
        <Card>
          <CardContent class="p-4">
            <div class="grid gap-2">
              <div><strong>工单ID:</strong> {{ selectedTicket.ticketID }}</div>
              <div><strong>标题:</strong> {{ selectedTicket.title }}</div>
              <div><strong>状态:</strong> {{ selectedTicket.status }}</div>
              <div><strong>创建人:</strong> {{ selectedTicket.creator }}</div>
              <div><strong>处理人:</strong> {{ selectedTicket.handler || '未分配' }}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 操作按钮 -->
      <div class="mb-6">
        <h2 class="mb-3 text-lg font-semibold">
          工单操作
        </h2>
        <div class="grid gap-3 lg:grid-cols-4 md:grid-cols-2">
          <!-- 认领工单 -->
          <Button
            :disabled="!getActionStatus('handle').canExecute"
            @click="handleClaimTicket"
          >
            认领工单
          </Button>

          <!-- 指派工单 -->
          <Button
            :disabled="!getActionStatus('assign').canExecute"
            @click="handleAssignTicket"
          >
            指派工单
          </Button>

          <!-- 完成工单 -->
          <Button
            :disabled="!getActionStatus('complete').canExecute"
            @click="handleCompleteTicket"
          >
            完成工单
          </Button>

          <!-- 归档工单 -->
          <Button
            :disabled="!getActionStatus('check').canExecute"
            @click="handleArchiveTicket"
          >
            归档工单
          </Button>

          <!-- 驳回工单 -->
          <Button
            variant="destructive"
            :disabled="!getActionStatus('reject').canExecute"
            @click="handleRejectTicket"
          >
            驳回工单
          </Button>

          <!-- 加急工单 -->
          <Button
            variant="outline"
            :disabled="!getActionStatus('urge').canExecute"
            @click="handleUrgeTicket"
          >
            加急工单
          </Button>

          <!-- 删除工单 -->
          <Button
            variant="destructive"
            :disabled="!getActionStatus('delete').canExecute"
            @click="handleDeleteTicket"
          >
            删除工单
          </Button>
        </div>
      </div>

      <!-- 权限和状态检查结果 -->
      <div class="mb-6">
        <h2 class="mb-3 text-lg font-semibold">
          权限和状态检查
        </h2>
        <div class="grid gap-3 md:grid-cols-2">
          <Card v-for="action in ['handle', 'assign', 'complete', 'check', 'reject', 'urge', 'delete']" :key="action">
            <CardContent class="p-4">
              <h3 class="mb-2 font-medium">
                {{ action }} 操作
              </h3>
              <div class="text-sm space-y-1">
                <div class="flex justify-between">
                  <span>权限检查:</span>
                  <Badge :variant="getActionStatus(action).hasPermission ? 'default' : 'destructive'">
                    {{ getActionStatus(action).hasPermission ? '通过' : '拒绝' }}
                  </Badge>
                </div>
                <div class="flex justify-between">
                  <span>状态检查:</span>
                  <Badge :variant="getActionStatus(action).hasValidStatus ? 'default' : 'destructive'">
                    {{ getActionStatus(action).hasValidStatus ? '通过' : '拒绝' }}
                  </Badge>
                </div>
                <div class="flex justify-between">
                  <span>可执行:</span>
                  <Badge :variant="getActionStatus(action).canExecute ? 'default' : 'destructive'">
                    {{ getActionStatus(action).canExecute ? '是' : '否' }}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>
