<script setup lang="ts">
definePageMeta({
  title: '简单视频测试',
  layout: 'default',
})

// 测试URL
const originalUrl = 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4'
const proxyUrl = '/api/proxy/obs/22870a5133574988be17e16071bdecf5.mp4'

function handleVideoLoad() {
  console.log('视频加载成功')
}

function handleVideoError(event: Event) {
  console.error('视频加载失败:', event)
}

function handleVideoLoadStart() {
  console.log('视频开始加载')
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-6">
    <h1 class="text-3xl font-bold">简单视频测试</h1>
    
    <div class="space-y-8">
      <!-- 代理视频 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">代理视频 (应该成功)</h2>
        <video
          :src="proxyUrl"
          controls
          class="w-full max-w-2xl border rounded-lg"
          preload="metadata"
          @loadstart="handleVideoLoadStart"
          @canplay="handleVideoLoad"
          @error="handleVideoError"
        >
          您的浏览器不支持视频播放。
        </video>
        <p class="text-sm text-muted-foreground">URL: {{ proxyUrl }}</p>
      </div>

      <!-- 原始视频 (会失败) -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">原始视频 (会失败 - CORS)</h2>
        <video
          :src="originalUrl"
          controls
          class="w-full max-w-2xl border rounded-lg"
          preload="metadata"
          @loadstart="() => console.log('原始视频开始加载')"
          @canplay="() => console.log('原始视频加载成功')"
          @error="(e) => console.error('原始视频加载失败:', e)"
        >
          您的浏览器不支持视频播放。
        </video>
        <p class="text-sm text-muted-foreground">URL: {{ originalUrl }}</p>
      </div>
    </div>
  </div>
</template>
