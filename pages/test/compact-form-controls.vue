<script setup lang="ts">
import { ref } from 'vue'

const radioValue = ref('option1')
const checkboxValues = ref(['option1'])
const singleCheckbox = ref(false)

const radioOptions = [
  { value: 'option1', label: '选项一' },
  { value: 'option2', label: '选项二' },
  { value: 'option3', label: '选项三' },
  { value: 'option4', label: '选项四' },
]

const checkboxOptions = [
  { value: 'option1', label: '功能模块A' },
  { value: 'option2', label: '功能模块B' },
  { value: 'option3', label: '功能模块C' },
  { value: 'option4', label: '功能模块D' },
]

function handleCheckboxChange(value: string, checked: boolean) {
  if (checked) {
    checkboxValues.value.push(value)
  }
  else {
    const index = checkboxValues.value.indexOf(value)
    if (index > -1) {
      checkboxValues.value.splice(index, 1)
    }
  }
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">
        紧凑型表单控件
      </h1>
      <p class="text-muted-foreground">
        优化后的单选和多选组件，去除外边框和阴影，使用更轻量的环形边框设计
      </p>
      <div class="flex gap-2">
        <Badge variant="outline">
          无外边框
        </Badge>
        <Badge variant="outline">
          无阴影
        </Badge>
        <Badge variant="outline">
          紧凑间距
        </Badge>
        <Badge variant="outline">
          平滑过渡
        </Badge>
      </div>
    </div>

    <div class="grid gap-8 md:grid-cols-2">
      <!-- 单选组件 -->
      <Card>
        <CardHeader>
          <CardTitle>单选组件 (Radio Group)</CardTitle>
          <CardDescription>
            去除外边框和阴影，使用环形边框和实心圆点指示器
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
          <!-- 传统小圆点布局 -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">传统小圆点布局</Label>
            <div class="flex gap-4">
              <div v-for="option in radioOptions.slice(0, 3)" :key="option.value" class="flex items-center space-x-2">
                <RadioGroupItem :id="`traditional-${option.value}`" :value="option.value" />
                <Label :for="`traditional-${option.value}`" class="cursor-pointer text-sm">{{ option.label }}</Label>
              </div>
            </div>
          </div>

          <!-- 卡片式布局（推荐） -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">卡片式布局（推荐）- 水平平铺，扩大点击区域</Label>
            <RadioGroup v-model="radioValue" class="flex gap-2">
              <RadioGroupCard
                v-for="option in radioOptions"
                :id="`card-${option.value}`"
                :key="option.value"
                :value="option.value"
                :label="option.label"
                :checked="radioValue === option.value"
              />
            </RadioGroup>
          </div>

          <div class="text-sm text-muted-foreground">
            当前选择: <Badge variant="outline">
              {{ radioValue }}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <!-- 多选组件 -->
      <Card>
        <CardHeader>
          <CardTitle>多选组件 (Checkbox)</CardTitle>
          <CardDescription>
            去除外边框和阴影，使用环形边框和勾选图标指示器
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
          <!-- 单个复选框 -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">单个复选框</Label>
            <div class="flex items-center space-x-2">
              <Checkbox id="single" v-model:checked="singleCheckbox" />
              <Label for="single" class="cursor-pointer text-sm">
                我同意服务条款和隐私政策
              </Label>
            </div>
          </div>

          <!-- 传统复选框布局 -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">传统复选框布局</Label>
            <div class="space-y-2">
              <div v-for="option in checkboxOptions.slice(0, 2)" :key="option.value" class="flex items-center space-x-2">
                <Checkbox
                  :id="`traditional-${option.value}`"
                  :checked="checkboxValues.includes(option.value)"
                  @update:checked="(checked) => handleCheckboxChange(option.value, checked)"
                />
                <Label :for="`traditional-${option.value}`" class="cursor-pointer text-sm">{{ option.label }}</Label>
              </div>
            </div>
          </div>

          <!-- 卡片式多选布局（推荐） -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">卡片式多选布局（推荐）- 水平平铺，扩大点击区域</Label>
            <div class="flex flex-wrap gap-2">
              <CheckboxCard
                v-for="option in checkboxOptions"
                :id="`card-${option.value}`"
                :key="option.value"
                :checked="checkboxValues.includes(option.value)"
                :label="option.label"
                @update:checked="(checked) => handleCheckboxChange(option.value, checked)"
              />
            </div>
          </div>

          <div class="space-y-2">
            <div class="text-sm text-muted-foreground">
              单个复选框: <Badge variant="outline">
                {{ singleCheckbox ? '已选中' : '未选中' }}
              </Badge>
            </div>
            <div class="text-sm text-muted-foreground">
              多选结果:
              <div class="mt-1 flex flex-wrap gap-1">
                <Badge v-for="value in checkboxValues" :key="value" variant="outline">
                  {{ checkboxOptions.find(opt => opt.value === value)?.label }}
                </Badge>
                <Badge v-if="checkboxValues.length === 0" variant="secondary">
                  无选择
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 紧凑表单示例 -->
    <Card>
      <CardHeader>
        <CardTitle>紧凑表单示例</CardTitle>
        <CardDescription>
          在实际表单中的应用效果
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form class="max-w-md space-y-4">
          <div class="space-y-2">
            <Label class="text-sm font-medium">优先级</Label>
            <RadioGroup v-model="radioValue" class="flex gap-2">
              <RadioGroupCard
                id="priority-high"
                value="high"
                label="高"
                :checked="radioValue === 'high'"
              />
              <RadioGroupCard
                id="priority-medium"
                value="medium"
                label="中"
                :checked="radioValue === 'medium'"
              />
              <RadioGroupCard
                id="priority-low"
                value="low"
                label="低"
                :checked="radioValue === 'low'"
              />
            </RadioGroup>
          </div>

          <div class="space-y-2">
            <Label class="text-sm font-medium">通知设置</Label>
            <div class="flex flex-wrap gap-2">
              <CheckboxCard
                id="email-notify"
                label="邮件通知"
                :checked="checkboxValues.includes('email')"
                @update:checked="(checked) => handleCheckboxChange('email', checked)"
              />
              <CheckboxCard
                id="sms-notify"
                label="短信通知"
                :checked="checkboxValues.includes('sms')"
                @update:checked="(checked) => handleCheckboxChange('sms', checked)"
              />
              <CheckboxCard
                id="push-notify"
                label="推送通知"
                :checked="checkboxValues.includes('push')"
                @update:checked="(checked) => handleCheckboxChange('push', checked)"
              />
            </div>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 优化说明 -->
    <Card>
      <CardHeader>
        <CardTitle>优化说明</CardTitle>
        <CardDescription>
          本次优化的具体改进点
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-4 md:grid-cols-2">
          <div class="space-y-3">
            <h4 class="text-sm font-medium">
              单选组件 (RadioGroup) 优化
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 去除 <code class="rounded bg-muted px-1">border</code> 和 <code class="rounded bg-muted px-1">shadow</code></li>
              <li>• 使用轻量的 <code class="rounded bg-muted px-1">ring-1 ring-muted-foreground/30</code></li>
              <li>• 选中状态使用实心圆点而非勾选图标</li>
              <li>• 添加 <code class="rounded bg-muted px-1">transition-colors</code> 平滑过渡</li>
              <li>• 减少容器间距从 <code class="rounded bg-muted px-1">gap-2</code> 到 <code class="rounded bg-muted px-1">gap-1.5</code></li>
            </ul>
          </div>
          <div class="space-y-3">
            <h4 class="text-sm font-medium">
              多选组件 (Checkbox) 优化
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 去除 <code class="rounded bg-muted px-1">border</code> 和 <code class="rounded bg-muted px-1">shadow</code></li>
              <li>• 使用轻量的 <code class="rounded bg-muted px-1">ring-1 ring-muted-foreground/30</code></li>
              <li>• 缩小勾选图标从 <code class="rounded bg-muted px-1">h-4 w-4</code> 到 <code class="rounded bg-muted px-1">h-3 w-3</code></li>
              <li>• 添加 <code class="rounded bg-muted px-1">transition-colors</code> 平滑过渡</li>
              <li>• 优化悬停状态的视觉反馈</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
