<script setup lang="ts">
import type { TicketApiTypes } from '~/api/feedback/ticket'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { UserSelect } from '@/components/ui/user-select'

// 页面配置
definePageMeta({
  title: '人员选择组件 - 修复验证',
  layout: 'default',
})

// 响应式数据
const singleUser = ref<string>('')
const multipleUsers = ref<string[]>([])
const largeDataUsers = ref<string[]>([])

// 事件处理
function handleSingleChange(value: string, user: TicketApiTypes.UserInfo) {
  console.log('单选变化:', { value, user })
}

function handleMultipleChange(value: string[], users: TicketApiTypes.UserInfo[]) {
  console.log('多选变化:', { value, users })
}

function handleLargeDataChange(value: string[], users: TicketApiTypes.UserInfo[]) {
  console.log('大数据多选变化:', { value, users })
}

// 清空选择
function clearAll() {
  singleUser.value = ''
  multipleUsers.value = []
  largeDataUsers.value = []
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        人员选择组件 - 修复验证
      </h1>
      <p class="text-muted-foreground">
        验证修复后的功能：搜索功能、唯一ID、虚拟列表性能优化
      </p>
    </div>

    <!-- 修复验证说明 -->
    <Card>
      <CardHeader>
        <CardTitle>修复内容说明</CardTitle>
        <CardDescription>
          本次修复解决的主要问题
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-4 md:grid-cols-3">
          <div class="space-y-2">
            <h4 class="text-green-600 font-semibold">
              ✅ 搜索功能修复
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 修复搜索框自动清除问题</li>
              <li>• 使用自定义filter-function</li>
              <li>• 防抖搜索优化</li>
              <li>• 支持实时搜索</li>
            </ul>
          </div>

          <div class="space-y-2">
            <h4 class="text-blue-600 font-semibold">
              ✅ 唯一ID修复
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 使用email作为唯一标识</li>
              <li>• 修复选择逻辑</li>
              <li>• 移除测试数据</li>
              <li>• 直接使用API接口</li>
            </ul>
          </div>

          <div class="space-y-2">
            <h4 class="text-purple-600 font-semibold">
              ✅ 性能优化
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 添加虚拟列表支持</li>
              <li>• 100+数据自动启用</li>
              <li>• 解决卡顿问题</li>
              <li>• 提升渲染性能</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <div class="grid gap-6 md:grid-cols-2">
      <!-- 搜索功能测试 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Badge variant="outline">
              搜索测试
            </Badge>
            搜索功能验证
          </CardTitle>
          <CardDescription>
            测试搜索框是否正常工作，不会自动清除
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>单选搜索测试</Label>
            <UserSelect
              v-model="singleUser"
              placeholder="输入关键词搜索用户"
              search-placeholder="搜索用户（姓名、工号、邮箱）"
              @change="handleSingleChange"
            />
          </div>

          <div class="space-y-2">
            <Label>多选搜索测试</Label>
            <UserSelect
              v-model="multipleUsers"
              multiple
              placeholder="搜索并选择多个用户"
              search-placeholder="搜索用户（支持中文、拼音、工号）"
              :max-tag-count="2"
              @change="handleMultipleChange"
            />
          </div>

          <div class="space-y-2">
            <Label>当前选择</Label>
            <div class="text-sm space-y-1">
              <div>单选: {{ singleUser || '未选择' }}</div>
              <div>多选 ({{ multipleUsers.length }}): {{ multipleUsers.join(', ') || '未选择' }}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 虚拟列表测试 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Badge variant="outline">
              性能测试
            </Badge>
            虚拟列表验证
          </CardTitle>
          <CardDescription>
            当用户数量超过100时自动启用虚拟列表
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>大数据量测试</Label>
            <UserSelect
              v-model="largeDataUsers"
              multiple
              placeholder="测试大数据量性能"
              search-placeholder="搜索大量用户数据"
              :virtual-threshold="50"
              :max-tag-count="3"
              @change="handleLargeDataChange"
            />
            <p class="text-xs text-muted-foreground">
              虚拟列表阈值设置为50，超过50个用户时自动启用虚拟滚动
            </p>
          </div>

          <div class="space-y-2">
            <Label>选择结果 ({{ largeDataUsers.length }})</Label>
            <div class="text-sm">
              <div v-if="largeDataUsers.length === 0">
                未选择
              </div>
              <div v-else class="space-y-1">
                <div v-for="(email, index) in largeDataUsers.slice(0, 5)" :key="email" class="flex items-center gap-2">
                  <Badge variant="secondary" class="text-xs">
                    {{ index + 1 }}
                  </Badge>
                  <span class="text-xs">{{ email }}</span>
                </div>
                <div v-if="largeDataUsers.length > 5" class="text-xs text-muted-foreground">
                  还有 {{ largeDataUsers.length - 5 }} 个用户...
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 测试说明 -->
    <Card>
      <CardHeader>
        <CardTitle>测试指南</CardTitle>
        <CardDescription>
          如何验证修复效果
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="space-y-2">
            <h4 class="font-semibold">
              搜索功能测试：
            </h4>
            <ol class="list-decimal list-inside text-sm text-muted-foreground space-y-1">
              <li>点击搜索框，输入关键词（如用户名、工号等）</li>
              <li>验证搜索内容不会自动清除</li>
              <li>验证搜索结果实时更新</li>
              <li>验证选择用户后搜索框保持内容</li>
            </ol>
          </div>

          <div class="space-y-2">
            <h4 class="font-semibold">
              唯一ID测试：
            </h4>
            <ol class="list-decimal list-inside text-sm text-muted-foreground space-y-1">
              <li>选择多个不同用户，验证不会全部选中</li>
              <li>查看控制台输出，确认使用email作为唯一标识</li>
              <li>验证选择和取消选择逻辑正确</li>
            </ol>
          </div>

          <div class="space-y-2">
            <h4 class="font-semibold">
              性能测试：
            </h4>
            <ol class="list-decimal list-inside text-sm text-muted-foreground space-y-1">
              <li>在大数据量测试中搜索用户</li>
              <li>验证滚动流畅，无明显卡顿</li>
              <li>验证虚拟列表自动启用（超过阈值时）</li>
            </ol>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 操作按钮 -->
    <Card>
      <CardHeader>
        <CardTitle>测试操作</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="flex gap-2">
          <Button @click="clearAll">
            清空所有选择
          </Button>
          <Button variant="outline" @click="() => console.log('当前状态:', { singleUser, multipleUsers, largeDataUsers })">
            输出当前状态
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
