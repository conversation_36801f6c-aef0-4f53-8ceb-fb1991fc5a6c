<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ref } from 'vue'

// 快捷指令配置
const quickCommands = ref([
  {
    id: 1,
    text: '@Jarvis 打电话给张工',
    icon: '📞',
    description: '云呼叫工具',
  },
  {
    id: 2,
    text: '@Jarvis account服务最近有没有变更',
    icon: '🔍',
    description: '变更检查',
  },
  {
    id: 3,
    text: '@Jarvis account服务是否正常',
    icon: '📊',
    description: '状态检查',
  },
  {
    id: 4,
    text: '@Jarvis 故障结束',
    icon: '✅',
    description: '结束流程',
  },
])

const customMessage = ref('')
const testResults = ref<any[]>([])

// 测试函数 - 复制自聊天页面的逻辑
function testCommand(command: any) {
  const result = analyzeCommand(command.text)
  testResults.value.unshift({
    id: Date.now(),
    timestamp: new Date().toLocaleTimeString(),
    command: command.text,
    ...result,
  })
}

function testCustomCommand() {
  if (!customMessage.value.trim())
    return

  const result = analyzeCommand(customMessage.value)
  testResults.value.unshift({
    id: Date.now(),
    timestamp: new Date().toLocaleTimeString(),
    command: customMessage.value,
    ...result,
  })

  customMessage.value = ''
}

function analyzeCommand(content: string) {
  console.log('🔍 分析指令:', content)

  const result = {
    jarvisDetected: false,
    keywordMatched: false,
    extractedValue: '',
    functionTriggered: '',
  }

  // 检测@Jarvis
  const mentionKeywords = ['@Jarvis', '@jarvis', 'Jarvis', 'jarvis']
  result.jarvisDetected = mentionKeywords.some(keyword => content.includes(keyword))
  console.log('📢 @Jarvis检测:', result.jarvisDetected)

  if (!result.jarvisDetected) {
    return result
  }

  // 场景1：云呼叫请求
  if (content.includes('打电话给')) {
    console.log('📞 匹配云呼叫关键词')
    result.keywordMatched = true
    result.extractedValue = extractContactNameFromCall(content)
    result.functionTriggered = 'performCloudCall'
  }
  // 场景2：变更检查
  else if (content.includes('变更') || content.includes('有没有变更')) {
    console.log('🔍 匹配变更检查关键词')
    result.keywordMatched = true
    result.extractedValue = extractServiceName(content)
    result.functionTriggered = 'performChangeCheck'
  }
  // 场景3：状态检查
  else if (content.includes('是否正常') || content.includes('状态') || content.includes('异常')) {
    console.log('📊 匹配状态检查关键词')
    result.keywordMatched = true
    result.extractedValue = extractServiceName(content)
    result.functionTriggered = 'performStatusCheck'
  }
  // 场景4：故障结束
  else if (content.includes('故障结束')) {
    console.log('✅ 匹配故障结束关键词')
    result.keywordMatched = true
    result.extractedValue = '故障结束流程'
    result.functionTriggered = 'performFaultCompletion'
  }
  else {
    console.log('❌ 未匹配任何关键词')
  }

  console.log('📋 分析结果:', result)
  return result
}

// 复制的辅助函数
function extractContactNameFromCall(content: string): string {
  const callPattern = /打电话给(.+?)(?:\s|$|[，。])/
  const match = content.match(callPattern)

  if (match && match[1]) {
    return match[1].trim()
  }

  return '相关人员'
}

function extractServiceName(content: string): string {
  const servicePatterns = [
    /account[服务]*/g,
    /WebSocket[服务]*/g,
    /数据库[服务]*/g,
    /缓存[服务]*/g,
    /Redis[服务]*/g,
    /MySQL[服务]*/g,
    /API[服务]*/g,
    /用户[服务]*/g,
    /房间[服务]*/g,
    /支付[服务]*/g,
    /消息[服务]*/g,
    /推送[服务]*/g,
    /登录[服务]*/g,
    /认证[服务]*/g,
  ]

  for (const pattern of servicePatterns) {
    const matches = content.match(pattern)
    if (matches && matches.length > 0) {
      return matches[0]
    }
  }

  return '相关服务'
}

function clearResults() {
  testResults.value = []
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <h1 class="mb-6 text-2xl font-bold">
      快捷指令测试页面
    </h1>

    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
      <!-- 测试区域 -->
      <div class="space-y-4">
        <h2 class="text-lg font-semibold">
          快捷指令测试
        </h2>

        <div class="space-y-2">
          <Button
            v-for="command in quickCommands"
            :key="command.id"
            class="w-full justify-start"
            variant="outline"
            @click="testCommand(command)"
          >
            <span class="mr-2">{{ command.icon }}</span>
            {{ command.text }}
          </Button>
        </div>

        <div class="mt-4">
          <h3 class="mb-2 font-medium">
            自定义测试
          </h3>
          <div class="flex gap-2">
            <Input
              v-model="customMessage"
              placeholder="输入自定义@Jarvis命令"
              class="flex-1"
            />
            <Button @click="testCustomCommand">
              测试
            </Button>
          </div>
        </div>
      </div>

      <!-- 结果显示区域 -->
      <div class="space-y-4">
        <h2 class="text-lg font-semibold">
          测试结果
        </h2>

        <div class="h-96 overflow-y-auto border rounded-lg bg-gray-50 p-4">
          <div v-if="testResults.length === 0" class="text-center text-gray-500">
            点击左侧按钮开始测试
          </div>

          <div v-for="result in testResults" :key="result.id" class="mb-4 border rounded bg-white p-3">
            <div class="mb-1 text-sm text-gray-600">
              {{ result.timestamp }}
            </div>
            <div class="mb-2 font-medium">
              测试指令: {{ result.command }}
            </div>
            <div class="space-y-1">
              <div class="text-sm">
                <span class="font-medium">@Jarvis检测:</span>
                <span :class="result.jarvisDetected ? 'text-green-600' : 'text-red-600'">
                  {{ result.jarvisDetected ? '✅ 成功' : '❌ 失败' }}
                </span>
              </div>
              <div class="text-sm">
                <span class="font-medium">关键词匹配:</span>
                <span :class="result.keywordMatched ? 'text-green-600' : 'text-red-600'">
                  {{ result.keywordMatched ? '✅ 成功' : '❌ 失败' }}
                </span>
              </div>
              <div class="text-sm">
                <span class="font-medium">提取结果:</span>
                <span class="text-blue-600">{{ result.extractedValue }}</span>
              </div>
              <div class="text-sm">
                <span class="font-medium">触发函数:</span>
                <span :class="result.functionTriggered ? 'text-green-600' : 'text-red-600'">
                  {{ result.functionTriggered || '❌ 未触发' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <Button variant="outline" class="w-full" @click="clearResults">
          清空结果
        </Button>
      </div>
    </div>
  </div>
</template>
