<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import TicketWorkflowSteps from '~/components/feedback/TicketWorkflowSteps.vue'
import { useTicketWorkflow } from '~/composables/useTicketWorkflow'

definePageMeta({
  title: '工单流程测试',
  layout: 'default',
})

// 模拟不同状态的工单数据
const mockTickets: Ticket[] = [
  {
    id: 'test-1',
    ticketID: 'TK001',
    title: '测试工单 - 待处理',
    text: '这是一个待处理的测试工单',
    status: '待处理',
    stage: '待处理',
    creator: 'admin',
    feedbackPerson: 'admin',
    handler: '',
    createdAt: '2024-01-01 10:00:00',
    enterTime: '2024-01-01 10:00:00',
  },
  {
    id: 'test-2',
    ticketID: 'TK002',
    title: '测试工单 - 处理中',
    text: '这是一个处理中的测试工单',
    status: '处理中',
    stage: '处理中',
    creator: 'user1',
    feedbackPerson: 'user1',
    handler: 'admin',
    devProcessor: 'admin',
    createdAt: '2024-01-02 09:00:00',
    enterTime: '2024-01-02 09:00:00',
    responseTime: '2024-01-02 10:30:00',
  },
  {
    id: 'test-3',
    ticketID: 'TK003',
    title: '测试工单 - 已处理',
    text: '这是一个已处理的测试工单',
    status: '已处理',
    stage: '已处理',
    creator: 'user2',
    feedbackPerson: 'user2',
    handler: 'admin',
    devProcessor: 'admin',
    createdAt: '2024-01-03 08:00:00',
    enterTime: '2024-01-03 08:00:00',
    responseTime: '2024-01-03 09:15:00',
    endTime: '2024-01-03 14:30:00',
    result: '问题已解决，已修复相关功能',
  },
  {
    id: 'test-4',
    ticketID: 'TK004',
    title: '测试工单 - 已归档',
    text: '这是一个已归档的测试工单',
    status: '已归档',
    stage: '已归档',
    creator: 'user3',
    feedbackPerson: 'user3',
    handler: 'admin',
    devProcessor: 'admin',
    createdAt: '2024-01-04 07:00:00',
    enterTime: '2024-01-04 07:00:00',
    responseTime: '2024-01-04 08:20:00',
    endTime: '2024-01-04 16:45:00',
    result: '问题已彻底解决，功能正常运行',
  },
  {
    id: 'test-5',
    ticketID: 'TK005',
    title: '测试工单 - 已驳回',
    text: '这是一个已驳回的测试工单',
    status: '已驳回',
    stage: '已驳回',
    creator: 'user4',
    feedbackPerson: 'user4',
    handler: 'admin',
    devProcessor: 'admin',
    createdAt: '2024-01-05 11:00:00',
    enterTime: '2024-01-05 11:00:00',
    responseTime: '2024-01-05 12:00:00',
    reason: '信息不完整，需要补充详细描述',
  },
]

const selectedTicket = ref<Ticket>(mockTickets[0])
const orientation = ref<'horizontal' | 'vertical'>('horizontal')

const {
  getWorkflowSteps,
  getCurrentStepIndex,
  getProgressPercentage,
  getStatusDisplay,
  getCurrentStepActions,
  isActionAllowedInCurrentStep,
} = useTicketWorkflow()

// 获取当前工单的流程信息
const workflowSteps = computed(() => getWorkflowSteps(selectedTicket.value))
const currentStepIndex = computed(() => getCurrentStepIndex(selectedTicket.value))
const progressPercentage = computed(() => getProgressPercentage(selectedTicket.value))
const statusDisplay = computed(() => getStatusDisplay(selectedTicket.value))
const currentStepActions = computed(() => getCurrentStepActions(selectedTicket.value))

// 使用修复后的权限检查系统
const {
  checkPermission,
  checkTicketStatus,
  getAvailableActions,
  canExecuteAction,
  getUserRoles,
  getCurrentUsername,
} = useTicketOperations()

// 检查操作权限
const actionPermissions = computed(() => {
  if (!selectedTicket.value)
    return []

  const actions = ['handle', 'assign', 'complete', 'check', 'reject', 'urge', 'delete']
  return actions.map((action) => {
    const hasPermission = checkPermission(action, selectedTicket.value)
    const hasValidStatus = checkTicketStatus(action, selectedTicket.value)
    const { allowed, reason } = canExecuteAction(action, selectedTicket.value)

    return {
      action,
      hasPermission,
      hasValidStatus,
      allowed,
      reason,
    }
  })
})

// 获取可用操作列表
const availableActions = computed(() => {
  return selectedTicket.value ? getAvailableActions(selectedTicket.value) : []
})

// 用户信息
const userInfo = computed(() => ({
  roles: getUserRoles(),
  username: getCurrentUsername(),
}))
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mx-auto max-w-6xl">
      <h1 class="mb-6 text-2xl font-bold">
        工单流程测试
      </h1>

      <!-- 工单选择 -->
      <div class="mb-6">
        <h2 class="mb-3 text-lg font-semibold">
          选择测试工单
        </h2>
        <div class="grid gap-3 lg:grid-cols-3 md:grid-cols-2">
          <Card
            v-for="ticket in mockTickets"
            :key="ticket.id"
            class="cursor-pointer transition-colors"
            :class="selectedTicket.id === ticket.id ? 'ring-2 ring-primary' : ''"
            @click="selectedTicket = ticket"
          >
            <CardContent class="p-4">
              <div class="flex items-center justify-between">
                <div>
                  <h3 class="font-medium">
                    {{ ticket.ticketID }}
                  </h3>
                  <p class="text-sm text-muted-foreground">
                    {{ ticket.title }}
                  </p>
                </div>
                <Badge
                  :variant="ticket.status === '待处理' ? 'destructive'
                    : ticket.status === '处理中' ? 'default'
                      : ticket.status === '已处理' ? 'secondary'
                        : ticket.status === '已归档' ? 'outline'
                          : 'destructive'"
                >
                  {{ ticket.status }}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- 布局选择 -->
      <div class="mb-6">
        <h2 class="mb-3 text-lg font-semibold">
          布局方向
        </h2>
        <div class="flex gap-2">
          <Button
            :variant="orientation === 'horizontal' ? 'default' : 'outline'"
            @click="orientation = 'horizontal'"
          >
            水平布局
          </Button>
          <Button
            :variant="orientation === 'vertical' ? 'default' : 'outline'"
            @click="orientation = 'vertical'"
          >
            垂直布局
          </Button>
        </div>
      </div>

      <!-- 工单流程步骤展示 -->
      <div class="mb-8">
        <h2 class="mb-4 text-lg font-semibold">
          工单流程步骤
        </h2>
        <Card>
          <CardContent class="p-6">
            <TicketWorkflowSteps
              :ticket="selectedTicket"
              :orientation="orientation"
              :show-progress="true"
              :show-timestamps="true"
              :show-operators="true"
            />
          </CardContent>
        </Card>
      </div>

      <!-- 流程信息详情 -->
      <div class="grid gap-6 md:grid-cols-2">
        <!-- 当前状态信息 -->
        <Card>
          <CardHeader>
            <CardTitle>当前状态信息</CardTitle>
          </CardHeader>
          <CardContent class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-muted-foreground">当前状态:</span>
              <Badge>{{ statusDisplay.status }}</Badge>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-muted-foreground">当前步骤:</span>
              <span class="text-sm font-medium">{{ statusDisplay.currentStep }} / {{ statusDisplay.totalSteps }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-muted-foreground">完成进度:</span>
              <span class="text-sm font-medium">{{ statusDisplay.percentage }}%</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-muted-foreground">是否完成:</span>
              <Badge :variant="statusDisplay.isCompleted ? 'default' : 'secondary'">
                {{ statusDisplay.isCompleted ? '是' : '否' }}
              </Badge>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-muted-foreground">是否驳回:</span>
              <Badge :variant="statusDisplay.isRejected ? 'destructive' : 'secondary'">
                {{ statusDisplay.isRejected ? '是' : '否' }}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <!-- 可执行操作 -->
        <Card>
          <CardHeader>
            <CardTitle>可执行操作</CardTitle>
            <div class="text-sm text-muted-foreground">
              用户: {{ userInfo.username || '未知' }} | 角色: {{ userInfo.roles.join(', ') || '无' }}
            </div>
          </CardHeader>
          <CardContent>
            <div class="space-y-3">
              <div class="mb-3 text-sm text-muted-foreground">
                当前步骤允许的操作: {{ currentStepActions.join(', ') || '无' }}
              </div>

              <!-- 详细权限检查 -->
              <div class="grid gap-2">
                <div
                  v-for="permission in actionPermissions"
                  :key="permission.action"
                  class="border rounded p-3"
                  :class="permission.allowed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'"
                >
                  <div class="mb-2 flex items-center justify-between">
                    <span class="text-sm font-medium">{{ permission.action }}</span>
                    <Badge :variant="permission.allowed ? 'default' : 'destructive'">
                      {{ permission.allowed ? '允许' : '禁止' }}
                    </Badge>
                  </div>
                  <div class="text-xs text-muted-foreground space-y-1">
                    <div class="flex justify-between">
                      <span>权限检查:</span>
                      <Badge :variant="permission.hasPermission ? 'default' : 'secondary'" class="text-xs">
                        {{ permission.hasPermission ? '通过' : '失败' }}
                      </Badge>
                    </div>
                    <div class="flex justify-between">
                      <span>状态检查:</span>
                      <Badge :variant="permission.hasValidStatus ? 'default' : 'secondary'" class="text-xs">
                        {{ permission.hasValidStatus ? '通过' : '失败' }}
                      </Badge>
                    </div>
                    <div class="mt-1 text-xs text-muted-foreground">
                      {{ permission.reason }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 可用操作按钮 -->
              <div class="mt-4">
                <h4 class="mb-2 text-sm font-medium">
                  可用操作按钮:
                </h4>
                <div class="flex flex-wrap gap-2">
                  <Button
                    v-for="action in availableActions"
                    :key="action.action"
                    :variant="action.variant"
                    :disabled="action.disabled"
                    size="sm"
                  >
                    {{ action.label }}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 步骤详情 -->
      <div class="mt-8">
        <h2 class="mb-4 text-lg font-semibold">
          步骤详情
        </h2>
        <div class="grid gap-4 lg:grid-cols-3 md:grid-cols-2">
          <Card
            v-for="(step, index) in workflowSteps"
            :key="step.id"
            :class="{
              'ring-2 ring-blue-500': step.status === 'active',
              'bg-green-50': step.status === 'completed',
              'bg-red-50': step.status === 'error',
            }"
          >
            <CardContent class="p-4">
              <div class="mb-2 flex items-center justify-between">
                <h3 class="font-medium">
                  {{ step.title }}
                </h3>
                <Badge
                  :variant="step.status === 'completed' ? 'default'
                    : step.status === 'active' ? 'secondary'
                      : step.status === 'error' ? 'destructive' : 'outline'"
                >
                  {{ step.status === 'completed' ? '已完成'
                    : step.status === 'active' ? '进行中'
                      : step.status === 'error' ? '已驳回' : '待处理' }}
                </Badge>
              </div>
              <p class="mb-3 text-sm text-muted-foreground">
                {{ step.description }}
              </p>
              <div class="text-xs text-muted-foreground space-y-1">
                <div v-if="step.timestamp">
                  <strong>时间:</strong> {{ step.timestamp }}
                </div>
                <div v-if="step.operator">
                  <strong>操作人:</strong> {{ step.operator }}
                </div>
                <div v-if="step.actions && step.actions.length > 0">
                  <strong>可执行操作:</strong> {{ step.actions.join(', ') }}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>
