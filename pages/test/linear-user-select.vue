<script setup lang="ts">
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { UserSelect } from '@/components/ui/user-select'

// 页面标题
useHead({
  title: 'Linear User Select - Test Page',
})

// 响应式数据
const singleUser = ref('')
const multipleUsers = ref<string[]>([])
const disabledUser = ref('')

// 事件处理
function handleSingleChange(value: string | string[], users: any) {
  console.log('Single user changed:', { value, users })
}

function handleMultipleChange(value: string | string[], users: any) {
  console.log('Multiple users changed:', { value, users })
}

function handleInviteUser(searchValue: string) {
  console.log('Invite user:', searchValue)
  // 这里可以实现邀请用户的逻辑
}

function handleNoAssignee() {
  console.log('No assignee selected')
}

function clearSelections() {
  singleUser.value = ''
  multipleUsers.value = []
}

// 调试函数
async function testAPI() {
  try {
    const { getUserList } = await import('~/api/feedback/ticket')
    const response = await getUserList({ keyword: '' })
    console.warn('Direct API test:', response)
  }
  catch (error) {
    console.error('API test failed:', error)
  }
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">
        Linear User Select Component
      </h1>
      <p class="text-muted-foreground">
        参考Linear设计的用户选择组件，具有现代化的界面和交互体验
      </p>
    </div>

    <div class="grid gap-6">
      <!-- 基本用法 -->
      <Card>
        <CardHeader>
          <CardTitle>基本用法</CardTitle>
          <CardDescription>
            单选模式，带有"无指派"选项和用户头像显示
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <label class="text-sm font-medium">Assignee</label>
            <UserSelect
              v-model="singleUser"
              placeholder="No assignee"
              :show-no-assignee="true"
              :show-invite-user="true"
              :virtual-threshold="20"
              :item-height="56"
              :list-height="280"
              @change="handleSingleChange"
              @invite-user="handleInviteUser"
              @no-assignee="handleNoAssignee"
            />
          </div>

          <div class="text-sm text-muted-foreground">
            Selected value: <Badge variant="outline">
              {{ singleUser || 'None' }}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <!-- 多选模式 -->
      <Card>
        <CardHeader>
          <CardTitle>多选模式</CardTitle>
          <CardDescription>
            支持选择多个用户，显示头像堆叠效果
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <label class="text-sm font-medium">Assignees</label>
            <UserSelect
              v-model="multipleUsers"
              multiple
              placeholder="No assignees"
              :show-no-assignee="false"
              :show-invite-user="true"
              :virtual-threshold="20"
              :item-height="56"
              :list-height="280"
              @change="handleMultipleChange"
              @invite-user="handleInviteUser"
            />
          </div>

          <div class="text-sm text-muted-foreground">
            Selected count: <Badge variant="outline">
              {{ multipleUsers.length }}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <!-- 不同尺寸 -->
      <Card>
        <CardHeader>
          <CardTitle>不同尺寸</CardTitle>
          <CardDescription>
            支持小、中、大三种尺寸
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
          <div class="space-y-2">
            <label class="text-sm font-medium">Small Size</label>
            <UserSelect
              size="sm"
              placeholder="Small assignee"
              :show-no-assignee="true"
            />
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium">Medium Size (Default)</label>
            <UserSelect
              size="md"
              placeholder="Medium assignee"
              :show-no-assignee="true"
            />
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium">Large Size</label>
            <UserSelect
              size="lg"
              placeholder="Large assignee"
              :show-no-assignee="true"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 禁用状态 -->
      <Card>
        <CardHeader>
          <CardTitle>禁用状态</CardTitle>
          <CardDescription>
            组件的禁用状态展示
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <label class="text-sm font-medium">Disabled Assignee</label>
            <UserSelect
              v-model="disabledUser"
              disabled
              placeholder="Disabled assignee"
              :show-no-assignee="true"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 性能优化测试 -->
      <Card>
        <CardHeader>
          <CardTitle>性能优化测试</CardTitle>
          <CardDescription>
            测试弹窗触发后才加载数据的性能表现
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <label class="text-sm font-medium">延迟加载测试</label>
            <UserSelect
              placeholder="点击后才加载用户数据"
              :show-no-assignee="true"
            />
            <p class="text-xs text-muted-foreground">
              只有在点击弹窗时才会请求用户数据，避免初始化时的性能问题
            </p>
          </div>
        </CardContent>
      </Card>

      <!-- 自定义样式 -->
      <Card>
        <CardHeader>
          <CardTitle>自定义样式</CardTitle>
          <CardDescription>
            可以通过class属性自定义样式
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <label class="text-sm font-medium">Custom Styled</label>
            <UserSelect
              placeholder="Custom assignee"
              class="border border-primary/50 border-dashed hover:border-primary"
              :show-no-assignee="true"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 操作按钮 -->
      <Card>
        <CardHeader>
          <CardTitle>操作</CardTitle>
          <CardDescription>
            测试组件的各种操作
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="flex gap-2">
            <Button @click="clearSelections">
              清除所有选择
            </Button>
            <Button variant="outline" @click="testAPI">
              测试API
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <Separator />

    <!-- 特性说明 -->
    <div class="space-y-4">
      <h2 class="text-2xl font-semibold">
        组件特性
      </h2>
      <div class="grid gap-4 md:grid-cols-2">
        <div class="space-y-2">
          <h3 class="text-lg font-medium">
            设计特点
          </h3>
          <ul class="text-sm text-muted-foreground space-y-1">
            <li>• 参考Linear的现代化设计风格</li>
            <li>• 用户头像显示和堆叠效果</li>
            <li>• 简洁的无边框按钮样式</li>
            <li>• 清晰的分组和状态显示</li>
          </ul>
        </div>

        <div class="space-y-2">
          <h3 class="text-lg font-medium">
            功能特性
          </h3>
          <ul class="text-sm text-muted-foreground space-y-1">
            <li>• 支持单选和多选模式</li>
            <li>• "无指派"和"邀请新用户"选项</li>
            <li>• 实时搜索和防抖优化</li>
            <li>• 延迟加载，点击后才请求数据</li>
            <li>• ScrollArea滚动，性能稳定</li>
            <li>• 多种尺寸和自定义样式</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 可以在这里添加自定义样式 */
</style>
