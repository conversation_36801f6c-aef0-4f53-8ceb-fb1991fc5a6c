<script setup lang="ts">
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import { AttachmentUploader } from '@/components/ui/attachment-uploader'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { ref } from 'vue'

// 页面标题
definePageMeta({
  title: '附件上传组件测试',
})

// 附件列表
const attachments = ref<AttachmentItem[]>([])

// 处理上传成功
function handleUploadSuccess(attachment: AttachmentItem) {
  console.log('✅ 上传成功:', attachment)
}

// 处理上传失败
function handleUploadError(error: string) {
  console.error('❌ 上传失败:', error)
}

// 清空附件
function clearAttachments() {
  attachments.value = []
}

// 模拟添加附件
function addMockAttachment() {
  const mockAttachment: AttachmentItem = {
    id: `mock-${Date.now()}`,
    name: `模拟文件-${Date.now()}.jpg`,
    url: 'https://picsum.photos/800/600',
    type: 'jpg',
    size: 1024 * 1024, // 1MB
  }
  attachments.value.push(mockAttachment)
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold">
        附件上传组件测试
      </h1>
      <div class="flex gap-2">
        <Button variant="outline" @click="addMockAttachment">
          添加模拟附件
        </Button>
        <Button variant="outline" @click="clearAttachments">
          清空附件
        </Button>
      </div>
    </div>

    <!-- 功能说明 -->
    <Card>
      <CardHeader>
        <CardTitle>功能特性</CardTitle>
      </CardHeader>
      <CardContent class="space-y-2">
        <div class="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
          <div>
            <h4 class="mb-2 font-medium">
              上传功能
            </h4>
            <ul class="text-muted-foreground space-y-1">
              <li>• 支持拖拽上传</li>
              <li>• 支持多文件选择</li>
              <li>• 实时上传进度显示</li>
              <li>• 上传失败重试机制</li>
              <li>• 文件大小和类型验证</li>
            </ul>
          </div>
          <div>
            <h4 class="mb-2 font-medium">
              支持格式
            </h4>
            <ul class="text-muted-foreground space-y-1">
              <li>• 图片: PNG, JPEG, JPG</li>
              <li>• 视频: MP4, RMVB, MOV</li>
              <li>• 最大文件大小: 100MB</li>
              <li>• 最大文件数量: 10个</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 上传组件测试 -->
    <Card>
      <CardHeader>
        <CardTitle>附件上传器</CardTitle>
        <CardDescription>
          测试文件上传功能，支持拖拽和点击上传
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentUploader
          v-model="attachments"
          :max-files="10"
          :max-size="100"
          :accept="['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov']"
          :multiple="true"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
        />
      </CardContent>
    </Card>

    <!-- 附件查看器测试 -->
    <Card v-if="attachments.length > 0">
      <CardHeader>
        <CardTitle>附件查看器</CardTitle>
        <CardDescription>
          查看已上传的附件，支持预览和下载
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          :attachments="attachments"
          :max-thumbnails="6"
          thumbnail-size="md"
          :show-file-name="true"
          :allow-download="true"
        />
      </CardContent>
    </Card>

    <!-- 当前附件状态 -->
    <Card>
      <CardHeader>
        <CardTitle>当前状态</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">附件数量:</span>
            <Badge variant="secondary">
              {{ attachments.length }}/10
            </Badge>
          </div>

          <div v-if="attachments.length > 0" class="space-y-2">
            <div class="text-sm font-medium">
              附件列表:
            </div>
            <div class="space-y-1">
              <div
                v-for="(attachment, index) in attachments"
                :key="attachment.id"
                class="flex items-center justify-between rounded bg-muted/50 p-2 text-xs"
              >
                <span class="truncate">{{ attachment.name }}</span>
                <div class="flex items-center gap-2">
                  <Badge variant="outline" class="text-xs">
                    {{ attachment.type.toUpperCase() }}
                  </Badge>
                  <span class="text-muted-foreground">
                    {{ attachment.size ? `${Math.round(attachment.size / 1024)}KB` : '未知' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="py-4 text-center text-sm text-muted-foreground">
            暂无附件
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- API 说明 -->
    <Card>
      <CardHeader>
        <CardTitle>API 接口说明</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <h4 class="font-medium">
            上传接口
          </h4>
          <div class="rounded bg-muted p-3 text-sm font-mono">
            POST /api/feedback/upload/
          </div>
          <div class="text-sm text-muted-foreground">
            请求参数: file (File), uid (string)
          </div>
        </div>

        <div class="space-y-2">
          <h4 class="font-medium">
            响应格式
          </h4>
          <pre class="overflow-x-auto rounded bg-muted p-3 text-xs"><code>{
  "code": 20000,
  "msg": "上传成功",
  "data": {
    "uid": "1234567890_abcdefghij",
    "url": "https://example.com/uploads/file.jpg",
    "name": "file.jpg",
    "type": "jpg",
    "size": 1048576
  },
  "status": "ok"
}</code></pre>
        </div>
      </CardContent>
    </Card>

    <!-- 使用说明 -->
    <Alert>
      <AlertDescription>
        <strong>使用说明：</strong>
        <br>
        1. 点击上传区域或拖拽文件到上传区域
        <br>
        2. 支持的文件格式：PNG, JPEG, JPG, MP4, RMVB, MOV
        <br>
        3. 单个文件最大 100MB，最多上传 10 个文件
        <br>
        4. 上传成功后可以在附件查看器中预览和下载
        <br>
        5. 查看浏览器控制台可以看到详细的上传日志
      </AlertDescription>
    </Alert>
  </div>
</template>
