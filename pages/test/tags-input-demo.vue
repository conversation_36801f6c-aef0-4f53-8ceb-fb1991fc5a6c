<script setup lang="ts">
import { ref } from 'vue'

// 模拟工单表单数据
const formData = ref({
  appVersion: ['1.0.0', '1.0.1'],
  mobileType: ['iPhone 15 Pro', 'Samsung Galaxy S24'],
  userTtid: ['123456789', '987654321'],
})

// 预设选项
const commonVersions = [
  '1.0.0', '1.0.1', '1.0.2', '1.1.0', '1.2.0', '2.0.0',
  '2.1.0', '2.2.0', '3.0.0', '3.1.0'
]

const commonDevices = [
  'iPhone 15 Pro', 'iPhone 15', 'iPhone 14 Pro', 'iPhone 14',
  'Samsung Galaxy S24', 'Samsung Galaxy S23', 'Xiaomi 14',
  'Huawei Mate 60', 'OPPO Find X7', 'Vivo X100'
]

// 处理变化
function handleVersionChange(versions: string[]) {
  formData.value.appVersion = versions
  console.log('产品版本更新:', versions)
}

function handleDeviceChange(devices: string[]) {
  formData.value.mobileType = devices
  console.log('设备型号更新:', devices)
}

function handleTtidChange(ttids: string[]) {
  formData.value.userTtid = ttids
  console.log('TT ID更新:', ttids)
}

// 快速添加预设值
function addCommonVersion(version: string) {
  if (!formData.value.appVersion.includes(version)) {
    formData.value.appVersion.push(version)
  }
}

function addCommonDevice(device: string) {
  if (!formData.value.mobileType.includes(device)) {
    formData.value.mobileType.push(device)
  }
}

// 清空所有
function clearAll() {
  formData.value.appVersion = []
  formData.value.mobileType = []
  formData.value.userTtid = []
}

// 设置示例数据
function setExampleData() {
  formData.value.appVersion = ['1.0.0', '1.0.1', '1.1.0']
  formData.value.mobileType = ['iPhone 15 Pro', 'Samsung Galaxy S24']
  formData.value.userTtid = ['123456789', '987654321', '555666777']
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        Tags Input 组件演示
      </h1>
      <p class="text-muted-foreground">
        演示如何使用 shadcn-ui Tags Input 组件处理产品版本、设备型号、TT ID 等多值字段
      </p>
    </div>

    <!-- 基础使用演示 -->
    <Card>
      <CardHeader>
        <CardTitle>工单表单字段演示</CardTitle>
        <CardDescription>
          使用 Tags Input 组件替代原有的单行输入框，支持多个值的输入和管理
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- 产品版本 -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <Label class="text-sm font-medium">产品版本</Label>
            <div class="flex gap-2">
              <Button
                v-for="version in commonVersions.slice(0, 3)"
                :key="version"
                variant="outline"
                size="sm"
                class="h-6 px-2 text-xs"
                @click="addCommonVersion(version)"
              >
                + {{ version }}
              </Button>
            </div>
          </div>
          <TagsInput
            :model-value="formData.appVersion"
            @update:model-value="handleVersionChange"
          >
            <TagsInputItem
              v-for="version in formData.appVersion"
              :key="version"
              :value="version"
            >
              <TagsInputItemText />
              <TagsInputItemDelete />
            </TagsInputItem>
            <TagsInputInput placeholder="输入版本号，如 1.0.0" />
          </TagsInput>
          <p class="text-xs text-muted-foreground">
            输入版本号后按回车添加，支持语义化版本号格式
          </p>
        </div>

        <!-- 设备型号 -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <Label class="text-sm font-medium">设备型号</Label>
            <div class="flex gap-2">
              <Button
                v-for="device in commonDevices.slice(0, 3)"
                :key="device"
                variant="outline"
                size="sm"
                class="h-6 px-2 text-xs"
                @click="addCommonDevice(device)"
              >
                + {{ device }}
              </Button>
            </div>
          </div>
          <TagsInput
            :model-value="formData.mobileType"
            @update:model-value="handleDeviceChange"
          >
            <TagsInputItem
              v-for="device in formData.mobileType"
              :key="device"
              :value="device"
            >
              <TagsInputItemText />
              <TagsInputItemDelete />
            </TagsInputItem>
            <TagsInputInput placeholder="输入设备型号，如 iPhone 15 Pro" />
          </TagsInput>
          <p class="text-xs text-muted-foreground">
            输入设备型号后按回车添加，支持各种品牌和型号
          </p>
        </div>

        <!-- TT ID -->
        <div class="space-y-3">
          <Label class="text-sm font-medium">TT ID</Label>
          <TagsInput
            :model-value="formData.userTtid"
            @update:model-value="handleTtidChange"
          >
            <TagsInputItem
              v-for="ttid in formData.userTtid"
              :key="ttid"
              :value="ttid"
            >
              <TagsInputItemText />
              <TagsInputItemDelete />
            </TagsInputItem>
            <TagsInputInput placeholder="输入 TT ID，如 123456789" />
          </TagsInput>
          <p class="text-xs text-muted-foreground">
            输入用户的 TT ID 后按回车添加，支持多个用户 ID
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2 pt-4 border-t">
          <Button @click="setExampleData">
            设置示例数据
          </Button>
          <Button variant="outline" @click="clearAll">
            清空所有
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 当前数据显示 -->
    <Card>
      <CardHeader>
        <CardTitle>当前表单数据</CardTitle>
        <CardDescription>
          实时显示 Tags Input 组件的数据变化
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="grid gap-4 text-sm">
            <div class="space-y-2">
              <Label class="font-medium">产品版本 ({{ formData.appVersion.length }} 个)</Label>
              <div class="rounded-md bg-muted p-3 font-mono text-xs">
                {{ formData.appVersion.length > 0 ? JSON.stringify(formData.appVersion, null, 2) : '[]' }}
              </div>
            </div>

            <div class="space-y-2">
              <Label class="font-medium">设备型号 ({{ formData.mobileType.length }} 个)</Label>
              <div class="rounded-md bg-muted p-3 font-mono text-xs">
                {{ formData.mobileType.length > 0 ? JSON.stringify(formData.mobileType, null, 2) : '[]' }}
              </div>
            </div>

            <div class="space-y-2">
              <Label class="font-medium">TT ID ({{ formData.userTtid.length }} 个)</Label>
              <div class="rounded-md bg-muted p-3 font-mono text-xs">
                {{ formData.userTtid.length > 0 ? JSON.stringify(formData.userTtid, null, 2) : '[]' }}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 功能特性说明 -->
    <Card>
      <CardHeader>
        <CardTitle>Tags Input 组件优势</CardTitle>
        <CardDescription>
          相比原有的单行输入框，Tags Input 组件提供了更好的用户体验
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-6 md:grid-cols-2">
          <div class="space-y-4">
            <h4 class="font-semibold text-sm">用户体验优势</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>可视化的标签展示，一目了然</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>单独删除每个标签，操作精确</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>支持键盘导航和快捷键</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>自动换行，适应不同屏幕</span>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <h4 class="font-semibold text-sm">功能特性</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>支持复制粘贴多个值</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>防重复添加相同值</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>支持预设值快速添加</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>完全兼容表单验证</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
