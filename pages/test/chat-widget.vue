<script setup lang="ts">
import { MessageCircle } from 'lucide-vue-next'

// 页面元数据
definePageMeta({
  title: '聊天组件测试',
  middleware: 'auth',
})

// 聊天组件状态
const showChatWidget = ref(false)
const chatMinimized = ref(false)

function openChat() {
  showChatWidget.value = true
  chatMinimized.value = false
}

function closeChat() {
  showChatWidget.value = false
}

function minimizeChat() {
  chatMinimized.value = true
}

function maximizeChat() {
  chatMinimized.value = false
}
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div>
        <h1 class="text-3xl font-bold">聊天组件测试页面</h1>
        <p class="text-muted-foreground mt-2">
          这个页面演示了如何在任何页面中集成聊天组件
        </p>
      </div>
      
      <!-- 功能介绍 -->
      <Card>
        <CardHeader>
          <CardTitle>聊天组件功能</CardTitle>
          <CardDescription>
            一个可嵌入任何页面的聊天组件，支持最小化、关闭等操作
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <h3 class="font-semibold">主要特性</h3>
              <ul class="text-sm text-muted-foreground space-y-1">
                <li>• 固定在页面右下角</li>
                <li>• 支持最小化/最大化</li>
                <li>• 实时消息发送和接收</li>
                <li>• 模拟自动回复</li>
                <li>• 响应式设计</li>
              </ul>
            </div>
            <div class="space-y-2">
              <h3 class="font-semibold">使用场景</h3>
              <ul class="text-sm text-muted-foreground space-y-1">
                <li>• 客服支持</li>
                <li>• 在线咨询</li>
                <li>• 实时帮助</li>
                <li>• 用户反馈</li>
                <li>• 技术支持</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <!-- 操作按钮 -->
      <Card>
        <CardHeader>
          <CardTitle>操作演示</CardTitle>
          <CardDescription>
            点击下面的按钮来测试聊天组件的各种功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="flex flex-wrap gap-4">
            <Button @click="openChat" :disabled="showChatWidget">
              <MessageCircle class="h-4 w-4 mr-2" />
              打开聊天
            </Button>
            <Button variant="outline" @click="minimizeChat" :disabled="!showChatWidget || chatMinimized">
              最小化聊天
            </Button>
            <Button variant="outline" @click="maximizeChat" :disabled="!showChatWidget || !chatMinimized">
              最大化聊天
            </Button>
            <Button variant="destructive" @click="closeChat" :disabled="!showChatWidget">
              关闭聊天
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <!-- 代码示例 -->
      <Card>
        <CardHeader>
          <CardTitle>代码示例</CardTitle>
          <CardDescription>
            如何在你的页面中使用聊天组件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div>
              <h4 class="font-semibold mb-2">1. 在模板中使用</h4>
              <pre class="bg-muted p-4 rounded-lg text-sm overflow-x-auto"><code>&lt;ChatWidget
  v-if="showChatWidget"
  :user-id="1"
  :user-name="'客服'"
  :user-avatar="'/avatars/01.png'"
  :minimized="chatMinimized"
  @close="closeChat"
  @minimize="minimizeChat"
  @maximize="maximizeChat"
/&gt;</code></pre>
            </div>
            
            <div>
              <h4 class="font-semibold mb-2">2. 在脚本中控制</h4>
              <pre class="bg-muted p-4 rounded-lg text-sm overflow-x-auto"><code>const showChatWidget = ref(false)
const chatMinimized = ref(false)

function openChat() {
  showChatWidget.value = true
  chatMinimized.value = false
}

function closeChat() {
  showChatWidget.value = false
}</code></pre>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <!-- 其他内容 -->
      <Card>
        <CardHeader>
          <CardTitle>页面内容示例</CardTitle>
          <CardDescription>
            这里是页面的其他内容，聊天组件不会影响页面布局
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <p class="text-muted-foreground">
              聊天组件使用固定定位，不会影响页面的正常布局和内容。
              用户可以在浏览页面内容的同时进行聊天对话。
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent class="p-4">
                  <h4 class="font-semibold mb-2">功能模块 1</h4>
                  <p class="text-sm text-muted-foreground">
                    这里是一些示例内容，展示页面的正常功能不受聊天组件影响。
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent class="p-4">
                  <h4 class="font-semibold mb-2">功能模块 2</h4>
                  <p class="text-sm text-muted-foreground">
                    聊天组件可以与页面的其他功能完美配合使用。
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent class="p-4">
                  <h4 class="font-semibold mb-2">功能模块 3</h4>
                  <p class="text-sm text-muted-foreground">
                    用户体验流畅，不会有任何冲突或干扰。
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
    
    <!-- 聊天组件 -->
    <ChatWidget
      v-if="showChatWidget"
      :user-id="1"
      :user-name="'客服'"
      :user-avatar="'/avatars/01.png'"
      :minimized="chatMinimized"
      @close="closeChat"
      @minimize="minimizeChat"
      @maximize="maximizeChat"
    />
  </div>
</template>
