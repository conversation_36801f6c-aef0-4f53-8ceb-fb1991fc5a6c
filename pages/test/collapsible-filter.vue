<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import TicketFilterToolbar from '~/components/feedback/filter/TicketFilterToolbar.vue'

// 模拟工单数据，包含多种筛选条件
const mockTickets: Ticket[] = [
  {
    id: '1',
    ticketID: 'T001',
    title: '登录问题',
    description: '用户无法登录系统',
    status: '待处理',
    priority: '高',
    category: '技术问题',
    assignee: '张三',
    reporter: '李四',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01',
    tags: ['登录', '紧急'],
    department: '技术部',
    product: '主系统',
  },
  {
    id: '2',
    ticketID: 'T002',
    title: '功能请求',
    description: '希望添加新功能',
    status: '处理中',
    priority: '中',
    category: '功能请求',
    assignee: '王五',
    reporter: '赵六',
    createdAt: '2024-01-02',
    updatedAt: '2024-01-02',
    tags: ['功能', '优化'],
    department: '产品部',
    product: '移动端',
  },
  {
    id: '3',
    ticketID: 'T003',
    title: '性能问题',
    description: '系统响应缓慢',
    status: '已完成',
    priority: '低',
    category: '性能问题',
    assignee: '孙七',
    reporter: '周八',
    createdAt: '2024-01-03',
    updatedAt: '2024-01-03',
    tags: ['性能', '优化', '缓慢'],
    department: '技术部',
    product: '主系统',
  },
  {
    id: '4',
    ticketID: 'T004',
    title: 'UI问题',
    description: '界面显示异常',
    status: '待处理',
    priority: '高',
    category: 'UI问题',
    assignee: '吴九',
    reporter: '郑十',
    createdAt: '2024-01-04',
    updatedAt: '2024-01-04',
    tags: ['UI', '显示', '异常', '界面'],
    department: '设计部',
    product: '网页端',
  },
  {
    id: '5',
    ticketID: 'T005',
    title: '数据问题',
    description: '数据同步失败',
    status: '处理中',
    priority: '中',
    category: '数据问题',
    assignee: '钱一',
    reporter: '孙二',
    createdAt: '2024-01-05',
    updatedAt: '2024-01-05',
    tags: ['数据', '同步', '失败', '错误', '修复'],
    department: '技术部',
    product: '数据平台',
  },
]

// 筛选条件状态
const activeFilters = ref<Record<string, string[]>>({})

// 处理筛选事件
function handleFilter(filters: Record<string, string[]>) {
  activeFilters.value = filters
  console.log('筛选条件更新:', filters)
}

// 设置一些初始筛选条件来测试展开/收起功能
onMounted(() => {
  // 设置筛选条件来测试展开/收起功能
  setTimeout(() => {
    activeFilters.value = {
      status: ['待处理'],
      priority: ['高'],
    }
  }, 1000) // 延迟1秒设置，让组件完全加载
})

// 添加更多筛选条件的函数
function addMoreFilters() {
  activeFilters.value = {
    status: ['待处理', '处理中'],
    priority: ['高', '中'],
    category: ['技术问题', '功能请求'],
    assignee: ['张三', '王五'],
    department: ['技术部'],
    product: ['主系统', '移动端'],
    tags: ['登录', '功能', '性能'],
  }
}

// 清除所有筛选条件
function clearAllFilters() {
  activeFilters.value = {}
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-6">
      <div>
        <h1 class="text-2xl font-bold">
          筛选器展开/收起功能测试
        </h1>
        <p class="mt-2 text-muted-foreground">
          测试筛选标签的展开和收起功能。当筛选条件超过3个时，会显示展开/收起按钮。
        </p>
      </div>

      <!-- 筛选工具栏 -->
      <Card>
        <CardHeader>
          <CardTitle>筛选工具栏</CardTitle>
          <CardDescription>
            下面的筛选工具栏包含多个筛选条件，可以测试展开/收起功能
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TicketFilterToolbar
            :tickets="mockTickets"
            title="测试工单列表"
            :initial-filters="activeFilters"
            @filter="handleFilter"
          />
        </CardContent>
      </Card>

      <!-- 当前筛选条件显示 -->
      <Card>
        <CardHeader>
          <CardTitle>当前筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <pre class="overflow-auto rounded-md bg-muted p-4 text-sm">{{ JSON.stringify(activeFilters, null, 2) }}</pre>
        </CardContent>
      </Card>

      <!-- 测试控制按钮 -->
      <Card>
        <CardHeader>
          <CardTitle>测试控制</CardTitle>
          <CardDescription>
            使用下面的按钮来测试不同的筛选状态
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="flex gap-2">
            <Button variant="outline" @click="addMoreFilters">
              添加多个筛选条件
            </Button>
            <Button variant="outline" @click="clearAllFilters">
              清除所有筛选条件
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 使用说明 -->
      <Card>
        <CardHeader>
          <CardTitle>功能说明</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div>
            <h4 class="mb-2 font-medium">
              展开/收起功能
            </h4>
            <ul class="list-disc list-inside text-sm text-muted-foreground space-y-1">
              <li>只要有筛选条件，就会显示展开/收起按钮</li>
              <li>默认状态为展开，显示所有筛选标签</li>
              <li>点击"收起"按钮，完全隐藏所有筛选标签</li>
              <li>收起状态下，筛选按钮会显示筛选条件的总数量</li>
              <li>点击"展开"按钮，重新显示所有筛选标签</li>
            </ul>
          </div>
          <div>
            <h4 class="mb-2 font-medium">
              测试步骤
            </h4>
            <ol class="list-decimal list-inside text-sm text-muted-foreground space-y-1">
              <li>页面加载时会自动设置少量筛选条件</li>
              <li>观察筛选标签区域是否显示展开/收起按钮</li>
              <li>点击"收起"按钮，检查是否完全隐藏所有筛选标签</li>
              <li>观察筛选按钮是否显示筛选条件数量的 badge</li>
              <li>点击"展开"按钮，检查是否重新显示所有标签</li>
              <li>使用测试按钮添加更多筛选条件或清除所有筛选条件</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
