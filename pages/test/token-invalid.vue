<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next'
import { requestClient } from '~/utils/request'

// 测试不同的token无效场景
const testScenarios = [
  {
    name: '模拟 2004 错误码 (Token无效)',
    action: async () => {
      // 模拟服务器返回 2004 错误码
      try {
        await requestClient.post('/test/token-invalid-2004', {}, {
          skipErrorHandler: false,
        })
      }
      catch (error) {
        console.log('捕获到错误:', error)
      }
    },
  },
  {
    name: '模拟 2002 错误码 (用户未登录)',
    action: async () => {
      try {
        await requestClient.post('/test/token-invalid-2002', {}, {
          skipErrorHandler: false,
        })
      }
      catch (error) {
        console.log('捕获到错误:', error)
      }
    },
  },
  {
    name: '模拟 401 HTTP状态码',
    action: async () => {
      try {
        await requestClient.post('/test/token-invalid-401', {}, {
          skipErrorHandler: false,
        })
      }
      catch (error) {
        console.log('捕获到错误:', error)
      }
    },
  },
  {
    name: '模拟真实API调用 (可能触发token无效)',
    action: async () => {
      try {
        // 先清除token，然后调用需要认证的API
        const authStore = useAuthStore()
        authStore.clearAuthData()

        await requestClient.get('/user/info', {
          skipErrorHandler: false,
        })
      }
      catch (error) {
        console.log('捕获到错误:', error)
      }
    },
  },
]

const isLoading = ref(false)

async function runTest(scenario: any) {
  isLoading.value = true
  try {
    console.log(`开始测试: ${scenario.name}`)
    await scenario.action()
  }
  catch (error) {
    console.error(`测试失败: ${scenario.name}`, error)
  }
  finally {
    isLoading.value = false
  }
}

// 手动触发认证过期弹窗
async function showAuthModal() {
  try {
    console.log('开始显示认证过期弹窗...')
    const modal = useModal()
    console.log('modal 对象:', modal)
    const result = await modal.showAuthExpiredModal()
    console.log('弹窗结果:', result)
  }
  catch (error) {
    console.error('显示弹窗失败:', error)
  }
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mx-auto max-w-4xl">
      <h1 class="mb-6 text-2xl font-bold">
        Token无效处理测试
      </h1>

      <div class="space-y-4">
        <div class="border border-yellow-200 rounded-lg bg-yellow-50 p-4">
          <h2 class="mb-2 text-lg text-yellow-800 font-semibold">
            测试说明
          </h2>
          <p class="text-yellow-700">
            这个页面用于测试当API返回token无效错误时，系统是否能正确显示认证过期弹窗并引导用户重新登录。
          </p>
        </div>

        <div class="grid gap-4">
          <div
            v-for="(scenario, index) in testScenarios"
            :key="index"
            class="border border-gray-200 rounded-lg p-4"
          >
            <h3 class="mb-2 font-medium">
              {{ scenario.name }}
            </h3>
            <Button
              :disabled="isLoading"
              class="w-full"
              @click="runTest(scenario)"
            >
              <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
              运行测试
            </Button>
          </div>
        </div>

        <div class="border border-blue-200 rounded-lg bg-blue-50 p-4">
          <h3 class="mb-2 text-blue-800 font-medium">
            手动测试
          </h3>
          <p class="mb-3 text-sm text-blue-700">
            直接触发认证过期弹窗，测试弹窗功能是否正常
          </p>
          <Button
            variant="outline"
            class="w-full"
            @click="showAuthModal"
          >
            显示认证过期弹窗
          </Button>
        </div>

        <div class="border border-gray-200 rounded-lg bg-gray-50 p-4">
          <h3 class="mb-2 font-medium">
            预期行为
          </h3>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• 当API返回2002、2003、2004错误码时，应该显示认证过期弹窗</li>
            <li>• 当API返回401 HTTP状态码时，应该显示认证过期弹窗</li>
            <li>• 弹窗应该有"稍后处理"和"立即登录"两个选项</li>
            <li>• 点击"立即登录"应该跳转到登录页面</li>
            <li>• 弹窗应该是模态的，不能通过点击外部或ESC键关闭</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
