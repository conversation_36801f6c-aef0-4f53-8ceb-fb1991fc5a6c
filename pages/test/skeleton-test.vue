<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import TicketDetail from '~/components/feedback/TicketDetail.vue'
import TicketList from '~/components/feedback/TicketList.vue'

// 模拟loading状态
const isListLoading = ref(false)
const isDetailLoading = ref(false)

// 模拟工单数据
const mockTickets = [
  {
    id: '1',
    ticketID: 'T001',
    title: '测试工单1',
    text: '这是一个测试工单的描述',
    status: '待处理',
    stage: '待处理',
    feedbackPerson: '张三',
    enterTime: '2024-01-01 10:00:00',
    labels: ['功能问题', '紧急'],
  },
  {
    id: '2',
    ticketID: 'T002',
    title: '测试工单2',
    text: '这是另一个测试工单的描述',
    status: '处理中',
    stage: '处理中',
    feedbackPerson: '李四',
    enterTime: '2024-01-02 14:30:00',
    labels: ['界面问题'],
  },
]

const mockTicket = {
  id: '1',
  ticketID: 'T001',
  title: '测试工单详情',
  text: '这是一个测试工单的详细描述内容',
  problemDescription: '用户反馈登录功能异常',
  status: '待处理',
  stage: '待处理',
  feedbackPerson: '张三',
  handler: '王五',
  enterTime: '2024-01-01 10:00:00',
  labels: ['功能问题', '紧急'],
  changeLogs: [
    {
      id: '1',
      action: '创建',
      operator: '张三',
      time: '2024-01-01 10:00:00',
      description: '工单已创建',
    },
  ],
}

// 测试函数
function testListSkeleton() {
  isListLoading.value = true
  setTimeout(() => {
    isListLoading.value = false
  }, 3000)
}

function testDetailSkeleton() {
  isDetailLoading.value = true
  setTimeout(() => {
    isDetailLoading.value = false
  }, 3000)
}

function testBothSkeleton() {
  isListLoading.value = true
  isDetailLoading.value = true
  setTimeout(() => {
    isListLoading.value = false
    isDetailLoading.value = false
  }, 3000)
}

definePageMeta({
  title: 'Skeleton 测试页面',
})
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">🎭 Skeleton 加载状态测试</h1>
        <p class="mt-2 text-muted-foreground">
          测试工单列表和工单详情页的skeleton加载效果
        </p>
      </div>

      <!-- 测试控制按钮 -->
      <Card>
        <CardHeader>
          <CardTitle>测试控制</CardTitle>
          <CardDescription>
            点击按钮测试不同的skeleton加载效果
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="flex gap-4">
            <Button @click="testListSkeleton">
              测试列表Skeleton (3秒)
            </Button>
            <Button @click="testDetailSkeleton">
              测试详情Skeleton (3秒)
            </Button>
            <Button @click="testBothSkeleton">
              测试全部Skeleton (3秒)
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 工单列表测试 -->
      <Card>
        <CardHeader>
          <CardTitle>工单列表 Skeleton</CardTitle>
          <CardDescription>
            当前状态: {{ isListLoading ? '加载中' : '已加载' }}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="h-96 border rounded-lg">
            <TicketList
              :items="isListLoading ? [] : mockTickets"
              :loading="isListLoading"
              selected-ticket="1"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 工单详情测试 -->
      <Card>
        <CardHeader>
          <CardTitle>工单详情 Skeleton</CardTitle>
          <CardDescription>
            当前状态: {{ isDetailLoading ? '加载中' : '已加载' }}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="h-96 border rounded-lg">
            <TicketDetail
              :ticket="isDetailLoading ? undefined : mockTicket"
              :loading="isDetailLoading"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 状态说明 -->
      <Card>
        <CardHeader>
          <CardTitle>📋 Skeleton 功能说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4 text-sm">
            <div>
              <h4 class="font-medium mb-2">工单列表 Skeleton 包含：</h4>
              <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                <li>5个工单卡片的skeleton</li>
                <li>状态标签、标题、描述、标签等元素的占位符</li>
                <li>与实际内容布局完全匹配</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium mb-2">工单详情 Skeleton 包含：</h4>
              <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                <li>工单标题和状态区域skeleton</li>
                <li>工单流程步骤skeleton</li>
                <li>工单信息网格skeleton</li>
                <li>处理结果skeleton</li>
                <li>附件skeleton</li>
                <li>变更日志skeleton</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium mb-2">使用方式：</h4>
              <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                <li>通过 <code>loading</code> 属性控制skeleton显示</li>
                <li>使用shadcn-ui的Skeleton组件保持设计一致性</li>
                <li>自动适配响应式布局</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
