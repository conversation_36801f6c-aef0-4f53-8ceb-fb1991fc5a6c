<script setup lang="ts">
import type { TicketApiTypes } from '~/api/feedback/ticket'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { UserSelect } from '@/components/ui/user-select'

// 页面配置
definePageMeta({
  title: '人员选择组件测试',
  layout: 'default',
})

// 响应式数据
const singleUser = ref<string>('')
const multipleUsers = ref<string[]>([])
const disabledSingleUser = ref<string>('<EMAIL>')
const disabledMultipleUsers = ref<string[]>(['<EMAIL>', '<EMAIL>'])

// 事件处理
function handleSingleChange(value: string, user: TicketApiTypes.UserInfo) {
  console.log('单选变化:', { value, user })
}

function handleMultipleChange(value: string[], users: TicketApiTypes.UserInfo[]) {
  console.log('多选变化:', { value, users })
}

// 清空选择
function clearSingle() {
  singleUser.value = ''
}

function clearMultiple() {
  multipleUsers.value = []
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        人员选择组件测试
      </h1>
      <p class="text-muted-foreground">
        测试通用人员选择下拉框组件的各种功能和配置选项
      </p>
    </div>

    <div class="grid gap-6 md:grid-cols-2">
      <!-- 单选模式 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Badge variant="outline">
              单选
            </Badge>
            单选模式
          </CardTitle>
          <CardDescription>
            选择单个人员，支持搜索和清除功能
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label for="single-user">选择人员</Label>
            <UserSelect
              id="single-user"
              v-model="singleUser"
              placeholder="请选择一个人员"
              @change="handleSingleChange"
            />
          </div>

          <div class="space-y-2">
            <Label>当前选择</Label>
            <div class="text-sm text-muted-foreground">
              {{ singleUser || '未选择' }}
            </div>
          </div>

          <div class="flex gap-2">
            <Button size="sm" variant="outline" @click="clearSingle">
              清空选择
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 多选模式 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Badge variant="outline">
              多选
            </Badge>
            多选模式
          </CardTitle>
          <CardDescription>
            选择多个人员，支持标签显示和批量操作
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label for="multiple-users">选择人员</Label>
            <UserSelect
              id="multiple-users"
              v-model="multipleUsers"
              multiple
              placeholder="请选择多个人员"
              :max-tag-count="2"
              @change="handleMultipleChange"
            />
          </div>

          <div class="space-y-2">
            <Label>当前选择 ({{ multipleUsers.length }})</Label>
            <div class="text-sm text-muted-foreground">
              <div v-if="multipleUsers.length === 0">
                未选择
              </div>
              <div v-else class="space-y-1">
                <div v-for="user in multipleUsers" :key="user" class="flex items-center gap-2">
                  <Badge variant="secondary" class="text-xs">
                    {{ user }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <div class="flex gap-2">
            <Button size="sm" variant="outline" @click="clearMultiple">
              清空选择
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 禁用状态 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Badge variant="outline">
              禁用
            </Badge>
            禁用状态
          </CardTitle>
          <CardDescription>
            展示组件的禁用状态效果
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>禁用单选</Label>
            <UserSelect
              v-model="disabledSingleUser"
              disabled
              placeholder="禁用状态"
            />
          </div>

          <div class="space-y-2">
            <Label>禁用多选</Label>
            <UserSelect
              v-model="disabledMultipleUsers"
              multiple
              disabled
              placeholder="禁用状态"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 自定义配置 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <Badge variant="outline">
              自定义
            </Badge>
            自定义配置
          </CardTitle>
          <CardDescription>
            展示组件的各种自定义配置选项
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>不可清除</Label>
            <UserSelect
              v-model="singleUser"
              :clearable="false"
              placeholder="不显示清除按钮"
            />
          </div>

          <div class="space-y-2">
            <Label>自定义占位符</Label>
            <UserSelect
              v-model="multipleUsers"
              multiple
              placeholder="请指派处理人员"
              search-placeholder="输入姓名或工号搜索"
              :max-tag-count="1"
            />
          </div>

          <div class="space-y-2">
            <Label>自定义样式</Label>
            <UserSelect
              v-model="singleUser"
              class="border-2 border-dashed"
              placeholder="自定义边框样式"
            />
          </div>
        </CardContent>
      </Card>
    </div>

    <Separator />

    <!-- 使用说明 -->
    <Card>
      <CardHeader>
        <CardTitle>使用说明</CardTitle>
        <CardDescription>
          UserSelect 组件的主要特性和使用方法
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-4 md:grid-cols-2">
          <div class="space-y-2">
            <h4 class="font-semibold">
              主要特性
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 支持单选和多选模式</li>
              <li>• 实时搜索（支持中文名、拼音、工号）</li>
              <li>• 标签显示和数量限制</li>
              <li>• 清除功能和禁用状态</li>
              <li>• 响应式设计和无障碍支持</li>
              <li>• 自定义样式和配置</li>
            </ul>
          </div>

          <div class="space-y-2">
            <h4 class="font-semibold">
              API 接口
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 接口：/user/auth/userlist/</li>
              <li>• 支持关键词搜索参数</li>
              <li>• 返回用户名、ID、部门等信息</li>
              <li>• 自动防抖优化搜索性能</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
