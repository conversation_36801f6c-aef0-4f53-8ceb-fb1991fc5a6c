<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import Layout from '~/components/feedback/Layout.vue'

// 页面元数据
definePageMeta({
  title: '我处理的工单测试',
  layout: 'default',
})

// 模拟工单数据，包含不同处理人的工单
const mockTickets: Ticket[] = [
  {
    id: 'test-1',
    ticketID: 'TK001',
    title: '测试工单 - 我处理的1',
    text: '这是一个由当前用户处理的工单',
    status: '处理中',
    stage: '处理中',
    creator: 'user1',
    feedbackPerson: 'user1',
    handler: 'admin', // 当前用户处理
    devProcessor: '',
    createdAt: '2024-01-01 10:00:00',
    enterTime: '2024-01-01 10:00:00',
  },
  {
    id: 'test-2',
    ticketID: 'TK002',
    title: '测试工单 - 我处理的2',
    text: '这是另一个由当前用户开发处理的工单',
    status: '处理中',
    stage: '处理中',
    creator: 'user2',
    feedbackPerson: 'user2',
    handler: '',
    devProcessor: 'admin', // 当前用户开发处理
    createdAt: '2024-01-02 11:00:00',
    enterTime: '2024-01-02 11:00:00',
  },
  {
    id: 'test-3',
    ticketID: 'TK003',
    title: '测试工单 - 其他人处理',
    text: '这是一个由其他人处理的工单',
    status: '处理中',
    stage: '处理中',
    creator: 'user3',
    feedbackPerson: 'user3',
    handler: 'other_user',
    devProcessor: 'other_dev',
    createdAt: '2024-01-03 12:00:00',
    enterTime: '2024-01-03 12:00:00',
  },
  {
    id: 'test-4',
    ticketID: 'TK004',
    title: '测试工单 - 我创建的',
    text: '这是一个由当前用户创建的工单',
    status: '待处理',
    stage: '待处理',
    creator: 'admin',
    feedbackPerson: 'admin', // 当前用户创建
    handler: 'other_user',
    devProcessor: '',
    createdAt: '2024-01-04 13:00:00',
    enterTime: '2024-01-04 13:00:00',
  },
  {
    id: 'test-5',
    ticketID: 'TK005',
    title: '测试工单 - 我待办的',
    text: '这是一个分配给当前用户的待办工单',
    status: '待处理',
    stage: '待处理',
    creator: 'user4',
    feedbackPerson: 'user4',
    handler: '',
    devProcessor: 'admin', // 当前用户待办
    createdAt: '2024-01-05 14:00:00',
    enterTime: '2024-01-05 14:00:00',
  },
]

// 响应式数据
const tickets = ref<Ticket[]>(mockTickets)
const activeMenu = ref('handled') // 默认选中"我处理的"菜单
const selectedTicketId = ref('')

// 处理菜单变化
function handleMenuChange(menu: string) {
  activeMenu.value = menu
}

// 处理工单创建
function handleTicketCreated(ticket: Ticket) {
  tickets.value.unshift(ticket)
}

// 处理刷新
function handleRefresh() {
  console.log('刷新工单列表')
  // 这里可以重新获取数据
}

// 处理工单操作刷新
function handleTicketActionRefresh() {
  console.log('工单操作后刷新')
  handleRefresh()
}

// 处理当前工单刷新
function handleRefreshCurrentTicket() {
  console.log('刷新当前工单')
}
</script>

<template>
  <div class="h-screen">
    <Layout
      :tickets="tickets"
      :active-menu="activeMenu"
      :selected-ticket-id="selectedTicketId"
      :nav-collapsed-size="4"
      @update:active-menu="handleMenuChange"
      @ticket-created="handleTicketCreated"
      @refresh-tickets="handleRefresh"
      @ticket-action-refresh="handleTicketActionRefresh"
      @refresh-current-ticket="handleRefreshCurrentTicket"
    />
  </div>
</template>
