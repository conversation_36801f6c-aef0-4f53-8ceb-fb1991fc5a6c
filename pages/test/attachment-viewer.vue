<script setup lang="ts">
import { ref } from 'vue'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import type { AttachmentItem } from '@/components/ui/attachment-viewer'

// 模拟附件数据
const sampleAttachments = ref<AttachmentItem[]>([
  {
    id: '1',
    name: '截图1.png',
    url: 'https://picsum.photos/800/600?random=1',
    type: 'png',
    size: 245760,
    thumbnailUrl: 'https://picsum.photos/200/200?random=1'
  },
  {
    id: '2',
    name: '用户反馈截图.jpg',
    url: 'https://picsum.photos/800/600?random=2',
    type: 'jpg',
    size: 512000,
    thumbnailUrl: 'https://picsum.photos/200/200?random=2'
  },
  {
    id: '3',
    name: '问题复现视频.mp4',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    type: 'mp4',
    size: 15728640
  },
  {
    id: '4',
    name: '界面异常.jpeg',
    url: 'https://picsum.photos/800/600?random=3',
    type: 'jpeg',
    size: 389120,
    thumbnailUrl: 'https://picsum.photos/200/200?random=3'
  },
  {
    id: '5',
    name: '操作录屏.mov',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    type: 'mov',
    size: 25165824
  },
  {
    id: '6',
    name: '错误日志截图.png',
    url: 'https://picsum.photos/800/600?random=4',
    type: 'png',
    size: 156672,
    thumbnailUrl: 'https://picsum.photos/200/200?random=4'
  },
  {
    id: '7',
    name: '系统截图.jpg',
    url: 'https://picsum.photos/800/600?random=5',
    type: 'jpg',
    size: 445440,
    thumbnailUrl: 'https://picsum.photos/200/200?random=5'
  },
  {
    id: '8',
    name: '演示视频.rmvb',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    type: 'rmvb',
    size: 18874368
  }
])

// 不同配置的附件数据
const fewAttachments = ref<AttachmentItem[]>(sampleAttachments.value.slice(0, 3))
const manyAttachments = ref<AttachmentItem[]>(sampleAttachments.value)
const imageOnlyAttachments = ref<AttachmentItem[]>(
  sampleAttachments.value.filter(item => ['png', 'jpg', 'jpeg'].includes(item.type))
)
const videoOnlyAttachments = ref<AttachmentItem[]>(
  sampleAttachments.value.filter(item => ['mp4', 'mov', 'rmvb'].includes(item.type))
)

// 处理下载事件
const handleDownload = (attachment: AttachmentItem) => {
  console.log('下载附件:', attachment.name)
  // 这里可以添加实际的下载逻辑
}

// 页面标题
definePageMeta({
  title: '附件查看器测试'
})
</script>

<template>
  <div class="container mx-auto py-8 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">附件查看器组件测试</h1>
      <p class="text-muted-foreground">
        测试 AttachmentViewer 组件的各种功能和配置选项
      </p>
    </div>

    <div class="grid gap-6">
      <!-- 基本用法 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            基本用法
            <Badge variant="secondary">默认配置</Badge>
          </CardTitle>
          <CardDescription>
            展示混合类型的附件，包含图片和视频，支持缩略图预览和下载
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AttachmentViewer
            :attachments="fewAttachments"
            @download="handleDownload"
          />
        </CardContent>
      </Card>

      <Separator />

      <!-- 大量附件 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            大量附件展示
            <Badge variant="secondary">maxThumbnails=4</Badge>
          </CardTitle>
          <CardDescription>
            当附件数量超过限制时，会显示"更多"按钮
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AttachmentViewer
            :attachments="manyAttachments"
            :max-thumbnails="4"
            @download="handleDownload"
          />
        </CardContent>
      </Card>

      <Separator />

      <!-- 不同尺寸 -->
      <div class="grid md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              小尺寸
              <Badge variant="secondary">size=sm</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AttachmentViewer
              :attachments="imageOnlyAttachments.slice(0, 4)"
              thumbnail-size="sm"
              @download="handleDownload"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              中等尺寸
              <Badge variant="secondary">size=md</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AttachmentViewer
              :attachments="imageOnlyAttachments.slice(0, 4)"
              thumbnail-size="md"
              @download="handleDownload"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              大尺寸
              <Badge variant="secondary">size=lg</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <AttachmentViewer
              :attachments="imageOnlyAttachments.slice(0, 4)"
              thumbnail-size="lg"
              @download="handleDownload"
            />
          </CardContent>
        </Card>
      </div>

      <Separator />

      <!-- 仅图片 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            仅图片附件
            <Badge variant="secondary">图片类型</Badge>
          </CardTitle>
          <CardDescription>
            只包含 PNG、JPG、JPEG 格式的图片文件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AttachmentViewer
            :attachments="imageOnlyAttachments"
            @download="handleDownload"
          />
        </CardContent>
      </Card>

      <Separator />

      <!-- 仅视频 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            仅视频附件
            <Badge variant="secondary">视频类型</Badge>
          </CardTitle>
          <CardDescription>
            只包含 MP4、MOV、RMVB 格式的视频文件
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AttachmentViewer
            :attachments="videoOnlyAttachments"
            @download="handleDownload"
          />
        </CardContent>
      </Card>

      <Separator />

      <!-- 隐藏文件名 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            隐藏文件名
            <Badge variant="secondary">showFileName=false</Badge>
          </CardTitle>
          <CardDescription>
            不显示文件名，只显示缩略图
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AttachmentViewer
            :attachments="fewAttachments"
            :show-file-name="false"
            @download="handleDownload"
          />
        </CardContent>
      </Card>

      <Separator />

      <!-- 禁用下载 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            禁用下载
            <Badge variant="secondary">allowDownload=false</Badge>
          </CardTitle>
          <CardDescription>
            隐藏下载按钮，只允许预览
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AttachmentViewer
            :attachments="fewAttachments"
            :allow-download="false"
          />
        </CardContent>
      </Card>

      <Separator />

      <!-- 空状态 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            空状态
            <Badge variant="secondary">无附件</Badge>
          </CardTitle>
          <CardDescription>
            当没有附件时的显示状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AttachmentViewer :attachments="[]" />
        </CardContent>
      </Card>
    </div>

    <!-- 使用说明 -->
    <Card>
      <CardHeader>
        <CardTitle>使用说明</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div>
          <h4 class="font-medium mb-2">支持的文件格式：</h4>
          <div class="flex gap-2 flex-wrap">
            <Badge variant="outline">PNG</Badge>
            <Badge variant="outline">JPEG</Badge>
            <Badge variant="outline">JPG</Badge>
            <Badge variant="outline">MP4</Badge>
            <Badge variant="outline">RMVB</Badge>
            <Badge variant="outline">MOV</Badge>
          </div>
        </div>
        
        <div>
          <h4 class="font-medium mb-2">主要功能：</h4>
          <ul class="list-disc list-inside space-y-1 text-sm text-muted-foreground">
            <li>缩略图展示，支持三种尺寸（sm、md、lg）</li>
            <li>点击预览大图或播放视频</li>
            <li>支持键盘导航（左右箭头切换，ESC关闭）</li>
            <li>文件下载功能</li>
            <li>自动统计文件类型和数量</li>
            <li>响应式网格布局</li>
            <li>当附件过多时显示"更多"按钮</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
