<script setup lang="ts">
import { 
  APP_ICONS, 
  LABEL_ICONS, 
  SEVERITY_ICONS,
  filterFieldConfigs,
  getAppIcon,
  getLabelIcon,
  getSeverityIcon,
  getCategoryIcon
} from '~/constants/filterCategories'

// 页面标题
useHead({
  title: '筛选图标展示',
})

// 应用图标展示数据
const appIconsData = Object.entries(APP_ICONS).map(([name, icon]) => ({
  name,
  icon,
  isDefault: name === 'default'
}))

// 标签图标展示数据
const labelIconsData = Object.entries(LABEL_ICONS).map(([name, icon]) => ({
  name,
  icon,
  isDefault: name === 'default'
}))

// 严重程度图标展示数据
const severityIconsData = Object.entries(SEVERITY_ICONS).map(([name, icon]) => ({
  name,
  icon,
  isDefault: name === 'default'
}))

// 类别图标展示数据
const categoryIconsData = filterFieldConfigs.map(config => ({
  name: config.label,
  key: config.key,
  icon: config.icon,
}))
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-2">筛选图标展示</h1>
      <p class="text-muted-foreground">
        展示筛选菜单中使用的各种图标
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 类别图标 -->
      <Card>
        <CardHeader>
          <CardTitle>筛选类别图标</CardTitle>
          <CardDescription>
            每个筛选类别的主图标
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 gap-3">
            <div
              v-for="category in categoryIconsData"
              :key="category.key"
              class="flex items-center gap-3 p-3 border rounded-lg"
            >
              <component
                :is="category.icon"
                v-if="category.icon"
                class="size-5 text-primary"
              />
              <div class="size-5 bg-muted rounded flex items-center justify-center" v-else>
                <span class="text-xs text-muted-foreground">?</span>
              </div>
              <div>
                <div class="font-medium">{{ category.name }}</div>
                <div class="text-sm text-muted-foreground">{{ category.key }}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 应用图标 -->
      <Card>
        <CardHeader>
          <CardTitle>应用图标</CardTitle>
          <CardDescription>
            不同应用对应的图标
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-2 gap-3">
            <div
              v-for="app in appIconsData.filter(a => !a.isDefault)"
              :key="app.name"
              class="flex items-center gap-2 p-2 border rounded-lg"
            >
              <component
                :is="app.icon"
                class="size-4 text-primary"
              />
              <span class="text-sm">{{ app.name }}</span>
            </div>
          </div>
          <div class="mt-3 pt-3 border-t">
            <div class="flex items-center gap-2 text-sm text-muted-foreground">
              <component
                :is="APP_ICONS.default"
                class="size-4"
              />
              <span>默认图标（其他应用）</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 标签图标 -->
      <Card>
        <CardHeader>
          <CardTitle>标签图标</CardTitle>
          <CardDescription>
            不同标签类型对应的图标
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 gap-2">
            <div
              v-for="label in labelIconsData.filter(l => !l.isDefault)"
              :key="label.name"
              class="flex items-center gap-2 p-2 border rounded-lg"
            >
              <component
                :is="label.icon"
                class="size-4 text-primary"
              />
              <span class="text-sm">{{ label.name }}</span>
            </div>
          </div>
          <div class="mt-3 pt-3 border-t">
            <div class="flex items-center gap-2 text-sm text-muted-foreground">
              <component
                :is="LABEL_ICONS.default"
                class="size-4"
              />
              <span>默认图标（其他标签）</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 严重程度图标 -->
      <Card>
        <CardHeader>
          <CardTitle>严重程度图标</CardTitle>
          <CardDescription>
            不同严重程度对应的图标
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 gap-3">
            <div
              v-for="severity in severityIconsData.filter(s => !s.isDefault)"
              :key="severity.name"
              class="flex items-center gap-3 p-3 border rounded-lg"
            >
              <component
                :is="severity.icon"
                class="size-5"
                :class="{
                  'text-red-500': severity.name === '高',
                  'text-yellow-500': severity.name === '中',
                  'text-green-500': severity.name === '低'
                }"
              />
              <div>
                <div class="font-medium">{{ severity.name }}</div>
                <div class="text-sm text-muted-foreground">
                  {{ severity.name === '高' ? '高优先级' : severity.name === '中' ? '中等优先级' : '低优先级' }}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 图标使用示例 -->
      <Card class="lg:col-span-2">
        <CardHeader>
          <CardTitle>图标使用示例</CardTitle>
          <CardDescription>
            展示如何在代码中使用这些图标
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div>
              <h4 class="font-medium mb-2">获取应用图标</h4>
              <div class="bg-muted p-3 rounded-lg font-mono text-sm">
                <div>import { getAppIcon } from '~/constants/filterCategories'</div>
                <div>const icon = getAppIcon('TT语音') // 返回 Mic 组件</div>
              </div>
            </div>
            
            <div>
              <h4 class="font-medium mb-2">获取标签图标</h4>
              <div class="bg-muted p-3 rounded-lg font-mono text-sm">
                <div>import { getLabelIcon } from '~/constants/filterCategories'</div>
                <div>const icon = getLabelIcon('登录问题') // 返回 User 组件</div>
              </div>
            </div>
            
            <div>
              <h4 class="font-medium mb-2">获取类别图标</h4>
              <div class="bg-muted p-3 rounded-lg font-mono text-sm">
                <div>import { getCategoryIcon } from '~/constants/filterCategories'</div>
                <div>const icon = getCategoryIcon('status') // 返回 Activity 组件</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
