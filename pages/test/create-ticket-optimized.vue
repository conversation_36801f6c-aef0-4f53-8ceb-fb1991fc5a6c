<script setup lang="ts">
import CreateTicketForm from '~/components/feedback/CreateTicketForm.vue'
import CreateTicketFormCompact from '~/components/feedback/CreateTicketFormCompact.vue'

definePageMeta({
  title: '优化后的创建工单表单 - 垂直单列布局',
  description: '测试优化后的创建工单表单用户体验，采用垂直单列紧凑布局',
})

const showForm = ref(false)
const showOriginalForm = ref(false)

function openForm() {
  showForm.value = true
}

function openOriginalForm() {
  showOriginalForm.value = true
}

function handleSuccess() {
  console.log('工单创建成功')
  showForm.value = false
  showOriginalForm.value = false
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <!-- 页面标题 -->
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        优化后的创建工单表单 - 垂直单列布局
      </h1>
      <p class="text-muted-foreground">
        测试优化后的创建工单表单，采用垂直单列紧凑布局，参考 /settings/profile 页面设计风格
      </p>
    </div>

    <!-- 优化说明 -->
    <div class="grid gap-6 lg:grid-cols-3 md:grid-cols-2">
      <div class="border rounded-lg bg-card p-6">
        <div class="mb-3 flex items-center gap-2">
          <Icon name="i-lucide-eye" class="h-5 w-5 text-primary" />
          <h3 class="font-semibold">
            视觉优化
          </h3>
        </div>
        <ul class="text-sm text-muted-foreground space-y-1">
          <li>• 更清晰的字段标签和描述</li>
          <li>• 实时验证状态指示</li>
          <li>• 卡片式分组布局</li>
          <li>• 图标和颜色区分</li>
        </ul>
      </div>

      <div class="border rounded-lg bg-card p-6">
        <div class="mb-3 flex items-center gap-2">
          <Icon name="i-lucide-zap" class="h-5 w-5 text-primary" />
          <h3 class="font-semibold">
            交互优化
          </h3>
        </div>
        <ul class="text-sm text-muted-foreground space-y-1">
          <li>• 智能默认值和快捷选择</li>
          <li>• 常用选项快速添加</li>
          <li>• 点击区域扩大</li>
          <li>• 实时字符计数</li>
        </ul>
      </div>

      <div class="border rounded-lg bg-card p-6">
        <div class="mb-3 flex items-center gap-2">
          <Icon name="i-lucide-check-circle" class="h-5 w-5 text-primary" />
          <h3 class="font-semibold">
            验证优化
          </h3>
        </div>
        <ul class="text-sm text-muted-foreground space-y-1">
          <li>• 实时表单验证</li>
          <li>• 详细错误提示</li>
          <li>• 进度状态显示</li>
          <li>• 智能提交控制</li>
        </ul>
      </div>
    </div>

    <!-- 测试按钮 -->
    <div class="space-y-4">
      <div class="flex items-center gap-4">
        <Button size="lg" @click="openForm">
          <Icon name="i-lucide-plus-circle" class="mr-2 h-5 w-5" />
          打开垂直单列布局表单
        </Button>
        <Button size="lg" variant="outline" @click="openOriginalForm">
          <Icon name="i-lucide-layout-grid" class="mr-2 h-5 w-5" />
          打开原始网格布局表单
        </Button>
      </div>
      <div class="text-sm text-muted-foreground">
        点击按钮对比两种布局的表单界面，体验垂直单列布局的紧凑性和易用性
      </div>
    </div>

    <!-- 优化详情 -->
    <div class="space-y-6">
      <h2 class="text-2xl font-semibold">
        优化详情
      </h2>

      <div class="grid gap-6 lg:grid-cols-2">
        <div class="space-y-4">
          <h3 class="text-lg font-medium">
            用户体验改进
          </h3>
          <div class="text-sm space-y-3">
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>垂直单列布局：</strong>参考 /settings/profile 页面设计，采用紧凑的垂直布局
              </div>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>分组优化：</strong>使用分隔线和标题清晰划分基本信息、产品信息、环境信息等
              </div>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>间距紧凑：</strong>减少不必要的空白，提高信息密度和填写效率
              </div>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>实时反馈：</strong>字段验证状态实时显示，错误信息即时提示
              </div>
            </div>
          </div>
        </div>

        <div class="space-y-4">
          <h3 class="text-lg font-medium">
            技术改进
          </h3>
          <div class="text-sm space-y-3">
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>表单验证：</strong>使用computed属性实现响应式验证，提供详细的错误信息
              </div>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>数据结构：</strong>优化选项数据结构，添加描述、图标和颜色信息
              </div>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>交互逻辑：</strong>改进点击事件处理，支持整个卡片区域点击
              </div>
            </div>
            <div class="flex items-start gap-2">
              <Icon name="i-lucide-check" class="mt-0.5 h-4 w-4 text-green-600" />
              <div>
                <strong>状态管理：</strong>优化表单状态管理，提供更好的用户反馈
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建工单表单 -->
    <CreateTicketFormCompact
      v-model:open="showForm"
      @success="handleSuccess"
    />

    <!-- 原始表单（对比用） -->
    <CreateTicketForm
      v-model:open="showOriginalForm"
      @success="handleSuccess"
    />
  </div>
</template>
