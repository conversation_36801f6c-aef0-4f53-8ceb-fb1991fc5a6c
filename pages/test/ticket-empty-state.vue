<script setup lang="ts">
import type { Ticket } from '~/types/ticket'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Layout from '~/components/feedback/Layout.vue'

// 模拟工单数据
const mockTickets = ref<Ticket[]>([
  {
    id: 'test-1',
    ticketID: 'TK001',
    title: '测试工单 1',
    text: '这是一个测试工单',
    problemDescription: '测试问题描述',
    status: '待处理',
    stage: '待处理',
    creator: 'user1',
    feedbackPerson: 'user1',
    createdAt: '2024-01-01 10:00:00',
    enterTime: '2024-01-01 10:00:00',
  },
  {
    id: 'test-2',
    ticketID: 'TK002',
    title: '测试工单 2',
    text: '这是另一个测试工单',
    problemDescription: '另一个测试问题描述',
    status: '处理中',
    stage: '处理中',
    creator: 'user2',
    feedbackPerson: 'user2',
    handler: 'admin',
    devProcessor: 'admin',
    createdAt: '2024-01-02 11:00:00',
    enterTime: '2024-01-02 11:00:00',
  },
])

const loading = ref(false)
const error = ref<string | null>(null)

// 当前活动菜单
const currentActiveMenu = ref('all')

// 当前路径
const currentPath = ref('')

// 监听路径变化
if (import.meta.client) {
  currentPath.value = window.location.pathname

  // 监听 URL 变化
  window.addEventListener('urlchange', () => {
    currentPath.value = window.location.pathname
  })

  // 监听浏览器前进后退
  window.addEventListener('popstate', () => {
    currentPath.value = window.location.pathname
  })
}

// 测试场景
const testScenarios = [
  {
    name: '有工单数据',
    description: '显示正常的工单列表和详情',
    action: () => {
      mockTickets.value = [
        {
          id: 'test-1',
          ticketID: 'TK001',
          title: '测试工单 1',
          text: '这是一个测试工单',
          problemDescription: '测试问题描述',
          status: '待处理',
          stage: '待处理',
          creator: 'user1',
          feedbackPerson: 'user1',
          createdAt: '2024-01-01 10:00:00',
          enterTime: '2024-01-01 10:00:00',
        },
        {
          id: 'test-2',
          ticketID: 'TK002',
          title: '测试工单 2',
          text: '这是另一个测试工单',
          problemDescription: '另一个测试问题描述',
          status: '处理中',
          stage: '处理中',
          creator: 'user2',
          feedbackPerson: 'user2',
          handler: 'admin',
          devProcessor: 'admin',
          createdAt: '2024-01-02 11:00:00',
          enterTime: '2024-01-02 11:00:00',
        },
      ]
      currentActiveMenu.value = 'all'
    },
  },
  {
    name: '清空工单列表',
    description: '清空工单列表，工单详情应该显示空状态',
    action: () => {
      mockTickets.value = []
      currentActiveMenu.value = 'all'
    },
  },
  {
    name: '切换到空菜单',
    description: '切换到没有工单的菜单分类（如已归档），工单详情应该显示空状态',
    action: () => {
      // 设置一些工单，但都不是已归档状态
      mockTickets.value = [
        {
          id: 'test-1',
          ticketID: 'TK001',
          title: '测试工单 1',
          text: '这是一个测试工单',
          problemDescription: '测试问题描述',
          status: '待处理',
          stage: '待处理',
          creator: 'user1',
          feedbackPerson: 'user1',
          createdAt: '2024-01-01 10:00:00',
          enterTime: '2024-01-01 10:00:00',
        },
      ]
      // 切换到已归档菜单（应该没有工单）
      currentActiveMenu.value = 'archived'
    },
  },
  {
    name: '加载状态',
    description: '模拟加载状态',
    action: () => {
      loading.value = true
      setTimeout(() => {
        loading.value = false
      }, 2000)
    },
  },
  {
    name: '错误状态',
    description: '模拟错误状态',
    action: () => {
      error.value = '获取工单数据失败'
      setTimeout(() => {
        error.value = null
      }, 3000)
    },
  },
]

function runTest(scenario: typeof testScenarios[0]) {
  scenario.action()
}

// 页面标题
useHead({
  title: '工单空状态测试',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mb-6">
      <h1 class="mb-2 text-2xl font-bold">
        工单空状态测试
      </h1>
      <p class="text-muted-foreground">
        测试当工单列表为空时，工单详情页是否正确显示空状态
      </p>
    </div>

    <!-- 测试控制面板 -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle>测试控制面板</CardTitle>
        <CardDescription>
          点击下面的按钮来测试不同的场景
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 gap-4 lg:grid-cols-4 md:grid-cols-2">
          <Button
            v-for="scenario in testScenarios"
            :key="scenario.name"
            variant="outline"
            class="h-auto flex flex-col items-start gap-2 p-4"
            @click="runTest(scenario)"
          >
            <div class="font-medium">
              {{ scenario.name }}
            </div>
            <div class="text-left text-xs text-muted-foreground">
              {{ scenario.description }}
            </div>
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 当前状态显示 -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle>当前状态</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2">
          <div>
            <span class="font-medium">工单数量:</span>
            {{ mockTickets.length }}
          </div>
          <div>
            <span class="font-medium">当前菜单:</span>
            {{ currentActiveMenu }}
          </div>
          <div>
            <span class="font-medium">当前路由:</span>
            {{ currentPath }}
          </div>
          <div>
            <span class="font-medium">加载状态:</span>
            {{ loading ? '加载中' : '已完成' }}
          </div>
          <div>
            <span class="font-medium">错误状态:</span>
            {{ error || '无错误' }}
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 工单布局组件 -->
    <div class="overflow-hidden border rounded-lg" style="height: 600px;">
      <Layout
        :tickets="mockTickets"
        :loading="loading"
        :error="error"
        :nav-collapsed-size="4"
        :active-menu="currentActiveMenu"
        @update:active-menu="(menu) => { currentActiveMenu = menu }"
        @ticket-created="() => {}"
        @refresh-tickets="() => {}"
        @ticket-action-refresh="() => {}"
        @refresh-current-ticket="() => {}"
      />
    </div>
  </div>
</template>
