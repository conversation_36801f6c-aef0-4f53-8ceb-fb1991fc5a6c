<script setup lang="ts">
import { useCreateTicketModal } from '~/composables/useCreateTicketModal'

definePageMeta({
  title: '创建工单测试',
  layout: 'default',
})

const { showCreateTicketModal } = useCreateTicketModal()

async function handleCreateTicket() {
  try {
    const success = await showCreateTicketModal()
    if (success) {
      console.log('工单创建成功')
    }
    else {
      console.log('工单创建取消')
    }
  }
  catch (error) {
    console.error('创建工单失败:', error)
  }
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mx-auto max-w-md">
      <h1 class="mb-6 text-2xl font-bold">
        创建工单测试
      </h1>

      <Button class="w-full" @click="handleCreateTicket">
        创建工单
      </Button>
    </div>
  </div>
</template>
