<script setup lang="ts">
import { ref } from 'vue'
import CreateTicketForm from '~/components/feedback/CreateTicketForm.vue'

// 页面标题
definePageMeta({
  title: '创建工单附件预览测试',
})

// 弹窗状态
const isCreateFormOpen = ref(false)

// 处理创建成功
function handleCreateSuccess() {
  console.log('✅ 工单创建成功')
}

// 打开创建工单弹窗
function openCreateForm() {
  isCreateFormOpen.value = true
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold">
        创建工单附件预览测试
      </h1>
      <Button @click="openCreateForm">
        创建工单
      </Button>
    </div>

    <!-- 功能说明 -->
    <Card>
      <CardHeader>
        <CardTitle>功能特性</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <h4 class="mb-2 font-medium">
              上传功能
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 支持拖拽上传和点击上传</li>
              <li>• 实时上传进度显示</li>
              <li>• 上传失败重试机制</li>
              <li>• 文件大小和类型验证</li>
              <li>• 最多上传10个文件，单个最大100MB</li>
            </ul>
          </div>
          <div>
            <h4 class="mb-2 font-medium">
              预览功能
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 缩略图网格展示</li>
              <li>• 点击预览大图或播放视频</li>
              <li>• 支持键盘导航（左右箭头、ESC）</li>
              <li>• 智能缩放适应屏幕</li>
              <li>• 支持文件下载</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 支持格式 -->
    <Card>
      <CardHeader>
        <CardTitle>支持的文件格式</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <h4 class="mb-2 flex items-center gap-2 font-medium">
              <Icon name="i-lucide-image" class="h-4 w-4" />
              图片格式
            </h4>
            <div class="flex gap-2">
              <Badge variant="secondary">
                PNG
              </Badge>
              <Badge variant="secondary">
                JPEG
              </Badge>
              <Badge variant="secondary">
                JPG
              </Badge>
            </div>
          </div>
          <div>
            <h4 class="mb-2 flex items-center gap-2 font-medium">
              <Icon name="i-lucide-video" class="h-4 w-4" />
              视频格式
            </h4>
            <div class="flex gap-2">
              <Badge variant="secondary">
                MP4
              </Badge>
              <Badge variant="secondary">
                MOV
              </Badge>
              <Badge variant="secondary">
                RMVB
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 使用流程 -->
    <Card>
      <CardHeader>
        <CardTitle>使用流程</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="flex items-start gap-3">
            <div class="h-6 w-6 flex flex-shrink-0 items-center justify-center rounded-full bg-primary text-sm text-primary-foreground font-medium">
              1
            </div>
            <div>
              <h4 class="font-medium">
                填写工单基本信息
              </h4>
              <p class="text-sm text-muted-foreground">
                填写问题描述、功能类型、发生时间等必填字段
              </p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <div class="h-6 w-6 flex flex-shrink-0 items-center justify-center rounded-full bg-primary text-sm text-primary-foreground font-medium">
              2
            </div>
            <div>
              <h4 class="font-medium">
                上传附件
              </h4>
              <p class="text-sm text-muted-foreground">
                拖拽或点击上传截图、视频等相关文件
              </p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <div class="h-6 w-6 flex flex-shrink-0 items-center justify-center rounded-full bg-primary text-sm text-primary-foreground font-medium">
              3
            </div>
            <div>
              <h4 class="font-medium">
                预览附件
              </h4>
              <p class="text-sm text-muted-foreground">
                在"已上传的附件"区域查看缩略图，点击可全屏预览
              </p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <div class="h-6 w-6 flex flex-shrink-0 items-center justify-center rounded-full bg-primary text-sm text-primary-foreground font-medium">
              4
            </div>
            <div>
              <h4 class="font-medium">
                提交工单
              </h4>
              <p class="text-sm text-muted-foreground">
                确认信息无误后点击"创建工单"按钮提交
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 预览功能说明 -->
    <Card>
      <CardHeader>
        <CardTitle>预览功能详解</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div class="border rounded-lg p-4 text-center">
            <Icon name="i-lucide-grid-3x3" class="mx-auto mb-2 h-8 w-8 text-primary" />
            <h4 class="mb-1 font-medium">
              缩略图网格
            </h4>
            <p class="text-xs text-muted-foreground">
              智能网格布局，最多显示6个缩略图
            </p>
          </div>

          <div class="border rounded-lg p-4 text-center">
            <Icon name="i-lucide-maximize" class="mx-auto mb-2 h-8 w-8 text-primary" />
            <h4 class="mb-1 font-medium">
              全屏预览
            </h4>
            <p class="text-xs text-muted-foreground">
              点击缩略图可全屏查看，支持缩放
            </p>
          </div>

          <div class="border rounded-lg p-4 text-center">
            <Icon name="i-lucide-keyboard" class="mx-auto mb-2 h-8 w-8 text-primary" />
            <h4 class="mb-1 font-medium">
              键盘导航
            </h4>
            <p class="text-xs text-muted-foreground">
              左右箭头切换，ESC键关闭预览
            </p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 测试说明 -->
    <Alert>
      <AlertDescription>
        <strong>测试说明：</strong>
        <br>
        1. 点击"创建工单"按钮打开创建工单弹窗
        <br>
        2. 填写必填字段（问题描述、功能类型、问题类型、发生时间、紧急程度、产品类型、设备系统）
        <br>
        3. 在"截图视频"区域上传文件，观察上传进度和状态
        <br>
        4. 上传成功后在"已上传的附件"区域查看缩略图
        <br>
        5. 点击缩略图测试全屏预览功能
        <br>
        6. 使用键盘左右箭头切换附件，ESC键关闭预览
        <br>
        7. 查看浏览器控制台的详细日志信息
      </AlertDescription>
    </Alert>

    <!-- 创建工单弹窗 -->
    <CreateTicketForm
      v-model:open="isCreateFormOpen"
      @success="handleCreateSuccess"
    />
  </div>
</template>
