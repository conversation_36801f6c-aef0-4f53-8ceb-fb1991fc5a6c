<script setup lang="ts">
import { createTicket } from '~/api/feedback/ticket'

const testResult = ref<any>(null)
const isLoading = ref(false)
const error = ref<string | null>(null)

// 模拟成功的工单数据
const mockTicketData = {
  problemDescription: "测试API响应处理",
  functionType: ["【测试】一级功能", "【测试】二级功能", "【测试】三级功能"],
  endType: ["服务端"],
  startTime: "2025-06-09T15:52",
  severityLevel: "个例问题",
  apps: ["TT语音"],
  appVersion: ["1.1.0"],
  osType: ["Android"],
  mobileType: ["iPhone 15 Pro"],
  userTtid: ["4564634"],
  isUploadLogs: "未反馈",
  attachments: []
}

// 测试API调用
async function testCreateTicket() {
  isLoading.value = true
  error.value = null
  testResult.value = null

  try {
    console.log('发送请求数据:', mockTicketData)
    
    const response = await createTicket({
      data: mockTicketData,
    })

    console.log('API响应:', response)
    testResult.value = response

    // 根据修复后的逻辑，如果响应存在就表示成功
    if (response) {
      console.log('✅ 创建成功 - 响应拦截器已处理成功状态')
    } else {
      console.log('❌ 创建失败 - 响应为空')
      error.value = '响应为空'
    }
  } catch (err: any) {
    console.error('❌ API调用失败:', err)
    error.value = err.message || '未知错误'
    
    // 显示详细的错误信息
    if (err.response) {
      console.log('错误响应:', err.response)
    }
    if (err.errorResult) {
      console.log('错误处理结果:', err.errorResult)
    }
  } finally {
    isLoading.value = false
  }
}

// 清空结果
function clearResults() {
  testResult.value = null
  error.value = null
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        API 响应处理测试
      </h1>
      <p class="text-muted-foreground">
        测试创建工单API的响应处理逻辑，验证响应拦截器的行为
      </p>
    </div>

    <!-- 测试控制 -->
    <Card>
      <CardHeader>
        <CardTitle>测试控制</CardTitle>
        <CardDescription>
          点击按钮测试创建工单API调用
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="flex gap-2">
          <Button 
            @click="testCreateTicket" 
            :disabled="isLoading"
          >
            <Icon v-if="isLoading" name="i-lucide-loader-2" class="mr-2 size-4 animate-spin" />
            {{ isLoading ? '测试中...' : '测试创建工单' }}
          </Button>
          <Button variant="outline" @click="clearResults">
            清空结果
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 请求数据 -->
    <Card>
      <CardHeader>
        <CardTitle>请求数据</CardTitle>
        <CardDescription>
          将要发送的工单数据
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="rounded-md bg-muted p-4">
          <pre class="text-xs overflow-auto">{{ JSON.stringify(mockTicketData, null, 2) }}</pre>
        </div>
      </CardContent>
    </Card>

    <!-- 成功响应 -->
    <Card v-if="testResult && !error">
      <CardHeader>
        <CardTitle class="text-green-600">✅ 测试成功</CardTitle>
        <CardDescription>
          API调用成功，响应拦截器正常处理
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="space-y-2">
            <Label class="font-medium">响应数据类型</Label>
            <div class="rounded-md bg-muted p-3 text-sm">
              {{ typeof testResult }}
            </div>
          </div>
          
          <div class="space-y-2">
            <Label class="font-medium">响应数据内容</Label>
            <div class="rounded-md bg-muted p-4">
              <pre class="text-xs overflow-auto">{{ JSON.stringify(testResult, null, 2) }}</pre>
            </div>
          </div>

          <div class="space-y-2">
            <Label class="font-medium">分析结果</Label>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>响应拦截器成功处理了 code: 20000 的响应</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>返回了 data.data 部分，包含工单的详细信息</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>CreateTicketForm 的修复逻辑应该能正确处理这种响应</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 错误响应 -->
    <Card v-if="error">
      <CardHeader>
        <CardTitle class="text-red-600">❌ 测试失败</CardTitle>
        <CardDescription>
          API调用失败或响应处理异常
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="space-y-2">
            <Label class="font-medium">错误信息</Label>
            <div class="rounded-md bg-red-50 border border-red-200 p-3 text-sm text-red-700">
              {{ error }}
            </div>
          </div>

          <div class="space-y-2">
            <Label class="font-medium">可能的原因</Label>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>网络连接问题</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>API端点不可用</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>认证token问题</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>请求数据格式问题</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 响应拦截器说明 -->
    <Card>
      <CardHeader>
        <CardTitle>响应拦截器逻辑说明</CardTitle>
        <CardDescription>
          解释当前的响应处理机制
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="space-y-2">
            <h4 class="font-semibold text-sm">原始API响应格式</h4>
            <div class="rounded-md bg-muted p-3 text-xs">
              <pre>{
  "code": 20000,
  "msg": "请求成功", 
  "data": { /* 实际数据 */ },
  "status": "ok"
}</pre>
            </div>
          </div>

          <div class="space-y-2">
            <h4 class="font-semibold text-sm">响应拦截器处理逻辑</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>检查 code === 20000 || code === 0 || status === 'ok'</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>如果成功，返回 data.data（实际数据部分）</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>如果失败，抛出错误并进入错误处理流程</span>
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <h4 class="font-semibold text-sm">修复后的组件逻辑</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>不再检查 response.code === 20000</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>直接检查 response 是否存在（truthy）</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>如果存在响应数据，就表示创建成功</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
