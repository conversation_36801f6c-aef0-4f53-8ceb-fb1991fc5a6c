<script setup lang="ts">
import DateTimePicker from '@/components/ui/date-time-picker/DateTimePicker.vue'

// 状态管理
const selectedDateTime = ref('')
const selectedDateTime2 = ref('')
const selectedDateTime3 = ref('')

// 预设一些测试值
const presetDateTime = ref('2024-12-20T14:30')

// 处理值变化
function handleDateTimeChange(value: string) {
  console.log('DateTime changed:', value)
}

// 设置预设值
function setPresetValue() {
  selectedDateTime2.value = presetDateTime.value
}

// 清空所有值
function clearAllValues() {
  selectedDateTime.value = ''
  selectedDateTime2.value = ''
  selectedDateTime3.value = ''
}

// 设置当前时间
function setCurrentTime() {
  const now = new Date()
  const isoString = now.toISOString()
  const datetimeLocal = isoString.slice(0, 16) // YYYY-MM-DDTHH:mm
  selectedDateTime3.value = datetimeLocal
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-4">
      <h1 class="text-2xl font-bold">
        DateTimePicker 组件测试
      </h1>
      <p class="text-muted-foreground">
        测试基于 @vuepic/vue-datepicker 的新日期时间选择器组件，保持 shadcn-ui 设计风格
      </p>
    </div>

    <!-- 基础使用 -->
    <Card>
      <CardHeader>
        <CardTitle>基础日期时间选择</CardTitle>
        <CardDescription>基本的日期时间选择功能</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>选择日期和时间</Label>
          <DateTimePicker
            v-model="selectedDateTime"
            placeholder="请选择日期和时间"
            @update:model-value="handleDateTimeChange"
          />
        </div>

        <div class="space-y-2">
          <Label>选择结果</Label>
          <div class="rounded-md bg-muted p-3">
            <pre class="text-sm">{{ selectedDateTime || '未选择' }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 预设值测试 -->
    <Card>
      <CardHeader>
        <CardTitle>预设值测试</CardTitle>
        <CardDescription>测试设置和修改预设值</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>预设值输入</Label>
          <Input
            v-model="presetDateTime"
            type="datetime-local"
            placeholder="输入预设的日期时间"
          />
        </div>

        <div class="space-y-2">
          <Label>日期时间选择器（带预设值）</Label>
          <DateTimePicker
            v-model="selectedDateTime2"
            placeholder="请选择日期和时间"
          />
        </div>

        <div class="flex gap-2">
          <Button variant="outline" size="sm" @click="setPresetValue">
            设置预设值
          </Button>
        </div>

        <div class="space-y-2">
          <Label>选择结果</Label>
          <div class="rounded-md bg-muted p-3">
            <pre class="text-sm">{{ selectedDateTime2 || '未选择' }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 必填和禁用状态 -->
    <Card>
      <CardHeader>
        <CardTitle>状态测试</CardTitle>
        <CardDescription>测试必填、禁用等状态</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>必填字段</Label>
          <DateTimePicker
            v-model="selectedDateTime3"
            placeholder="这是必填字段"
            :required="true"
          />
        </div>

        <div class="space-y-2">
          <Label>禁用状态</Label>
          <DateTimePicker
            model-value="2024-12-20T10:30"
            placeholder="禁用状态"
            :disabled="true"
          />
        </div>

        <div class="flex gap-2">
          <Button variant="outline" size="sm" @click="setCurrentTime">
            设为当前时间
          </Button>
        </div>

        <div class="space-y-2">
          <Label>选择结果</Label>
          <div class="rounded-md bg-muted p-3">
            <pre class="text-sm">{{ selectedDateTime3 || '未选择' }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 表单集成测试 -->
    <Card>
      <CardHeader>
        <CardTitle>表单集成测试</CardTitle>
        <CardDescription>在实际表单中的使用效果</CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <form class="space-y-4" @submit.prevent>
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div class="space-y-2">
              <Label>开始时间 *</Label>
              <DateTimePicker
                v-model="selectedDateTime"
                placeholder="请选择开始时间"
                :required="true"
              />
            </div>

            <div class="space-y-2">
              <Label>结束时间</Label>
              <DateTimePicker
                v-model="selectedDateTime2"
                placeholder="请选择结束时间"
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label>问题发生时间 *</Label>
            <DateTimePicker
              v-model="selectedDateTime3"
              placeholder="请选择问题发生的时间"
              :required="true"
            />
          </div>

          <div class="flex gap-2">
            <Button type="submit" :disabled="!selectedDateTime || !selectedDateTime3">
              提交表单
            </Button>
            <Button type="button" variant="outline" @click="clearAllValues">
              清空所有
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 功能说明 -->
    <Card>
      <CardHeader>
        <CardTitle>组件特性说明</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <h4 class="font-medium">
            主要特性：
          </h4>
          <ul class="list-disc list-inside text-sm text-muted-foreground space-y-1">
            <li>基于 @vuepic/vue-datepicker 构建，功能强大且稳定</li>
            <li>自定义 shadcn-ui 主题样式，完美融入项目设计</li>
            <li>支持明暗主题自动切换</li>
            <li>支持日期和时间同时选择</li>
            <li>内置清空和快捷操作功能</li>
            <li>兼容 datetime-local 格式输出</li>
            <li>支持必填和禁用状态</li>
            <li>响应式设计，移动端友好</li>
            <li>高性能，支持大量数据和复杂交互</li>
            <li>完全可定制的触发器样式</li>
          </ul>
        </div>

        <div class="space-y-2">
          <h4 class="font-medium">
            使用方法：
          </h4>
          <div class="rounded-md bg-muted p-3">
            <pre class="text-sm"><code>&lt;DateTimePicker
  v-model="dateTime"
  placeholder="请选择日期和时间"
  :required="true"
  :disabled="false"
/&gt;</code></pre>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
