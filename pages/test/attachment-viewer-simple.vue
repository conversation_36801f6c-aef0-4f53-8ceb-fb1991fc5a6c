<script setup lang="ts">
import { ref } from 'vue'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import type { AttachmentItem } from '@/components/ui/attachment-viewer'

// 页面标题
definePageMeta({
  title: '附件查看器 - 简单示例'
})

// 简单的附件数据
const simpleAttachments = ref<AttachmentItem[]>([
  {
    id: '1',
    name: '产品截图.png',
    url: 'https://picsum.photos/800/600?random=1',
    type: 'png',
    size: 245760,
    thumbnailUrl: 'https://picsum.photos/200/200?random=1'
  },
  {
    id: '2',
    name: '用户界面.jpg',
    url: 'https://picsum.photos/800/600?random=2',
    type: 'jpg',
    size: 512000,
    thumbnailUrl: 'https://picsum.photos/200/200?random=2'
  },
  {
    id: '3',
    name: '功能演示.mp4',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    type: 'mp4',
    size: 15728640
  }
])

// 处理下载事件
const handleDownload = (attachment: AttachmentItem) => {
  console.log('下载附件:', attachment.name)
  // 实际项目中这里可以添加下载统计、权限检查等逻辑
}
</script>

<template>
  <div class="container mx-auto py-8 space-y-6">
    <!-- 页面标题 -->
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">附件查看器 - 简单示例</h1>
      <p class="text-muted-foreground">
        展示 AttachmentViewer 组件的基本使用方法
      </p>
    </div>

    <!-- 基本使用示例 -->
    <Card>
      <CardHeader>
        <CardTitle>基本使用</CardTitle>
        <CardDescription>
          最简单的使用方式，包含图片和视频附件
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          :attachments="simpleAttachments"
          @download="handleDownload"
        />
      </CardContent>
    </Card>

    <!-- 代码示例 -->
    <Card>
      <CardHeader>
        <CardTitle>代码示例</CardTitle>
        <CardDescription>
          如何在你的项目中使用这个组件
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div>
            <h4 class="font-medium mb-2">1. 导入组件</h4>
            <pre class="bg-muted p-3 rounded-md text-sm overflow-x-auto"><code>import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import type { AttachmentItem } from '@/components/ui/attachment-viewer'</code></pre>
          </div>
          
          <div>
            <h4 class="font-medium mb-2">2. 准备数据</h4>
            <pre class="bg-muted p-3 rounded-md text-sm overflow-x-auto"><code>const attachments = ref&lt;AttachmentItem[]&gt;([
  {
    id: '1',
    name: '截图.png',
    url: 'https://example.com/image.png',
    type: 'png',
    size: 245760
  }
])</code></pre>
          </div>
          
          <div>
            <h4 class="font-medium mb-2">3. 使用组件</h4>
            <pre class="bg-muted p-3 rounded-md text-sm overflow-x-auto"><code>&lt;AttachmentViewer
  :attachments="attachments"
  @download="handleDownload"
/&gt;</code></pre>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 功能特性 -->
    <Card>
      <CardHeader>
        <CardTitle>主要功能</CardTitle>
        <CardDescription>
          AttachmentViewer 组件提供的核心功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <h4 class="font-medium">支持的文件格式</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 图片：PNG, JPEG, JPG</li>
              <li>• 视频：MP4, RMVB, MOV</li>
            </ul>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium">交互功能</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 点击预览大图/视频</li>
              <li>• 键盘导航（←→ 切换，ESC 关闭）</li>
              <li>• 文件下载</li>
              <li>• 响应式布局</li>
            </ul>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium">显示特性</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 缩略图网格展示</li>
              <li>• 文件类型和大小统计</li>
              <li>• 自动文件大小格式化</li>
              <li>• 支持大量附件的"更多"按钮</li>
            </ul>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium">配置选项</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 三种缩略图尺寸（sm/md/lg）</li>
              <li>• 可控制显示数量</li>
              <li>• 可隐藏文件名</li>
              <li>• 可禁用下载功能</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 使用提示 -->
    <Card>
      <CardHeader>
        <CardTitle>使用提示</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-3 text-sm">
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>数据格式：</strong>确保附件数据符合 AttachmentItem 接口定义，特别是 id、name、url、type 字段是必需的。
            </div>
          </div>
          
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>文件大小：</strong>size 字段应该是数字类型（字节数），组件会自动格式化显示。
            </div>
          </div>
          
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>缩略图：</strong>如果提供了 thumbnailUrl，图片会使用缩略图；否则会直接使用原图。
            </div>
          </div>
          
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>下载处理：</strong>组件提供默认的下载行为，你也可以通过 @download 事件自定义下载逻辑。
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
