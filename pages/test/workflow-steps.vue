<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import TicketWorkflowSteps from '~/components/feedback/TicketWorkflowSteps.vue'

// 示例工单数据
const sampleTickets = {
  created: {
    id: 'T001',
    title: '新创建的工单',
    stage: '已创建',
    creator: '张三',
    feedbackPerson: '张三',
    createdAt: '2024-01-15 09:00:00',
    enterTime: '2024-01-15 09:00:00',
    severityLevel: '高',
    firstLevelCategory: '系统问题',
    secondLevelCategory: '功能异常',
    description: '用户反馈系统功能异常，无法正常使用',
  } as Ticket,

  pending: {
    id: 'T002',
    title: '待处理工单',
    stage: '待处理',
    creator: '李四',
    feedbackPerson: '李四',
    handler: '王五',
    createdAt: '2024-01-15 10:00:00',
    enterTime: '2024-01-15 10:00:00',
    severityLevel: '中',
    firstLevelCategory: '咨询问题',
    description: '用户咨询系统使用方法',
    estimatedTime: '2小时',
  } as Ticket,

  processing: {
    id: 'T003',
    title: '处理中工单',
    stage: '处理中',
    creator: '王五',
    feedbackPerson: '王五',
    handler: '赵六',
    devProcessor: '赵六',
    createdAt: '2024-01-15 11:00:00',
    enterTime: '2024-01-15 11:00:00',
    responseTime: '2024-01-15 14:00:00',
    updatedAt: '2024-01-15 14:00:00',
    severityLevel: '高',
    firstLevelCategory: '系统问题',
    secondLevelCategory: '性能问题',
    description: '系统响应缓慢，用户反馈页面加载时间过长',
    progress: '正在分析性能瓶颈',
  } as Ticket,

  processed: {
    id: 'T004',
    title: '已处理工单',
    stage: '已处理',
    creator: '钱七',
    feedbackPerson: '钱七',
    handler: '孙八',
    devProcessor: '孙八',
    createdAt: '2024-01-15 12:00:00',
    enterTime: '2024-01-15 12:00:00',
    responseTime: '2024-01-15 13:00:00',
    endTime: '2024-01-15 16:00:00',
    severityLevel: '中',
    firstLevelCategory: '功能问题',
    secondLevelCategory: '登录问题',
    description: '用户无法正常登录系统',
    cause: '数据库连接池配置错误，导致连接超时',
    result: '已修复数据库连接池配置，增加连接数量并优化超时设置，系统恢复正常',
    solution: '更新了数据库配置文件，重启了应用服务',
  } as Ticket,

  archived: {
    id: 'T005',
    title: '已归档工单',
    stage: '已归档',
    creator: '周九',
    feedbackPerson: '周九',
    handler: '吴十',
    devProcessor: '吴十',
    createdAt: '2024-01-15 13:00:00',
    enterTime: '2024-01-15 13:00:00',
    responseTime: '2024-01-15 14:00:00',
    endTime: '2024-01-15 17:00:00',
    severityLevel: '低',
    firstLevelCategory: '网络问题',
    description: '网络连接不稳定',
    cause: '网络设备配置错误',
    result: '已修复网络配置，更新了路由器设置',
    feedback: '问题已解决，网络连接恢复正常',
    satisfaction: '满意',
    resolution: '技术问题已解决',
  } as Ticket,

  rejected: {
    id: 'T006',
    title: '已驳回工单',
    stage: '已驳回',
    creator: '郑十一',
    feedbackPerson: '郑十一',
    handler: '王十二',
    createdAt: '2024-01-15 14:00:00',
    enterTime: '2024-01-15 14:00:00',
    updatedAt: '2024-01-15 15:00:00',
    severityLevel: '低',
    description: '重复提交的问题',
  } as Ticket,
}

// 状态标题映射
function getStatusTitle(key: string) {
  const titles = {
    created: '创建状态',
    pending: '认领状态',
    processing: '处理状态',
    processed: '已处理状态（待归档）',
    archived: '归档状态',
    rejected: '已驳回状态',
  }
  return titles[key as keyof typeof titles] || key
}

// 页面元数据
useHead({
  title: '工单流程图优化展示',
  meta: [
    { name: 'description', content: '展示优化后的紧凑工单流程图组件' },
  ],
})
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="text-center">
      <h1 class="mb-2 text-3xl font-bold">
        工单流程组件优化展示
      </h1>
      <p class="text-muted-foreground">
        展示优化后的紧凑设计、主题色统一、相关图标的工单流程组件
      </p>
    </div>

    <!-- 水平布局示例 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">
        水平布局 (紧凑设计，突出当前环节)
      </h2>
      <div class="rounded-lg bg-card p-6">
        <TicketWorkflowSteps
          :ticket="sampleTickets.processing"
          orientation="horizontal"
          :show-timestamps="true"
          :show-operators="true"
        />
      </div>
    </div>

    <!-- 垂直布局示例 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">
        垂直布局 (当前环节背景突出)
      </h2>
      <div class="rounded-lg bg-card p-6">
        <TicketWorkflowSteps
          :ticket="sampleTickets.processing"
          orientation="vertical"
          :show-timestamps="true"
          :show-operators="true"
        />
      </div>
    </div>

    <!-- 不同状态对比 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">
        不同状态对比 (相关图标展示)
      </h2>
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div v-for="(ticket, key) in sampleTickets" :key="key" class="rounded-lg bg-card p-4">
          <h3 class="mb-3 text-sm text-card-foreground font-medium">
            {{ getStatusTitle(key) }}
          </h3>
          <TicketWorkflowSteps
            :ticket="ticket"
            orientation="vertical"
            :show-timestamps="false"
            :show-operators="false"
          />
        </div>
      </div>
    </div>

    <!-- 优化说明 -->
    <div class="rounded-lg bg-card p-6">
      <h2 class="mb-4 text-xl font-semibold">
        优化说明
      </h2>
      <div class="text-sm space-y-3">
        <div class="flex items-start gap-2">
          <div class="mt-2 h-2 w-2 shrink-0 rounded-full bg-primary" />
          <div>
            <strong>紧凑设计：</strong>减少了步骤间距和内边距，使流程显示更紧凑，当前环节通过缩放和阴影效果突出显示。
          </div>
        </div>
        <div class="flex items-start gap-2">
          <div class="mt-2 h-2 w-2 shrink-0 rounded-full bg-primary" />
          <div>
            <strong>主题色统一：</strong>使用shadcn-ui的主题色系统，包括primary、destructive、muted等语义化颜色。
          </div>
        </div>
        <div class="flex items-start gap-2">
          <div class="mt-2 h-2 w-2 shrink-0 rounded-full bg-primary" />
          <div>
            <strong>相关图标：</strong>为每个流程环节选择了更合适的图标：
            <ul class="ml-4 mt-1 space-y-1">
              <li>• 创建：文档图标 (FileText)</li>
              <li>• 认领：手势图标 (Hand)</li>
              <li>• 处理：播放图标 (Play)</li>
              <li>• 归档：已完成图标 (CheckCircle2)</li>
              <li>• 已完成：保持原图标 + 右上角勾选标记</li>
              <li>• 已驳回：警告图标 (AlertCircle)</li>
            </ul>
          </div>
        </div>
        <div class="flex items-start gap-2">
          <div class="mt-2 h-2 w-2 shrink-0 rounded-full bg-primary" />
          <div>
            <strong>交互优化：</strong>添加了平滑的过渡动画，当前环节有视觉反馈，仅显示相关步骤的时间戳和操作人信息。
          </div>
        </div>
        <div class="flex items-start gap-2">
          <div class="mt-2 h-2 w-2 shrink-0 rounded-full bg-primary" />
          <div>
            <strong>状态标识：</strong>右上角标记清晰区分状态 - 已完成显示勾选标记，进行中显示脉动圆点，提升状态识别度。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
