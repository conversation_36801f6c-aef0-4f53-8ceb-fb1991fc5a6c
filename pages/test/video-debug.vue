<script setup lang="ts">
import type { AttachmentItem } from '@/components/ui/attachment-viewer'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ref } from 'vue'
import { checkMediaUrl, debugMediaUrl, formatMediaUrl } from '~/utils/mediaUrl'

definePageMeta({
  title: '视频调试测试',
  layout: 'default',
})

// 测试用的附件数据
const testAttachments = ref<AttachmentItem[]>([
  {
    id: '1',
    name: '测试图片.jpg',
    url: 'https://picsum.photos/800/600?random=1',
    type: 'jpg',
    size: 245760,
  },
  {
    id: '2',
    name: '在线视频测试.mp4',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    type: 'mp4',
    size: 15728640,
  },
  {
    id: '3',
    name: '本地视频测试.mp4',
    url: '/test-video.mp4', // 假设的本地视频
    type: 'mp4',
    size: 5242880,
  },
  {
    id: '4',
    name: '无效视频链接.mp4',
    url: 'https://invalid-url.com/video.mp4',
    type: 'mp4',
    size: 1024000,
  },
  {
    id: '5',
    name: '相对路径视频.mov',
    url: './assets/test.mov', // 相对路径测试
    type: 'mov',
    size: 2048000,
  },
  {
    id: '6',
    name: 'API代理视频.mp4',
    url: '/api/feedback/files/test-video.mp4', // API代理路径测试
    type: 'mp4',
    size: 3145728,
  },
  {
    id: '7',
    name: '上传文件示例.mp4',
    url: 'feedback/upload/1234567890_abcdef.mp4', // 模拟上传后的URL
    type: 'mp4',
    size: 4194304,
  },
  {
    id: '8',
    name: '华为云OBS测试.mp4',
    url: 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4', // 华为云OBS URL
    type: 'mp4',
    size: 5242880,
  },
])

// 单个视频测试
const singleVideoAttachment = ref<AttachmentItem[]>([
  {
    id: 'single',
    name: '单独视频测试.mp4',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    type: 'mp4',
    size: 25165824,
  },
])

// 处理下载事件
function handleDownload(attachment: AttachmentItem) {
  console.log('下载附件:', attachment)
}

// 添加测试视频
function addTestVideo() {
  const newVideo: AttachmentItem = {
    id: `test-${Date.now()}`,
    name: `动态添加视频-${Date.now()}.mp4`,
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    type: 'mp4',
    size: Math.floor(Math.random() * 10000000),
  }
  testAttachments.value.push(newVideo)
}

// 清空附件
function clearAttachments() {
  testAttachments.value = []
}

// 重置为默认附件
function resetAttachments() {
  testAttachments.value = [
    {
      id: '1',
      name: '测试图片.jpg',
      url: 'https://picsum.photos/800/600?random=1',
      type: 'jpg',
      size: 245760,
    },
    {
      id: '2',
      name: '在线视频测试.mp4',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      type: 'mp4',
      size: 15728640,
    },
  ]
}

// URL测试功能
const testUrl = ref('')
const urlTestResult = ref<any>(null)

async function testMediaUrl() {
  if (!testUrl.value)
    return

  console.log('测试URL:', testUrl.value)

  // 调试URL信息
  const debugInfo = debugMediaUrl(testUrl.value)

  // 检查URL可访问性
  const accessibilityResult = await checkMediaUrl(testUrl.value)

  urlTestResult.value = {
    debugInfo,
    accessibilityResult,
    formattedUrl: formatMediaUrl(testUrl.value),
  }

  console.log('URL测试结果:', urlTestResult.value)
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold">
        视频调试测试页面
      </h1>
      <p class="mt-2 text-muted-foreground">
        用于测试和调试视频加载问题的专用页面
      </p>
    </div>

    <!-- 控制按钮 -->
    <Card>
      <CardHeader>
        <CardTitle>测试控制</CardTitle>
        <CardDescription>
          使用这些按钮来测试不同的场景
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex flex-wrap gap-2">
          <Button @click="addTestVideo">
            添加测试视频
          </Button>
          <Button variant="outline" @click="clearAttachments">
            清空附件
          </Button>
          <Button variant="secondary" @click="resetAttachments">
            重置为默认
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 多个附件测试 -->
    <Card>
      <CardHeader>
        <CardTitle>多附件测试 ({{ testAttachments.length }} 个附件)</CardTitle>
        <CardDescription>
          包含图片和视频的混合附件列表，用于测试各种加载情况
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          v-if="testAttachments.length > 0"
          :attachments="testAttachments"
          :max-thumbnails="8"
          thumbnail-size="lg"
          :show-file-name="true"
          :allow-download="true"
          @download="handleDownload"
        />
        <div v-else class="py-8 text-center text-muted-foreground">
          暂无附件，点击上方按钮添加测试附件
        </div>
      </CardContent>
    </Card>

    <!-- 单个视频测试 -->
    <Card>
      <CardHeader>
        <CardTitle>单个视频测试</CardTitle>
        <CardDescription>
          专门用于测试单个视频文件的加载和播放
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          :attachments="singleVideoAttachment"
          :max-thumbnails="1"
          thumbnail-size="lg"
          :show-file-name="true"
          :allow-download="true"
          @download="handleDownload"
        />
      </CardContent>
    </Card>

    <!-- URL测试工具 -->
    <Card>
      <CardHeader>
        <CardTitle>URL测试工具</CardTitle>
        <CardDescription>
          测试特定URL的格式化和可访问性
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="flex gap-2">
            <div class="flex-1">
              <Label for="test-url">测试URL</Label>
              <Input
                id="test-url"
                v-model="testUrl"
                placeholder="输入要测试的视频或图片URL"
                class="mt-1"
              />
            </div>
            <div class="flex items-end">
              <Button @click="testMediaUrl">
                测试URL
              </Button>
            </div>
          </div>

          <div v-if="urlTestResult" class="mt-4 rounded-lg bg-muted p-4">
            <h4 class="mb-2 font-medium">
              测试结果:
            </h4>
            <div class="text-sm space-y-2">
              <div><strong>原始URL:</strong> {{ urlTestResult.debugInfo.original }}</div>
              <div><strong>格式化URL:</strong> {{ urlTestResult.debugInfo.formatted }}</div>
              <div><strong>需要代理:</strong> {{ urlTestResult.debugInfo.needsProxy ? '是' : '否' }}</div>
              <div><strong>是否绝对路径:</strong> {{ urlTestResult.debugInfo.isAbsolute ? '是' : '否' }}</div>
              <div><strong>是否API路径:</strong> {{ urlTestResult.debugInfo.isApiPath ? '是' : '否' }}</div>
              <div><strong>使用代理:</strong> {{ urlTestResult.debugInfo.isUsingProxy ? '是' : '否' }}</div>
              <div><strong>华为云OBS:</strong> {{ urlTestResult.debugInfo.isObsUrl ? '是' : '否' }}</div>
              <div><strong>文件扩展名:</strong> {{ urlTestResult.debugInfo.extension || '无' }}</div>
              <div><strong>MIME类型:</strong> {{ urlTestResult.debugInfo.mimeType }}</div>
              <div><strong>浏览器支持:</strong> {{ urlTestResult.debugInfo.browserSupport ? '是' : '否' }}</div>
              <div><strong>URL可访问:</strong> {{ urlTestResult.accessibilityResult.accessible ? '是' : '否' }}</div>
              <div v-if="urlTestResult.accessibilityResult.status">
                <strong>HTTP状态:</strong> {{ urlTestResult.accessibilityResult.status }}
              </div>
              <div v-if="urlTestResult.accessibilityResult.error" class="text-destructive">
                <strong>错误:</strong> {{ urlTestResult.accessibilityResult.error }}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 调试信息 -->
    <Card>
      <CardHeader>
        <CardTitle>调试信息</CardTitle>
        <CardDescription>
          查看浏览器控制台获取详细的加载日志
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="text-sm space-y-2">
          <div><strong>当前附件数量:</strong> {{ testAttachments.length }}</div>
          <div><strong>浏览器:</strong> {{ navigator.userAgent }}</div>
          <div><strong>支持的视频格式:</strong></div>
          <ul class="ml-4 list-disc list-inside space-y-1">
            <li>MP4: {{ document.createElement('video').canPlayType('video/mp4') || '不支持' }}</li>
            <li>WebM: {{ document.createElement('video').canPlayType('video/webm') || '不支持' }}</li>
            <li>OGG: {{ document.createElement('video').canPlayType('video/ogg') || '不支持' }}</li>
          </ul>
          <div class="mt-4 rounded-lg bg-muted p-3">
            <strong>使用说明:</strong>
            <ul class="mt-2 list-disc list-inside space-y-1">
              <li>点击附件缩略图打开预览模式</li>
              <li>在预览模式中观察视频加载状态</li>
              <li>查看浏览器控制台的详细日志</li>
              <li>测试不同的视频URL和格式</li>
              <li>如果视频一直加载，查看错误信息</li>
              <li>使用URL测试工具检查特定URL的问题</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
