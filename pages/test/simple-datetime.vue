<script setup lang="ts">
import DateTimePicker from '@/components/ui/date-time-picker/DateTimePicker.vue'
import VueDatePicker from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'

const dateTime = ref('')
const rawDate = ref(new Date())

function handleChange(value: string) {
  console.log('DateTime changed:', value)
}

function handleRawChange(value: Date) {
  console.log('Raw date changed:', value)
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        简单日期时间选择器测试
      </h1>
      <p class="text-muted-foreground">
        测试新的 VueDatePicker 组件
      </p>
    </div>

    <Card>
      <CardHeader>
        <CardTitle>原始 VueDatePicker 测试</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <Label>原始组件</Label>
          <VueDatePicker
            v-model="rawDate"
            :enable-time-picker="true"
            @update:model-value="handleRawChange"
          />
        </div>

        <div class="space-y-2">
          <Label>原始值</Label>
          <div class="rounded-md bg-muted p-3">
            <code>{{ rawDate }}</code>
          </div>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>自定义组件测试</CardTitle>
        <CardDescription>
          支持键盘输入、快捷选项和美化样式的日期时间选择器
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid gap-4 md:grid-cols-2">
          <div class="space-y-2">
            <Label>基础选择器</Label>
            <DateTimePicker
              v-model="dateTime"
              placeholder="请选择日期和时间"
              @update:model-value="handleChange"
            />
            <p class="text-xs text-muted-foreground">
              支持键盘输入，格式：YYYY-MM-DD HH:mm
            </p>
          </div>

          <div class="space-y-2">
            <Label>禁用状态</Label>
            <DateTimePicker
              model-value="2024-12-20T14:30"
              placeholder="禁用状态"
              disabled
            />
            <p class="text-xs text-muted-foreground">
              禁用状态下不可编辑
            </p>
          </div>
        </div>

        <div class="space-y-2">
          <Label>当前值</Label>
          <div class="rounded-md bg-muted p-3">
            <code>{{ dateTime || '(空)' }}</code>
          </div>
        </div>

        <div class="space-y-2">
          <Label>功能特性</Label>
          <div class="grid gap-2 text-sm">
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>支持键盘直接输入日期时间</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>内置快捷选项：此刻、1小时后、明天此时、下周此时</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>美化的日历界面，支持明暗主题</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>平滑的动画效果和交互反馈</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>完全兼容 shadcn-ui 设计系统</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
