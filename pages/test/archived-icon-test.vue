<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import { Archive, CheckCircle, CheckCircle2 } from 'lucide-vue-next'

// 测试已归档状态的工单
const archivedTicket: Ticket = {
  id: 'test-archived',
  ticketID: 'TK-ARCHIVED-001',
  title: '已归档工单测试',
  text: '这是一个已归档状态的测试工单',
  problemDescription: '测试已归档状态的图标显示',
  status: '已归档',
  stage: '已归档',
  creator: '测试用户',
  feedbackPerson: '测试用户',
  handler: '处理人员',
  devProcessor: '处理人员',
  createdAt: '2024-01-01 10:00:00',
  enterTime: '2024-01-01 10:00:00',
  responseTime: '2024-01-01 11:00:00',
  endTime: '2024-01-01 15:00:00',
  severityLevel: '中',
  firstLevelCategory: '功能问题',
  secondLevelCategory: '界面问题',
  cause: '测试问题原因',
  result: '测试处理结果',
  changeLogs: [
    {
      time: '2024-01-01 10:00:00',
      content: '创建了工单',
      operator: '测试用户',
    },
    {
      time: '2024-01-01 15:00:00',
      content: '归档了工单',
      operator: '处理人员',
    },
  ],
}

// 页面元数据
useHead({
  title: '已归档图标测试',
  meta: [
    { name: 'description', content: '测试已归档状态的图标显示' },
  ],
})
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-4">
      <h1 class="text-2xl font-bold">
        已归档图标测试页面
      </h1>
      <p class="text-muted-foreground">
        测试已归档状态工单的图标显示效果
      </p>
    </div>

    <!-- 工单详情组件测试 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">
        工单详情组件
      </h2>
      <div class="h-[600px] border rounded-lg">
        <TicketDetail :ticket="archivedTicket" />
      </div>
    </div>

    <!-- 工单流程步骤测试 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">
        工单流程步骤
      </h2>
      <div class="border rounded-lg p-4">
        <TicketWorkflowSteps
          :ticket="archivedTicket"
          orientation="horizontal"
          :show-progress="true"
          :show-timestamps="true"
          :show-operators="true"
        />
      </div>
    </div>

    <!-- 状态标签测试 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">
        状态标签测试
      </h2>
      <div class="flex items-center gap-4">
        <div class="text-sm text-muted-foreground">
          原始状态标签：
        </div>
        <div
          class="flex items-center gap-1.5 whitespace-nowrap rounded-md bg-[rgb(100,100,105)]/10 px-2 py-1 text-xs text-[rgb(100,100,105)] font-medium"
        >
          <div class="size-2 shrink-0 rounded-full bg-[rgb(100,100,105)]" />
          已归档
        </div>
      </div>

      <div class="flex items-center gap-4">
        <div class="text-sm text-muted-foreground">
          新的状态标签：
        </div>
        <div
          class="flex items-center gap-1.5 whitespace-nowrap rounded-md bg-[rgb(100,100,105)]/10 px-2 py-1 text-xs text-[rgb(100,100,105)] font-medium"
        >
          <CheckCircle2 class="size-3 text-[rgb(100,100,105)]" />
          已归档
        </div>
      </div>
    </div>

    <!-- 图标对比测试 -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold">
        图标对比
      </h2>
      <div class="grid grid-cols-3 gap-4">
        <div class="border rounded-lg p-4 text-center space-y-2">
          <Archive class="mx-auto size-8 text-[rgb(100,100,105)]" />
          <div class="text-sm">
            Archive (旧)
          </div>
        </div>
        <div class="border rounded-lg p-4 text-center space-y-2">
          <CheckCircle2 class="mx-auto size-8 text-[rgb(100,100,105)]" />
          <div class="text-sm">
            CheckCircle2 (新)
          </div>
        </div>
        <div class="border rounded-lg p-4 text-center space-y-2">
          <CheckCircle class="mx-auto size-8 text-[rgb(100,100,105)]" />
          <div class="text-sm">
            CheckCircle (对比)
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
