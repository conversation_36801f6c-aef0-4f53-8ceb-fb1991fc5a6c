<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import { ref } from 'vue'
import { useModal } from '~/composables/useModal'
import { useTicketOperations } from '~/composables/useTicketOperations'

// 页面标题
definePageMeta({
  title: '工单操作测试（更新版）',
})

const modal = useModal()
const {
  rejectTicketAction,
  assignTicketAction,
  completeTicketAction,
} = useTicketOperations()

// 模拟工单数据
const mockTicket = ref<Ticket>({
  id: 'TEST-001',
  ticketID: 'TEST-001',
  problemDescription: '测试工单操作功能',
  status: '待处理',
  stage: '待处理',
  feedbackPerson: '测试用户',
  handler: '',
  createdAt: '2024-01-15 10:00:00',
  enterTime: '2024-01-15 10:00:00',
  severityLevel: '中',
  apps: ['测试应用'],
  appVersion: ['1.0.0'],
  osType: ['Windows'],
  mobileType: ['PC'],
  firstLevelCategory: '功能问题',
  secondLevelCategory: '界面显示',
  attachments: [],
  changeLogs: [],
})

// 测试驳回工单（不需要原因）
async function testRejectTicket() {
  console.log('🔄 测试驳回工单（不需要原因）')
  
  const success = await rejectTicketAction(mockTicket.value, '')
  
  if (success) {
    console.log('✅ 驳回操作成功')
    mockTicket.value.status = '已驳回'
    mockTicket.value.stage = '已驳回'
  } else {
    console.log('❌ 驳回操作失败')
  }
}

// 测试指派工单（不需要原因）
async function testAssignTicket() {
  console.log('🔄 测试指派工单（不需要原因）')
  
  await modal.showModal({
    title: '指派工单',
    type: 'custom',
    component: 'AssignTicketForm',
    props: {
      ticket: mockTicket.value,
      onAssign: async (data: { assignee: string }) => {
        console.log('指派数据:', data)
        
        const success = await assignTicketAction(
          mockTicket.value,
          data.assignee,
        )
        
        if (success) {
          console.log('✅ 指派操作成功')
          mockTicket.value.handler = data.assignee
          mockTicket.value.status = '处理中'
          mockTicket.value.stage = '处理中'
        } else {
          console.log('❌ 指派操作失败')
        }
      },
    },
    persistent: true,
    closable: true,
  })
}

// 测试完成工单（需要原因和结果）
async function testCompleteTicket() {
  console.log('🔄 测试完成工单（需要原因和结果）')
  
  await modal.showModal({
    title: '完成工单',
    type: 'custom',
    component: 'CompleteTicketForm',
    props: {
      ticket: mockTicket.value,
      onComplete: async (data: { cause: string, result: string }) => {
        console.log('完成数据:', data)
        
        const success = await completeTicketAction(
          mockTicket.value,
          data.result,
          data.cause,
        )
        
        if (success) {
          console.log('✅ 完成操作成功')
          mockTicket.value.status = '已处理'
          mockTicket.value.stage = '已处理'
          mockTicket.value.cause = data.cause
          mockTicket.value.result = data.result
        } else {
          console.log('❌ 完成操作失败')
        }
      },
    },
    persistent: true,
    closable: true,
  })
}

// 重置工单状态
function resetTicket() {
  mockTicket.value = {
    ...mockTicket.value,
    status: '待处理',
    stage: '待处理',
    handler: '',
    cause: '',
    result: '',
  }
  console.log('🔄 工单状态已重置')
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold">工单操作测试（更新版）</h1>
      <Button @click="resetTicket" variant="outline">
        重置工单状态
      </Button>
    </div>

    <!-- 当前工单状态 -->
    <Card>
      <CardHeader>
        <CardTitle>当前工单状态</CardTitle>
      </CardHeader>
      <CardContent class="space-y-2">
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div><strong>工单ID:</strong> {{ mockTicket.ticketID }}</div>
          <div><strong>状态:</strong> 
            <Badge :variant="mockTicket.status === '待处理' ? 'destructive' : 
                           mockTicket.status === '处理中' ? 'default' : 
                           mockTicket.status === '已处理' ? 'secondary' : 'outline'">
              {{ mockTicket.status }}
            </Badge>
          </div>
          <div><strong>处理人:</strong> {{ mockTicket.handler || '未分配' }}</div>
          <div><strong>严重程度:</strong> {{ mockTicket.severityLevel }}</div>
        </div>
        <div v-if="mockTicket.cause" class="mt-4">
          <strong>问题原因:</strong>
          <div class="mt-1 p-2 bg-muted rounded text-sm">{{ mockTicket.cause }}</div>
        </div>
        <div v-if="mockTicket.result" class="mt-4">
          <strong>处理结果:</strong>
          <div class="mt-1 p-2 bg-muted rounded text-sm">{{ mockTicket.result }}</div>
        </div>
      </CardContent>
    </Card>

    <!-- 操作按钮 -->
    <Card>
      <CardHeader>
        <CardTitle>操作测试</CardTitle>
        <CardDescription>
          测试更新后的工单操作逻辑：
          <ul class="mt-2 space-y-1 text-sm">
            <li>• 驳回：不需要填写驳回原因</li>
            <li>• 指派：不需要填写指派原因</li>
            <li>• 完成：需要填写问题原因和处理结果</li>
          </ul>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex flex-wrap gap-4">
          <Button 
            @click="testRejectTicket" 
            variant="destructive"
            :disabled="mockTicket.status !== '待处理' && mockTicket.status !== '处理中'"
          >
            驳回工单（无需原因）
          </Button>
          
          <Button 
            @click="testAssignTicket" 
            variant="default"
            :disabled="mockTicket.status !== '待处理' && mockTicket.status !== '处理中'"
          >
            指派工单（无需原因）
          </Button>
          
          <Button 
            @click="testCompleteTicket" 
            variant="secondary"
            :disabled="mockTicket.status !== '处理中'"
          >
            完成工单（需要原因+结果）
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 说明 -->
    <Alert>
      <AlertDescription>
        <strong>测试说明：</strong>
        <br>
        1. 点击"驳回工单"按钮，会直接确认驳回，不需要填写原因
        <br>
        2. 点击"指派工单"按钮，只需要选择指派人，不需要填写指派原因
        <br>
        3. 点击"完成工单"按钮，必须填写问题原因和处理结果才能提交
        <br>
        4. 查看浏览器控制台可以看到详细的操作日志
      </AlertDescription>
    </Alert>
  </div>
</template>
