<script setup lang="ts">
import { ref } from 'vue'
import { AttachmentViewer } from '@/components/ui/attachment-viewer'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import type { AttachmentItem } from '@/components/ui/attachment-viewer'

// 页面标题
definePageMeta({
  title: '附件缩放测试'
})

// 测试不同尺寸的附件
const testAttachments = ref<AttachmentItem[]>([
  {
    id: '1',
    name: '超大图片 (4K).jpg',
    url: 'https://picsum.photos/3840/2160?random=1',
    type: 'jpg',
    size: 2048000,
    thumbnailUrl: 'https://picsum.photos/200/200?random=1'
  },
  {
    id: '2',
    name: '超宽图片.png',
    url: 'https://picsum.photos/2560/1080?random=2',
    type: 'png',
    size: 1536000,
    thumbnailUrl: 'https://picsum.photos/200/200?random=2'
  },
  {
    id: '3',
    name: '超高图片.jpeg',
    url: 'https://picsum.photos/1080/2560?random=3',
    type: 'jpeg',
    size: 1024000,
    thumbnailUrl: 'https://picsum.photos/200/200?random=3'
  },
  {
    id: '4',
    name: '正方形大图.jpg',
    url: 'https://picsum.photos/2048/2048?random=4',
    type: 'jpg',
    size: 3072000,
    thumbnailUrl: 'https://picsum.photos/200/200?random=4'
  },
  {
    id: '5',
    name: '高清视频.mp4',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    type: 'mp4',
    size: 15728640
  },
  {
    id: '6',
    name: '小尺寸图片.png',
    url: 'https://picsum.photos/400/300?random=5',
    type: 'png',
    size: 102400,
    thumbnailUrl: 'https://picsum.photos/200/200?random=5'
  }
])

// 处理下载事件
const handleDownload = (attachment: AttachmentItem) => {
  console.log('下载附件:', attachment.name)
}
</script>

<template>
  <div class="container mx-auto py-8 space-y-6">
    <!-- 页面标题 -->
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">附件缩放测试</h1>
      <p class="text-muted-foreground">
        测试不同尺寸附件的自适应缩放功能
      </p>
    </div>

    <!-- 测试说明 -->
    <Card>
      <CardHeader>
        <CardTitle>测试说明</CardTitle>
        <CardDescription>
          这个页面包含了各种尺寸的图片和视频，用于测试预览弹窗的自适应缩放功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <h4 class="font-medium">测试内容</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 4K 超大图片 (3840×2160)</li>
              <li>• 超宽图片 (2560×1080)</li>
              <li>• 超高图片 (1080×2560)</li>
              <li>• 正方形大图 (2048×2048)</li>
              <li>• 高清视频文件</li>
              <li>• 小尺寸图片 (400×300)</li>
            </ul>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium">缩放特性</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 保持原始宽高比</li>
              <li>• 自适应屏幕尺寸</li>
              <li>• 移动端优化</li>
              <li>• 平滑过渡动画</li>
              <li>• 加载状态显示</li>
              <li>• 错误处理机制</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 附件展示 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          测试附件
          <Badge variant="secondary">{{ testAttachments.length }} 个文件</Badge>
        </CardTitle>
        <CardDescription>
          点击任意附件查看预览效果，注意观察不同尺寸文件的缩放表现
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          :attachments="testAttachments"
          :max-thumbnails="8"
          thumbnail-size="lg"
          :show-file-name="true"
          :allow-download="true"
          @download="handleDownload"
        />
      </CardContent>
    </Card>

    <!-- 技术细节 -->
    <Card>
      <CardHeader>
        <CardTitle>技术实现</CardTitle>
        <CardDescription>
          缩放功能的技术实现细节
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div>
            <h4 class="font-medium mb-2">响应式尺寸计算</h4>
            <pre class="bg-muted p-3 rounded-md text-sm overflow-x-auto"><code>// 根据屏幕尺寸动态调整
const maxDisplaySize = computed(() => {
  const vw = viewportSize.value.width
  const vh = viewportSize.value.height
  
  // 移动端适配
  if (vw < 640) {
    widthRatio = 0.95
    heightRatio = 0.85
  } else if (vw < 768) {
    widthRatio = 0.90
    heightRatio = 0.80
  }
  
  return {
    width: Math.max(vw * widthRatio - padding * 2, 300),
    height: Math.max(vh * heightRatio - headerHeight, 200)
  }
})</code></pre>
          </div>
          
          <div>
            <h4 class="font-medium mb-2">CSS 样式控制</h4>
            <pre class="bg-muted p-3 rounded-md text-sm overflow-x-auto"><code>:style="{
  maxWidth: `${maxDisplaySize.width}px`,
  maxHeight: `${maxDisplaySize.height}px`,
  width: 'auto',
  height: 'auto'
}"</code></pre>
          </div>
          
          <div>
            <h4 class="font-medium mb-2">关键特性</h4>
            <div class="grid md:grid-cols-2 gap-4 text-sm">
              <div class="space-y-1">
                <div class="font-medium">图片处理</div>
                <ul class="text-muted-foreground space-y-1">
                  <li>• object-contain 保持比例</li>
                  <li>• 动态最大尺寸限制</li>
                  <li>• 加载状态管理</li>
                  <li>• 错误重试机制</li>
                </ul>
              </div>
              
              <div class="space-y-1">
                <div class="font-medium">视频处理</div>
                <ul class="text-muted-foreground space-y-1">
                  <li>• 原生 video 元素</li>
                  <li>• 预加载元数据</li>
                  <li>• 播放控制器</li>
                  <li>• 格式兼容性检查</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 使用提示 -->
    <Card>
      <CardHeader>
        <CardTitle>使用提示</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-3 text-sm">
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>键盘导航：</strong>使用左右箭头键切换附件，ESC 键关闭预览。
            </div>
          </div>
          
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>移动端优化：</strong>在移动设备上，弹窗会占用更多屏幕空间以提供更好的查看体验。
            </div>
          </div>
          
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>加载优化：</strong>大文件会显示加载状态，失败时提供重试选项。
            </div>
          </div>
          
          <div class="flex items-start gap-2">
            <div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
            <div>
              <strong>性能考虑：</strong>组件会根据屏幕尺寸动态调整最大显示尺寸，避免内存占用过大。
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
