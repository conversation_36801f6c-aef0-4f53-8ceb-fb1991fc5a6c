<script setup lang="ts">
import type { AttachmentItem } from '~/components/ui/attachment-viewer'
import { AttachmentUploaderSimple } from '~/components/ui/attachment-uploader'
import { AttachmentViewer } from '~/components/ui/attachment-viewer'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'

// 页面标题
useHead({
  title: '优化附件组件测试',
})

// 上传文件状态
interface UploadingFile {
  uid: string
  file: File
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

// 附件数据
const attachments = ref<AttachmentItem[]>([])
const uploadingFiles = ref<UploadingFile[]>([])

// 处理附件更新
function handleAttachmentsUpdate(newAttachments: AttachmentItem[]) {
  attachments.value = newAttachments
}

// 处理上传成功
function handleUploadSuccess(attachment: AttachmentItem) {
  console.log('上传成功:', attachment)
}

// 处理上传失败
function handleUploadError(error: string) {
  console.error('上传失败:', error)
}

// 处理附件删除
function handleAttachmentDelete(attachment: AttachmentItem) {
  attachments.value = attachments.value.filter(item => item.id !== attachment.id)
}

// 处理删除上传中的文件
function handleDeleteUploadingFile(file: File) {
  uploadingFiles.value = uploadingFiles.value.filter(f => f.file !== file)
}

// 处理重试上传
function handleRetryUpload(file: File) {
  // 这个方法会被 AttachmentUploaderSimple 组件处理
}
</script>

<template>
  <div class="mx-auto py-8 container space-y-8">
    <div class="text-center space-y-2">
      <h1 class="text-3xl font-bold">
        优化附件组件测试
      </h1>
      <p class="text-muted-foreground">
        测试优化后的附件上传和预览功能
      </p>
    </div>

    <!-- 简化的上传组件测试 -->
    <Card>
      <CardHeader>
        <CardTitle>简化的附件上传器</CardTitle>
        <CardDescription>
          只保留上传区域，上传进度集成在缩略图中显示
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentUploaderSimple
          v-model="attachments"
          :uploading-files="uploadingFiles"
          :max-files="10"
          :max-size="100"
          :accept="['png', 'jpeg', 'jpg', 'mp4', 'rmvb', 'mov']"
          :multiple="true"
          @update:model-value="handleAttachmentsUpdate"
          @update:uploading-files="(files) => uploadingFiles = files"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
        />
      </CardContent>
    </Card>

    <!-- 集成上传进度的附件查看器测试 -->
    <Card v-if="attachments.length > 0 || uploadingFiles.length > 0">
      <CardHeader>
        <CardTitle>附件预览器（集成上传进度）</CardTitle>
        <CardDescription>
          显示已上传和上传中的文件，上传进度直接在缩略图中显示
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AttachmentViewer
          :attachments="attachments"
          :uploading-files="uploadingFiles"
          thumbnail-size="md"
          :show-file-name="true"
          :allow-download="false"
          :allow-delete="true"
          :allow-retry="true"
          @delete="handleAttachmentDelete"
          @delete-uploading="handleDeleteUploadingFile"
          @retry-upload="handleRetryUpload"
        />
      </CardContent>
    </Card>

    <!-- 统计信息 -->
    <Card v-if="attachments.length > 0">
      <CardHeader>
        <CardTitle>附件统计</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
          <div class="border rounded-lg p-4 text-center">
            <div class="text-2xl text-primary font-bold">
              {{ attachments.length + uploadingFiles.length }}
            </div>
            <div class="text-sm text-muted-foreground">
              总文件数
            </div>
          </div>
          <div class="border rounded-lg p-4 text-center">
            <div class="text-2xl text-green-600 font-bold">
              {{ attachments.length }}
            </div>
            <div class="text-sm text-muted-foreground">
              已上传
            </div>
          </div>
          <div class="border rounded-lg p-4 text-center">
            <div class="text-2xl text-blue-600 font-bold">
              {{ uploadingFiles.filter(f => f.status === 'uploading').length }}
            </div>
            <div class="text-sm text-muted-foreground">
              上传中
            </div>
          </div>
          <div class="border rounded-lg p-4 text-center">
            <div class="text-2xl text-red-600 font-bold">
              {{ uploadingFiles.filter(f => f.status === 'error').length }}
            </div>
            <div class="text-sm text-muted-foreground">
              上传失败
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 附件详情 -->
    <Card v-if="attachments.length > 0">
      <CardHeader>
        <CardTitle>附件详情</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2">
          <div
            v-for="attachment in attachments"
            :key="attachment.id"
            class="flex items-center justify-between border rounded-lg p-3"
          >
            <div class="flex items-center gap-3">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <div>
                <div class="font-medium">
                  {{ attachment.name }}
                </div>
                <div class="text-sm text-muted-foreground">
                  {{ attachment.type.toUpperCase() }}
                  <span v-if="attachment.size">
                    · {{ Math.round(attachment.size / 1024) }} KB
                  </span>
                </div>
              </div>
            </div>
            <div class="text-xs text-muted-foreground">
              ID: {{ attachment.id }}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
