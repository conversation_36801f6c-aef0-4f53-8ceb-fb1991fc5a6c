<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import AssignUserModal from '@/components/feedback/AssignUserModal.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, UserPlus } from 'lucide-vue-next'
import { ref } from 'vue'
import { useModal } from '~/composables/useModal'

// 页面配置
definePageMeta({
  title: '指派工单测试',
  layout: 'default',
})

const modal = useModal()

// 模拟工单数据
const mockTicket = ref<Ticket>({
  id: 'TEST-001',
  ticketID: 'TEST-001',
  problemDescription: '测试工单 - 用户登录问题',
  status: '待处理',
  stage: '待处理',
  feedbackPerson: '张三',
  handler: '',
  createdAt: '2024-01-15 10:30:00',
  enterTime: '2024-01-15 10:30:00',
  severityLevel: '中',
  apps: ['移动端APP'],
  appVersion: ['v2.1.0'],
  osType: ['iOS'],
  mobileType: ['iPhone 14'],
})

// 指派工单弹窗状态
const showAssignModal = ref(false)

// 指派工单函数
async function handleAssignTicket() {
  showAssignModal.value = true
}

// 处理指派确认
async function handleAssignConfirm(data: { assignee: any, reason?: string }) {
  console.log('指派工单数据:', data)
  console.log('指派参数格式:', JSON.stringify(data.assignee, null, 2))

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新工单信息（模拟刷新效果）
    mockTicket.value.handler = data.assignee.option?.user_name || data.assignee.label
    mockTicket.value.status = '处理中'
    mockTicket.value.stage = '处理中'

    // 关闭弹窗
    showAssignModal.value = false

    // 显示成功提示
    await modal.success('指派成功！工单信息已刷新', '操作完成')

    console.log('✅ 指派操作完成，工单信息已更新')
  }
  catch (error) {
    console.error('指派失败:', error)
    await modal.error('指派失败，请重试', '操作失败')
  }
}

// 完成工单函数
async function handleCompleteTicket() {
  await modal.showModal({
    title: '完成工单',
    type: 'custom',
    component: 'CompleteTicketForm',
    props: {
      ticket: mockTicket.value,
      onComplete: async (data: { result: string, solution?: string }) => {
        console.log('完成工单数据:', data)

        try {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 1000))

          // 更新工单信息（模拟刷新效果）
          mockTicket.value.status = '已处理'
          mockTicket.value.stage = '已处理'
          mockTicket.value.result = data.result
          if (data.solution) {
            mockTicket.value.solution = data.solution
          }

          // 显示成功提示
          await modal.success('工单完成！工单信息已刷新', '操作完成')

          console.log('✅ 完成操作完成，工单信息已更新')
        }
        catch (error) {
          console.error('完成工单失败:', error)
          await modal.error('完成工单失败，请重试', '操作失败')
        }
      },
    },
    persistent: true,
    closable: true,
  })
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        指派工单功能测试
      </h1>
      <p class="text-muted-foreground">
        测试使用 useModal 和 UserSelect 组件的指派工单功能
      </p>
    </div>

    <!-- 模拟工单卡片 -->
    <Card>
      <CardHeader>
        <div class="flex items-center justify-between">
          <div class="space-y-1">
            <CardTitle class="flex items-center gap-2">
              <Badge
                :variant="mockTicket.status === '待处理' ? 'destructive' : mockTicket.status === '处理中' ? 'default' : 'secondary'"
              >
                {{ mockTicket.status }}
              </Badge>
              {{ mockTicket.problemDescription }}
            </CardTitle>
            <CardDescription>
              工单ID: {{ mockTicket.ticketID }} | 创建人: {{ mockTicket.feedbackPerson }} | 创建时间: {{ mockTicket.createdAt }}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="font-medium">处理人:</span>
            <span class="ml-2">{{ mockTicket.handler || '未指派' }}</span>
          </div>
          <div>
            <span class="font-medium">严重程度:</span>
            <span class="ml-2">{{ mockTicket.severityLevel }}</span>
          </div>
          <div>
            <span class="font-medium">应用:</span>
            <span class="ml-2">{{ mockTicket.apps?.join(', ') }}</span>
          </div>
          <div>
            <span class="font-medium">版本:</span>
            <span class="ml-2">{{ mockTicket.appVersion?.join(', ') }}</span>
          </div>
        </div>

        <div v-if="mockTicket.result" class="mt-4 rounded-lg bg-muted/50 p-3">
          <div class="mb-1 text-sm text-muted-foreground font-medium">
            处理结果
          </div>
          <div class="text-sm">
            {{ mockTicket.result }}
          </div>
          <div v-if="mockTicket.solution" class="mt-2">
            <div class="mb-1 text-sm text-muted-foreground font-medium">
              解决方案
            </div>
            <div class="text-sm">
              {{ mockTicket.solution }}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter class="flex gap-2">
        <Button
          v-if="!mockTicket.handler"
          class="flex items-center gap-2"
          @click="handleAssignTicket"
        >
          <UserPlus class="h-4 w-4" />
          指派工单
        </Button>
        <Button
          v-if="mockTicket.handler && mockTicket.status !== '已处理'"
          variant="outline"
          class="flex items-center gap-2"
          @click="handleCompleteTicket"
        >
          <CheckCircle class="h-4 w-4" />
          完成工单
        </Button>
        <Button
          v-if="mockTicket.status === '已处理'"
          disabled
          variant="secondary"
        >
          工单已完成
        </Button>
      </CardFooter>
    </Card>

    <!-- 功能说明 -->
    <Card>
      <CardHeader>
        <CardTitle>功能说明</CardTitle>
        <CardDescription>
          测试指派工单和完成工单的弹窗功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div>
            <h4 class="mb-2 text-green-600 font-semibold">
              ✅ 指派工单功能
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 使用 UserSelect 组件选择指派人员</li>
              <li>• 支持搜索用户（中文名、拼音、工号）</li>
              <li>• 可填写指派原因（可选）</li>
              <li>• 统一的弹窗样式和交互</li>
            </ul>
          </div>

          <div>
            <h4 class="mb-2 text-blue-600 font-semibold">
              ✅ 完成工单功能
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 必填处理结果字段</li>
              <li>• 可选解决方案字段</li>
              <li>• 表单验证和错误提示</li>
              <li>• 取消时不会执行操作</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>

  <!-- 指派工单弹窗 -->
  <AssignUserModal
    v-model:open="showAssignModal"
    :ticket-title="mockTicket.problemDescription"
    @confirm="handleAssignConfirm"
    @cancel="showAssignModal = false"
  />
</template>
