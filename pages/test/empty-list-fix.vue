<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Layout from '~/components/feedback/Layout.vue'

// 模拟工单数据
const mockTickets = [
  {
    id: '1',
    ticketID: 'T001',
    title: '测试工单1',
    text: '这是一个测试工单的描述',
    status: '待处理',
    stage: '待处理',
    feedbackPerson: '张三',
    enterTime: '2024-01-01 10:00:00',
    labels: ['功能问题', '紧急'],
  },
  {
    id: '2',
    ticketID: 'T002',
    title: '测试工单2',
    text: '这是另一个测试工单的描述',
    status: '处理中',
    stage: '处理中',
    feedbackPerson: '李四',
    enterTime: '2024-01-02 14:30:00',
    labels: ['界面问题'],
  },
  {
    id: '3',
    ticketID: 'T003',
    title: '测试工单3',
    text: '这是第三个测试工单的描述',
    status: '已处理',
    stage: '已处理',
    feedbackPerson: '王五',
    enterTime: '2024-01-03 09:15:00',
    labels: ['性能问题'],
  },
]

// 当前工单列表状态
const currentTickets = ref([...mockTickets])
const loading = ref(false)
const error = ref(null)
const activeMenu = ref('all')

// 测试函数
function loadTickets() {
  loading.value = true
  setTimeout(() => {
    currentTickets.value = [...mockTickets]
    loading.value = false
  }, 1000)
}

function clearTickets() {
  loading.value = true
  setTimeout(() => {
    currentTickets.value = []
    loading.value = false
  }, 500)
}

function loadPartialTickets() {
  loading.value = true
  setTimeout(() => {
    currentTickets.value = [mockTickets[0]]
    loading.value = false
  }, 500)
}

function simulateError() {
  loading.value = true
  setTimeout(() => {
    currentTickets.value = []
    error.value = '模拟网络错误'
    loading.value = false
  }, 500)
}

function clearError() {
  error.value = null
}

// 事件处理
function handleRefreshTickets() {
  console.log('刷新工单列表')
}

function handleTicketCreated() {
  console.log('工单创建成功')
}

function handleTicketActionRefresh() {
  console.log('工单操作刷新')
}

function handleRefreshCurrentTicket() {
  console.log('刷新当前工单')
}

definePageMeta({
  title: '空列表修复测试',
})
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">🔧 空工单列表修复测试</h1>
        <p class="mt-2 text-muted-foreground">
          测试当工单列表为空时，工单详情页是否正确清空之前的数据
        </p>
      </div>

      <!-- 测试控制面板 -->
      <Card>
        <CardHeader>
          <CardTitle>测试控制</CardTitle>
          <CardDescription>
            模拟不同的工单列表状态，观察详情页的行为
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="flex flex-wrap gap-4">
            <Button @click="loadTickets">
              加载完整工单列表 (3个工单)
            </Button>
            <Button @click="loadPartialTickets">
              加载部分工单 (1个工单)
            </Button>
            <Button @click="clearTickets" variant="destructive">
              清空工单列表
            </Button>
            <Button @click="simulateError" variant="outline">
              模拟加载错误
            </Button>
            <Button v-if="error" @click="clearError" variant="secondary">
              清除错误状态
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 当前状态显示 -->
      <Card>
        <CardHeader>
          <CardTitle>当前状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid gap-4 md:grid-cols-3">
            <div>
              <h4 class="font-medium mb-2">工单列表状态</h4>
              <p class="text-sm text-muted-foreground">
                数量: {{ currentTickets.length }}
              </p>
              <p class="text-sm text-muted-foreground">
                加载中: {{ loading ? '是' : '否' }}
              </p>
              <p class="text-sm text-muted-foreground">
                错误: {{ error || '无' }}
              </p>
            </div>
            <div>
              <h4 class="font-medium mb-2">工单列表</h4>
              <div class="text-xs text-muted-foreground space-y-1">
                <div v-if="currentTickets.length === 0" class="text-orange-600">
                  列表为空
                </div>
                <div v-for="ticket in currentTickets" :key="ticket.id">
                  {{ ticket.ticketID }}: {{ ticket.title }}
                </div>
              </div>
            </div>
            <div>
              <h4 class="font-medium mb-2">预期行为</h4>
              <div class="text-xs text-muted-foreground space-y-1">
                <div v-if="currentTickets.length === 0">
                  ✅ 详情页应该显示空状态，不显示之前选中的工单数据
                </div>
                <div v-else>
                  ✅ 详情页应该显示第一个工单的详情
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 工单布局组件 -->
      <Card>
        <CardHeader>
          <CardTitle>工单布局测试</CardTitle>
          <CardDescription>
            观察右侧详情页在不同列表状态下的表现
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="h-[600px] border rounded-lg">
            <Layout
              :tickets="currentTickets"
              :loading="loading"
              :error="error"
              :nav-collapsed-size="4"
              :active-menu="activeMenu"
              @update:active-menu="activeMenu = $event"
              @ticket-created="handleTicketCreated"
              @refresh-tickets="handleRefreshTickets"
              @ticket-action-refresh="handleTicketActionRefresh"
              @refresh-current-ticket="handleRefreshCurrentTicket"
            />
          </div>
        </CardContent>
      </Card>

      <!-- 测试说明 -->
      <Card>
        <CardHeader>
          <CardTitle>📋 测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4 text-sm">
            <div>
              <h4 class="font-medium mb-2">问题描述</h4>
              <p class="text-muted-foreground">
                之前的实现中，当工单列表变为空时（比如筛选后无结果、网络错误等），
                工单详情页仍然显示之前选中的工单数据，这会让用户感到困惑。
              </p>
            </div>
            <div>
              <h4 class="font-medium mb-2">修复方案</h4>
              <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                <li>在 <code>useTicketSelection</code> 中添加监听器，当工单列表为空时自动清除选中状态</li>
                <li>优化 <code>selectedTicketData</code> 计算逻辑，确保列表为空时返回 <code>undefined</code></li>
                <li>修改 Layout 组件中的自动选择逻辑，避免在空列表时执行选择操作</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium mb-2">测试步骤</h4>
              <ol class="list-decimal list-inside space-y-1 text-muted-foreground">
                <li>点击"加载完整工单列表"，观察详情页显示第一个工单</li>
                <li>点击"清空工单列表"，观察详情页是否清空显示</li>
                <li>重新加载工单，确认功能正常</li>
                <li>测试部分加载和错误状态</li>
              </ol>
            </div>
            <div>
              <h4 class="font-medium mb-2">预期结果</h4>
              <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                <li>✅ 工单列表为空时，详情页显示空状态或加载状态</li>
                <li>✅ 工单列表有数据时，详情页显示第一个工单的详情</li>
                <li>✅ 状态切换流畅，无残留数据</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
