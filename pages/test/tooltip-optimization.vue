<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Archive, CheckCircle, Hand, Trash2, UserPlus, X, Zap } from 'lucide-vue-next'
import { useTicketOperations } from '~/composables/useTicketOperations'

// 模拟不同的工单状态和用户权限
const mockTickets = [
  {
    id: '1',
    ticketID: 'T001',
    title: '待处理工单',
    stage: '待处理',
    feedbackPerson: '张三',
    handler: '',
    result: '',
    cause: '',
  },
  {
    id: '2',
    ticketID: 'T002',
    title: '处理中工单',
    stage: '处理中',
    feedbackPerson: '李四',
    handler: '王五',
    result: '',
    cause: '',
  },
  {
    id: '3',
    ticketID: 'T003',
    title: '已处理工单',
    stage: '已处理',
    feedbackPerson: '赵六',
    handler: '王五',
    result: '问题已修复',
    cause: '代码逻辑错误',
  },
  {
    id: '4',
    ticketID: 'T004',
    title: '已归档工单',
    stage: '已归档',
    feedbackPerson: '孙七',
    handler: '王五',
    result: '问题已修复',
    cause: '配置错误',
  },
]

const selectedTicket = ref(mockTickets[0])
const currentUser = ref('王五') // 模拟当前用户
const userRole = ref('User') // 模拟用户角色

// 使用工单操作管理
const { getActionTooltip, checkPermission, checkTicketStatus } = useTicketOperations()

// 操作列表
const actions = [
  { key: 'handle', label: '认领', icon: Hand },
  { key: 'assign', label: '指派', icon: UserPlus },
  { key: 'complete', label: '完成', icon: CheckCircle },
  { key: 'reject', label: '驳回', icon: X },
  { key: 'check', label: '归档', icon: Archive },
  { key: 'urge', label: '加急', icon: Zap },
  { key: 'delete', label: '删除', icon: Trash2 },
]

// 切换工单
function selectTicket(ticket: any) {
  selectedTicket.value = ticket
}

// 切换用户角色
function switchRole(role: string) {
  userRole.value = role
}

// 切换当前用户
function switchUser(user: string) {
  currentUser.value = user
}

definePageMeta({
  title: 'Tooltip 优化测试',
})
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">🔧 工单操作提示优化测试</h1>
        <p class="mt-2 text-muted-foreground">
          测试优化后的工单操作提示，区分权限限制和流程限制
        </p>
      </div>

      <!-- 测试控制面板 -->
      <div class="grid gap-4 md:grid-cols-3">
        <!-- 工单选择 -->
        <Card>
          <CardHeader>
            <CardTitle>选择工单</CardTitle>
            <CardDescription>切换不同状态的工单</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <Button
                v-for="ticket in mockTickets"
                :key="ticket.id"
                :variant="selectedTicket.id === ticket.id ? 'default' : 'outline'"
                class="w-full justify-start"
                @click="selectTicket(ticket)"
              >
                {{ ticket.title }} ({{ ticket.stage }})
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- 用户角色切换 -->
        <Card>
          <CardHeader>
            <CardTitle>用户角色</CardTitle>
            <CardDescription>切换不同的用户权限</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <Button
                v-for="role in ['User', 'Admin', 'Super']"
                :key="role"
                :variant="userRole === role ? 'default' : 'outline'"
                class="w-full"
                @click="switchRole(role)"
              >
                {{ role === 'User' ? '普通用户' : role === 'Admin' ? '管理员' : '超级管理员' }}
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- 当前用户切换 -->
        <Card>
          <CardHeader>
            <CardTitle>当前用户</CardTitle>
            <CardDescription>切换不同的用户身份</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <Button
                v-for="user in ['王五', '张三', '李四', '赵六']"
                :key="user"
                :variant="currentUser === user ? 'default' : 'outline'"
                class="w-full"
                @click="switchUser(user)"
              >
                {{ user }}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 当前状态显示 -->
      <Card>
        <CardHeader>
          <CardTitle>当前测试状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid gap-4 md:grid-cols-3">
            <div>
              <h4 class="font-medium mb-2">选中工单</h4>
              <p class="text-sm text-muted-foreground">
                {{ selectedTicket.title }} ({{ selectedTicket.stage }})
              </p>
              <p class="text-xs text-muted-foreground mt-1">
                创建人: {{ selectedTicket.feedbackPerson }}
              </p>
              <p class="text-xs text-muted-foreground">
                处理人: {{ selectedTicket.handler || '未分配' }}
              </p>
            </div>
            <div>
              <h4 class="font-medium mb-2">当前用户</h4>
              <p class="text-sm text-muted-foreground">{{ currentUser }}</p>
              <p class="text-xs text-muted-foreground mt-1">
                角色: {{ userRole === 'User' ? '普通用户' : userRole === 'Admin' ? '管理员' : '超级管理员' }}
              </p>
            </div>
            <div>
              <h4 class="font-medium mb-2">权限关系</h4>
              <p class="text-xs text-muted-foreground">
                是否为创建人: {{ selectedTicket.feedbackPerson === currentUser ? '是' : '否' }}
              </p>
              <p class="text-xs text-muted-foreground">
                是否为处理人: {{ selectedTicket.handler === currentUser ? '是' : '否' }}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 操作按钮测试 -->
      <Card>
        <CardHeader>
          <CardTitle>工单操作按钮</CardTitle>
          <CardDescription>
            悬停查看详细的权限和流程提示信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TooltipProvider>
            <div class="flex flex-wrap gap-4">
              <Tooltip v-for="action in actions" :key="action.key">
                <TooltipTrigger as-child>
                  <span class="inline-block">
                    <Button
                      variant="outline"
                      size="sm"
                      :disabled="!checkPermission(action.key, selectedTicket) || !checkTicketStatus(action.key, selectedTicket)"
                      class="flex items-center gap-2"
                    >
                      <component :is="action.icon" class="size-4" />
                      {{ action.label }}
                    </Button>
                  </span>
                </TooltipTrigger>
                <TooltipContent class="max-w-xs">
                  {{ getActionTooltip(action.key, selectedTicket) }}
                </TooltipContent>
              </Tooltip>
            </div>
          </TooltipProvider>
        </CardContent>
      </Card>

      <!-- 提示信息说明 -->
      <Card>
        <CardHeader>
          <CardTitle>📋 提示信息优化说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4 text-sm">
            <div>
              <h4 class="font-medium mb-2 text-green-600">✅ 可执行操作</h4>
              <p class="text-muted-foreground">显示操作的具体说明，如"认领工单"、"指派工单给其他人处理"等</p>
            </div>
            <div>
              <h4 class="font-medium mb-2 text-red-600">🚫 权限限制</h4>
              <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                <li>显示具体的权限要求，如"只有工单处理人或管理员可以完成工单"</li>
                <li>显示当前用户权限与所需权限的对比</li>
                <li>针对特殊操作显示详细的权限说明</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium mb-2 text-orange-600">⚠️ 流程限制</h4>
              <ul class="list-disc list-inside space-y-1 text-muted-foreground">
                <li>显示当前状态与允许状态的对比，如"当前状态'待处理'不允许此操作，允许的状态：处理中"</li>
                <li>显示前置条件要求，如"完成工单前需要先指派处理人"</li>
                <li>显示工单流程的具体要求</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
