<script setup lang="ts">
import type { TicketApiTypes } from '~/api/feedback/ticket'
import AssignUserModal from '@/components/feedback/AssignUserModal.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { UserSelect } from '@/components/ui/user-select'

// 页面配置
definePageMeta({
  title: '人员选择组件实际应用示例',
  layout: 'default',
})

// 响应式数据
const selectedHandler = ref<string>('')
const selectedReviewers = ref<string[]>([])
const showAssignModal = ref(false)
const showMultiAssignModal = ref(false)

// 模拟工单数据
const mockTicket = {
  id: 'TICKET-001',
  title: '登录页面无法正常显示验证码',
  status: '待处理',
  creator: '张三',
  createTime: '2024-01-15 10:30:00',
}

// 事件处理
function handleHandlerChange(value: string, user: TicketApiTypes.UserInfo) {
  console.log('处理人变更:', { value, user })
}

function handleReviewersChange(value: string[], users: TicketApiTypes.UserInfo[]) {
  console.log('审核人变更:', { value, users })
}

function handleAssignConfirm(data: { assignee: any, reason?: string }) {
  console.log('单人指派确认:', data)
  console.log('指派参数格式:', JSON.stringify(data.assignee, null, 2))
  showAssignModal.value = false
  // 这里可以调用实际的指派API
}

function handleMultiAssignConfirm(data: { assignee: any, reason?: string }) {
  console.log('多人指派确认:', data)
  console.log('指派参数格式:', JSON.stringify(data.assignee, null, 2))
  showMultiAssignModal.value = false
  // 这里可以调用实际的指派API
}

function openAssignModal() {
  showAssignModal.value = true
}

function openMultiAssignModal() {
  showMultiAssignModal.value = true
}

function clearSelections() {
  selectedHandler.value = ''
  selectedReviewers.value = []
}
</script>

<template>
  <div class="mx-auto p-6 container space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        人员选择组件实际应用示例
      </h1>
      <p class="text-muted-foreground">
        展示 UserSelect 组件在工单管理系统中的实际应用场景
      </p>
    </div>

    <!-- 模拟工单信息 -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Badge variant="outline">
            {{ mockTicket.status }}
          </Badge>
          {{ mockTicket.title }}
        </CardTitle>
        <CardDescription>
          工单ID: {{ mockTicket.id }} | 创建人: {{ mockTicket.creator }} | 创建时间: {{ mockTicket.createTime }}
        </CardDescription>
      </CardHeader>
    </Card>

    <div class="grid gap-6 md:grid-cols-2">
      <!-- 工单处理场景 -->
      <Card>
        <CardHeader>
          <CardTitle>工单处理场景</CardTitle>
          <CardDescription>
            为工单指定处理人和审核人
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <label class="text-sm font-medium">主要处理人</label>
            <UserSelect
              v-model="selectedHandler"
              placeholder="选择主要处理人"
              @change="handleHandlerChange"
            />
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium">审核人员（多选）</label>
            <UserSelect
              v-model="selectedReviewers"
              multiple
              placeholder="选择审核人员"
              :max-tag-count="2"
              @change="handleReviewersChange"
            />
          </div>

          <div class="space-y-2">
            <label class="text-sm font-medium">当前选择</label>
            <div class="text-sm text-muted-foreground space-y-1">
              <div>处理人: {{ selectedHandler || '未选择' }}</div>
              <div>审核人 ({{ selectedReviewers.length }}): {{ selectedReviewers.length > 0 ? selectedReviewers.join(', ') : '未选择' }}</div>
            </div>
          </div>

          <div class="flex gap-2">
            <Button size="sm" variant="outline" @click="clearSelections">
              清空选择
            </Button>
            <Button size="sm" :disabled="!selectedHandler">
              保存设置
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 指派对话框场景 -->
      <Card>
        <CardHeader>
          <CardTitle>指派对话框场景</CardTitle>
          <CardDescription>
            使用模态对话框进行工单指派操作
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <p class="text-sm text-muted-foreground">
              点击按钮打开指派对话框，体验完整的指派流程
            </p>
          </div>

          <div class="flex gap-2">
            <Button @click="openAssignModal">
              指派给单人
            </Button>
            <Button variant="outline" @click="openMultiAssignModal">
              指派给多人
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <Separator />

    <!-- 组件特性说明 -->
    <Card>
      <CardHeader>
        <CardTitle>组件特性说明</CardTitle>
        <CardDescription>
          UserSelect 组件的主要特性和优势
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-6 md:grid-cols-3">
          <div class="space-y-2">
            <h4 class="text-green-600 font-semibold">
              🔍 智能搜索
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 支持中文名搜索</li>
              <li>• 支持拼音搜索</li>
              <li>• 支持工号搜索</li>
              <li>• 300ms 防抖优化</li>
            </ul>
          </div>

          <div class="space-y-2">
            <h4 class="text-blue-600 font-semibold">
              🎯 灵活配置
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 单选/多选模式</li>
              <li>• 自定义占位符</li>
              <li>• 标签数量限制</li>
              <li>• 禁用状态支持</li>
            </ul>
          </div>

          <div class="space-y-2">
            <h4 class="text-purple-600 font-semibold">
              🎨 用户体验
            </h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• shadcn-ui 设计风格</li>
              <li>• 响应式布局</li>
              <li>• 无障碍支持</li>
              <li>• 清除功能</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 指派对话框 -->
    <AssignUserModal
      v-model:open="showAssignModal"
      :ticket-title="mockTicket.title"
      @confirm="handleAssignConfirm"
      @cancel="showAssignModal = false"
    />

    <AssignUserModal
      v-model:open="showMultiAssignModal"
      :ticket-title="mockTicket.title"
      multiple
      @confirm="handleMultiAssignConfirm"
      @cancel="showMultiAssignModal = false"
    />
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
