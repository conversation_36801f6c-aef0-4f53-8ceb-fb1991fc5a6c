<script setup lang="ts">
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger, EnhancedTooltip } from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import { useTooltipManager } from '~/composables/useTooltipManager'

const { resetTooltips, fixTooltipState, checkTooltipHealth } = useTooltipManager()

const testResults = ref({
  healthCheck: false,
  resetTest: false,
  fixTest: false,
})

// 执行健康检查
const runHealthCheck = () => {
  testResults.value.healthCheck = checkTooltipHealth()
}

// 测试重置功能
const testReset = async () => {
  try {
    await resetTooltips()
    testResults.value.resetTest = true
    setTimeout(() => {
      testResults.value.resetTest = false
    }, 2000)
  } catch (error) {
    console.error('Reset test failed:', error)
  }
}

// 测试修复功能
const testFix = async () => {
  try {
    await fixTooltipState()
    testResults.value.fixTest = true
    setTimeout(() => {
      testResults.value.fixTest = false
    }, 2000)
  } catch (error) {
    console.error('Fix test failed:', error)
  }
}

// 使用全局修复函数
const useGlobalFix = () => {
  if (window.$fixTooltips) {
    window.$fixTooltips()
  } else {
    console.warn('Global tooltip fix function not available')
  }
}

onMounted(() => {
  runHealthCheck()
})
</script>

<template>
  <div class="container mx-auto p-6 space-y-6">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">Tooltip 修复测试</h1>
      <p class="text-muted-foreground">
        测试tooltip失效问题的修复方案
      </p>
    </div>

    <!-- 控制面板 -->
    <Card>
      <CardHeader>
        <CardTitle>🔧 Tooltip 修复控制面板</CardTitle>
        <CardDescription>
          使用以下按钮来测试和修复tooltip问题
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex flex-wrap gap-4">
          <Button @click="runHealthCheck" variant="outline">
            健康检查
            <Badge v-if="testResults.healthCheck" variant="default" class="ml-2">✓</Badge>
          </Button>
          
          <Button @click="testReset" variant="outline">
            重置Tooltips
            <Badge v-if="testResults.resetTest" variant="default" class="ml-2">✓</Badge>
          </Button>
          
          <Button @click="testFix" variant="outline">
            修复状态
            <Badge v-if="testResults.fixTest" variant="default" class="ml-2">✓</Badge>
          </Button>
          
          <Button @click="useGlobalFix" variant="outline">
            全局修复
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 标准Tooltip测试 -->
    <Card>
      <CardHeader>
        <CardTitle>📋 标准 Tooltip 测试</CardTitle>
        <CardDescription>
          测试标准的radix-vue tooltip组件
        </CardDescription>
      </CardHeader>
      <CardContent>
        <TooltipProvider>
          <div class="flex flex-wrap gap-4">
            <Tooltip>
              <TooltipTrigger as-child>
                <Button variant="outline">悬停我 (基础)</Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>这是一个基础的tooltip</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger as-child>
                <Button variant="outline">悬停我 (长文本)</Button>
              </TooltipTrigger>
              <TooltipContent class="max-w-xs">
                <p>这是一个包含很长文本的tooltip，用来测试在不同情况下tooltip是否能正常显示和隐藏。</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger as-child>
                <Button variant="outline" disabled>禁用按钮</Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>即使按钮被禁用，tooltip也应该正常工作</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </TooltipProvider>
      </CardContent>
    </Card>

    <!-- 增强Tooltip测试 -->
    <Card>
      <CardHeader>
        <CardTitle>⚡ 增强 Tooltip 测试</CardTitle>
        <CardDescription>
          测试带有自动修复功能的增强tooltip组件
        </CardDescription>
      </CardHeader>
      <CardContent>
        <TooltipProvider>
          <div class="flex flex-wrap gap-4">
            <EnhancedTooltip content="这是增强版tooltip，具有自动修复功能">
              <Button variant="outline">增强版 (自动修复)</Button>
            </EnhancedTooltip>

            <EnhancedTooltip 
              content="这个tooltip在右侧显示，并且有更长的延迟时间" 
              side="right"
              :delay-duration="500"
            >
              <Button variant="outline">右侧显示</Button>
            </EnhancedTooltip>

            <EnhancedTooltip 
              content="这是一个被禁用的tooltip" 
              :disabled="true"
            >
              <Button variant="outline">禁用的tooltip</Button>
            </EnhancedTooltip>

            <EnhancedTooltip 
              content="这个tooltip有自定义的最大宽度限制，可以显示更多的文本内容而不会过宽"
              max-width="200px"
            >
              <Button variant="outline">自定义宽度</Button>
            </EnhancedTooltip>
          </div>
        </TooltipProvider>
      </CardContent>
    </Card>

    <!-- 压力测试 -->
    <Card>
      <CardHeader>
        <CardTitle>🚀 压力测试</CardTitle>
        <CardDescription>
          大量tooltip同时存在的情况下的性能测试
        </CardDescription>
      </CardHeader>
      <CardContent>
        <TooltipProvider>
          <div class="grid grid-cols-4 gap-2">
            <EnhancedTooltip 
              v-for="i in 20" 
              :key="i" 
              :content="`这是第 ${i} 个tooltip`"
            >
              <Button variant="outline" size="sm">
                按钮 {{ i }}
              </Button>
            </EnhancedTooltip>
          </div>
        </TooltipProvider>
      </CardContent>
    </Card>

    <!-- 使用说明 -->
    <Card>
      <CardHeader>
        <CardTitle>📖 使用说明</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4 text-sm">
          <div>
            <h4 class="font-medium mb-2">🔧 修复方案</h4>
            <ul class="list-disc list-inside space-y-1 text-muted-foreground">
              <li>自动检测和修复tooltip状态</li>
              <li>监听页面可见性变化和路由变化</li>
              <li>清理孤立的tooltip DOM元素</li>
              <li>提供手动修复功能</li>
            </ul>
          </div>
          <div>
            <h4 class="font-medium mb-2">⚡ 增强功能</h4>
            <ul class="list-disc list-inside space-y-1 text-muted-foreground">
              <li>EnhancedTooltip组件提供更好的稳定性</li>
              <li>自动修复功能可以防止tooltip卡住</li>
              <li>更好的z-index管理避免被遮挡</li>
              <li>防抖机制减少不必要的DOM操作</li>
            </ul>
          </div>
          <div>
            <h4 class="font-medium mb-2">🎯 最佳实践</h4>
            <ul class="list-disc list-inside space-y-1 text-muted-foreground">
              <li>在应用根部使用单个TooltipProvider</li>
              <li>对于复杂场景使用EnhancedTooltip</li>
              <li>定期调用健康检查确保tooltip正常工作</li>
              <li>在路由变化后手动触发修复</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
