<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import TicketFilterToolbar from '~/components/feedback/filter/TicketFilterToolbar.vue'
import { useDynamicFilters } from '~/composables/useDynamicFilters'

// 模拟票据数据
const mockTickets: Ticket[] = [
  {
    id: '1',
    ticketID: 'T001',
    status: '待处理',
    stage: '待处理',
    handler: '张三',
    owner: ['张三', '李四'],
    businessOwner: ['王五'],
    devOwner: ['赵六', '陈七'],
    labels: ['登录问题', '紧急'],
    functionType: ['用户管理', '认证'],
    apps: ['TT语音'],
    severityLevel: '高',
    firstLevelCategory: '技术问题',
    secondLevelCategory: '登录异常',
    problemDescription: '用户无法登录系统',
  },
  {
    id: '2',
    ticketID: 'T002',
    status: '处理中',
    stage: '处理中',
    handler: '李四',
    owner: ['李四'],
    businessOwner: ['王五', '陈七'],
    devOwner: ['赵六'],
    labels: ['界面优化', 'UI问题'],
    functionType: ['界面设计'],
    apps: ['游戏大厅', 'TT语音'],
    severityLevel: '中',
    firstLevelCategory: '产品优化',
    secondLevelCategory: '界面改进',
    problemDescription: '界面布局需要优化',
  },
  {
    id: '3',
    ticketID: 'T003',
    status: '已处理',
    stage: '已处理',
    handler: '王五',
    owner: ['王五', '张三'],
    businessOwner: ['陈七'],
    devOwner: ['赵六', '李四'],
    labels: ['性能问题', '网络异常'],
    functionType: ['性能优化', '数据库'],
    apps: ['麻将', '斗地主'],
    severityLevel: '高',
    firstLevelCategory: '技术问题',
    secondLevelCategory: '性能优化',
    problemDescription: '系统响应速度慢',
  },
  {
    id: '4',
    ticketID: 'T004',
    status: '已归档',
    stage: '已归档',
    handler: '赵六',
    owner: ['赵六'],
    businessOwner: ['王五'],
    devOwner: ['张三', '李四', '陈七'],
    labels: ['功能建议', '游戏体验'],
    functionType: ['新功能'],
    apps: ['德州扑克', '象棋'],
    severityLevel: '低',
    firstLevelCategory: '功能需求',
    secondLevelCategory: '新增功能',
    problemDescription: '建议添加新的游戏模式',
  },
]

// 使用动态筛选
const { generateFilterCategories, getFilterStats, createTicketFilter } = useDynamicFilters()

// 生成筛选类别
const filterCategories = computed(() => {
  return generateFilterCategories(mockTickets)
})

// 获取统计信息
const filterStats = computed(() => {
  return getFilterStats(mockTickets)
})

// 筛选状态 - 添加一些预设的筛选条件来展示图标效果
const activeFilters = ref<Record<string, string[]>>({
  status: ['待处理', '处理中'],
  apps: ['TT语音', '游戏大厅'],
  labels: ['登录问题', '性能问题'],
})

// 处理筛选事件
function handleFilter(filters: Record<string, string[]>) {
  activeFilters.value = filters
  console.log('筛选条件变化:', filters)
}

// 根据筛选条件过滤票据
const filteredTickets = computed(() => {
  if (Object.keys(activeFilters.value).length === 0) {
    return mockTickets
  }

  const filterFn = createTicketFilter(activeFilters.value)
  return mockTickets.filter(filterFn)
})

// 页面标题
useHead({
  title: '动态筛选测试',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mb-6">
      <h1 class="mb-2 text-2xl font-bold">
        动态筛选测试页面
      </h1>
      <p class="text-muted-foreground">
        测试从票据数据中动态生成筛选选项的功能
      </p>
    </div>

    <!-- 筛选工具栏 -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle>动态筛选工具栏</CardTitle>
        <CardDescription>
          基于实际数据动态生成的筛选选项
        </CardDescription>
      </CardHeader>
      <CardContent>
        <TicketFilterToolbar
          :tickets="mockTickets"
          :initial-filters="activeFilters"
          title="测试工单列表"
          @filter="handleFilter"
        />
      </CardContent>
    </Card>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <!-- 筛选后的数据展示 -->
      <Card>
        <CardHeader>
          <CardTitle>
            筛选结果 ({{ filteredTickets.length }}/{{ mockTickets.length }} 条)
          </CardTitle>
          <CardDescription>
            <span v-if="Object.keys(activeFilters).length === 0">
              显示所有票据
            </span>
            <span v-else>
              已应用 {{ Object.keys(activeFilters).length }} 个筛选条件
            </span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="max-h-96 overflow-y-auto space-y-2">
            <div
              v-for="ticket in filteredTickets"
              :key="ticket.id"
              class="border rounded-lg p-3"
            >
              <div class="font-medium">
                {{ ticket.ticketID }} - {{ ticket.problemDescription }}
              </div>
              <div class="mt-1 text-sm text-muted-foreground">
                状态: {{ ticket.status }} | 处理人: {{ ticket.handler }}
              </div>
              <div class="text-sm text-muted-foreground">
                标签: {{ ticket.labels?.join(', ') }}
              </div>
            </div>
            <div v-if="filteredTickets.length === 0" class="py-8 text-center text-muted-foreground">
              没有符合筛选条件的票据
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 动态生成的筛选类别 -->
      <Card>
        <CardHeader>
          <CardTitle>动态生成的筛选类别</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="max-h-96 overflow-y-auto space-y-4">
            <div
              v-for="category in filterCategories"
              :key="category.key"
              class="border rounded-lg p-3"
            >
              <h3 class="mb-2 font-medium">
                {{ category.label }} ({{ category.options.length }} 项)
              </h3>
              <div class="flex flex-wrap gap-1">
                <Badge
                  v-for="option in category.options"
                  :key="option.value"
                  variant="secondary"
                  class="text-xs"
                >
                  {{ option.label }}
                  <span v-if="option.count" class="ml-1 text-muted-foreground">
                    ({{ option.count }})
                  </span>
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 统计信息 -->
      <Card class="lg:col-span-2">
        <CardHeader>
          <CardTitle>筛选统计信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
            <div
              v-for="(stats, fieldKey) in filterStats"
              :key="fieldKey"
              class="border rounded-lg p-3"
            >
              <h4 class="mb-2 font-medium">
                {{ fieldKey }}
              </h4>
              <div class="space-y-1">
                <div
                  v-for="(count, value) in stats"
                  :key="value"
                  class="flex justify-between text-sm"
                >
                  <span>{{ value }}</span>
                  <span class="text-muted-foreground">{{ count }}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
