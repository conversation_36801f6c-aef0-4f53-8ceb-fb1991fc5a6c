<script setup lang="ts">
// 简单的弹窗测试页面
const isLoading = ref(false)

async function testBasicModal() {
  isLoading.value = true
  try {
    console.log('测试基础弹窗...')
    const modal = useModal()
    const result = await modal.info('这是一个测试消息', '测试标题')
    console.log('基础弹窗结果:', result)
  }
  catch (error) {
    console.error('基础弹窗测试失败:', error)
  }
  finally {
    isLoading.value = false
  }
}

async function testConfirmModal() {
  isLoading.value = true
  try {
    console.log('测试确认弹窗...')
    const modal = useModal()
    const result = await modal.confirm('确定要执行这个操作吗？', '确认操作')
    console.log('确认弹窗结果:', result)
  }
  catch (error) {
    console.error('确认弹窗测试失败:', error)
  }
  finally {
    isLoading.value = false
  }
}

async function testAuthExpiredModal() {
  isLoading.value = true
  try {
    console.log('测试认证过期弹窗...')
    const modal = useModal()
    const result = await modal.showAuthExpiredModal()
    console.log('认证过期弹窗结果:', result)
  }
  catch (error) {
    console.error('认证过期弹窗测试失败:', error)
  }
  finally {
    isLoading.value = false
  }
}

async function testErrorModal() {
  isLoading.value = true
  try {
    console.log('测试错误弹窗...')
    const modal = useModal()
    const result = await modal.error('发生了一个错误', '错误')
    console.log('错误弹窗结果:', result)
  }
  catch (error) {
    console.error('错误弹窗测试失败:', error)
  }
  finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mx-auto max-w-2xl">
      <h1 class="mb-6 text-2xl font-bold">
        弹窗功能测试
      </h1>

      <div class="space-y-4">
        <div class="border border-blue-200 rounded-lg bg-blue-50 p-4">
          <h2 class="mb-2 text-lg text-blue-800 font-semibold">
            弹窗测试
          </h2>
          <p class="mb-4 text-blue-700">
            测试各种类型的弹窗是否能正常显示和工作
          </p>

          <div class="grid gap-3">
            <Button
              :disabled="isLoading"
              variant="outline"
              class="w-full"
              @click="testBasicModal"
            >
              测试基础信息弹窗
            </Button>

            <Button
              :disabled="isLoading"
              variant="outline"
              class="w-full"
              @click="testConfirmModal"
            >
              测试确认弹窗
            </Button>

            <Button
              :disabled="isLoading"
              variant="outline"
              class="w-full"
              @click="testErrorModal"
            >
              测试错误弹窗
            </Button>

            <Button
              :disabled="isLoading"
              variant="destructive"
              class="w-full"
              @click="testAuthExpiredModal"
            >
              测试认证过期弹窗
            </Button>
          </div>
        </div>

        <div class="border border-gray-200 rounded-lg bg-gray-50 p-4">
          <h3 class="mb-2 font-medium">
            预期行为
          </h3>
          <ul class="text-sm text-gray-600 space-y-1">
            <li>• 点击按钮应该显示对应类型的弹窗</li>
            <li>• 弹窗应该有正确的图标和样式</li>
            <li>• 认证过期弹窗应该有"稍后处理"和"立即登录"按钮</li>
            <li>• 弹窗应该是模态的，有遮罩层</li>
            <li>• 控制台应该输出弹窗的返回结果</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
