<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import TicketDetail from '~/components/feedback/TicketDetail.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ref } from 'vue'

definePageMeta({
  title: '工单详情测试',
  layout: 'default',
})

// 创建包含真实华为云OBS附件的测试工单
const testTicketWithAttachments = ref<Ticket>({
  id: 'test-obs-video',
  ticketID: 'TK-OBS-001',
  title: '华为云OBS视频附件测试工单',
  problemDescription: '这是一个用于测试华为云OBS视频附件的工单，包含真实的视频文件URL',
  stage: '待处理',
  status: '待处理',
  creator: 'test-user',
  feedbackPerson: 'test-user',
  createdAt: '2024-01-20 10:00:00',
  enterTime: '2024-01-20 10:00:00',
  severityLevel: '中',
  firstLevelCategory: '测试分类',
  secondLevelCategory: '附件测试',
  thirdLevelCategory: '视频测试',
  functionType: ['附件功能', '视频播放'],
  apps: ['TT语音'],
  appVersion: ['1.0.0'],
  osType: ['Android'],
  mobileType: ['测试设备'],
  userTtid: ['123456'],
  
  // 包含华为云OBS视频附件
  attachments: [
    {
      uid: '22870a5133574988be17e16071bdecf5',
      name: '20240118-165846.mp4',
      url: 'https://obs-test-hw-gz-yw-fmp-backend.obs.cn-south-1.myhuaweicloud.com/22870a5133574988be17e16071bdecf5.mp4',
      type: 'mp4',
      size: '5242880'
    },
    {
      uid: 'test-image-001',
      name: '测试图片.jpg',
      url: 'https://picsum.photos/800/600?random=1',
      type: 'jpg',
      size: '245760'
    },
    {
      uid: 'test-video-002',
      name: '在线测试视频.mp4',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      type: 'mp4',
      size: '15728640'
    }
  ],
  
  changeLogs: [
    {
      time: '2024-01-20 10:00:00',
      content: '创建了工单',
      operator: 'test-user',
      type: 'create'
    }
  ]
})

// 创建无附件的测试工单
const testTicketNoAttachments = ref<Ticket>({
  id: 'test-no-attachments',
  ticketID: 'TK-NO-ATT-001',
  title: '无附件测试工单',
  problemDescription: '这是一个没有附件的测试工单',
  stage: '处理中',
  status: '处理中',
  creator: 'test-user',
  feedbackPerson: 'test-user',
  handler: 'admin',
  devProcessor: 'admin',
  createdAt: '2024-01-20 09:00:00',
  enterTime: '2024-01-20 09:00:00',
  responseTime: '2024-01-20 10:30:00',
  severityLevel: '低',
  firstLevelCategory: '测试分类',
  secondLevelCategory: '功能测试',
  functionType: ['基础功能'],
  apps: ['TT语音'],
  
  attachments: [], // 空附件数组
  
  changeLogs: [
    {
      time: '2024-01-20 09:00:00',
      content: '创建了工单',
      operator: 'test-user',
      type: 'create'
    },
    {
      time: '2024-01-20 10:30:00',
      content: '认领了工单',
      operator: 'admin',
      type: 'claim'
    }
  ]
})

const currentTicket = ref<Ticket>(testTicketWithAttachments.value)

function switchToAttachmentsTicket() {
  currentTicket.value = testTicketWithAttachments.value
}

function switchToNoAttachmentsTicket() {
  currentTicket.value = testTicketNoAttachments.value
}

function handleRefresh() {
  console.log('刷新工单列表')
}

function handleRefreshTicket() {
  console.log('刷新当前工单')
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-6">
    <div class="mb-6">
      <h1 class="text-3xl font-bold">工单详情测试页面</h1>
      <p class="text-muted-foreground mt-2">
        测试工单详情组件中的附件预览功能，特别是华为云OBS视频
      </p>
    </div>

    <!-- 测试控制 -->
    <Card>
      <CardHeader>
        <CardTitle>测试控制</CardTitle>
        <CardDescription>
          切换不同的测试工单来验证附件功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex gap-2 flex-wrap">
          <Button 
            @click="switchToAttachmentsTicket"
            :variant="currentTicket.id === 'test-obs-video' ? 'default' : 'outline'"
          >
            华为云OBS附件工单
          </Button>
          <Button 
            @click="switchToNoAttachmentsTicket"
            :variant="currentTicket.id === 'test-no-attachments' ? 'default' : 'outline'"
          >
            无附件工单
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 当前工单信息 -->
    <Card>
      <CardHeader>
        <CardTitle>当前测试工单</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2 text-sm">
          <div><strong>工单ID:</strong> {{ currentTicket.ticketID }}</div>
          <div><strong>标题:</strong> {{ currentTicket.title }}</div>
          <div><strong>状态:</strong> {{ currentTicket.stage }}</div>
          <div><strong>附件数量:</strong> {{ currentTicket.attachments?.length || 0 }}</div>
          <div v-if="currentTicket.attachments && currentTicket.attachments.length > 0">
            <strong>附件列表:</strong>
            <ul class="list-disc list-inside ml-4 mt-1">
              <li v-for="attachment in currentTicket.attachments" :key="attachment.uid">
                {{ attachment.name }} ({{ attachment.type }})
                <br>
                <span class="text-xs text-muted-foreground">{{ attachment.url }}</span>
              </li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 工单详情组件 -->
    <div class="border rounded-lg">
      <TicketDetail
        :ticket="currentTicket"
        @close="() => console.log('关闭工单详情')"
        @refresh="handleRefresh"
        @refresh-ticket="handleRefreshTicket"
      />
    </div>

    <!-- 说明信息 -->
    <Card>
      <CardHeader>
        <CardTitle>测试说明</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="text-sm space-y-2">
          <p><strong>华为云OBS附件工单:</strong> 包含真实的华为云OBS视频文件，用于测试CORS代理功能</p>
          <p><strong>无附件工单:</strong> 没有附件的工单，用于测试无附件状态的显示</p>
          <p><strong>预期结果:</strong></p>
          <ul class="list-disc list-inside ml-4 space-y-1">
            <li>华为云OBS视频应该能正常预览播放（通过代理）</li>
            <li>在线测试视频应该能正常播放</li>
            <li>测试图片应该能正常显示</li>
            <li>无附件工单应该显示"暂无附件"状态</li>
          </ul>
          <p><strong>调试方法:</strong></p>
          <ul class="list-disc list-inside ml-4 space-y-1">
            <li>打开浏览器开发者工具查看网络请求</li>
            <li>查看控制台的URL调试信息</li>
            <li>观察视频是否使用了代理URL (/proxy/obs/...)</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
