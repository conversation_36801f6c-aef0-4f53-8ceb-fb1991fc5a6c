<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import { computed, ref } from 'vue'
import { useTicketOperations } from '~/composables/useTicketOperations'

// 页面标题
definePageMeta({
  title: '工单权限测试',
  description: '测试修复后的工单权限控制和环节管理',
})

// 模拟不同状态的工单数据
const mockTickets: Ticket[] = [
  {
    id: 'test-1',
    ticketID: 'T001',
    problemDescription: '测试工单 - 待处理状态',
    stage: '待处理',
    creator: 'testuser',
    feedbackPerson: 'testuser',
    handler: '',
    devProcessor: '',
    enterTime: '2024-01-15 10:00:00',
  },
  {
    id: 'test-2',
    ticketID: 'T002',
    problemDescription: '测试工单 - 处理中状态',
    stage: '处理中',
    creator: 'testuser',
    feedbackPerson: 'testuser',
    handler: 'admin',
    devProcessor: 'admin',
    enterTime: '2024-01-15 09:00:00',
    startTime: '2024-01-15 10:30:00',
  },
  {
    id: 'test-3',
    ticketID: 'T003',
    problemDescription: '测试工单 - 已处理状态',
    stage: '已处理',
    creator: 'testuser',
    feedbackPerson: 'testuser',
    handler: 'admin',
    devProcessor: 'admin',
    result: '问题已解决',
    cause: '配置错误',
    enterTime: '2024-01-15 08:00:00',
    startTime: '2024-01-15 09:30:00',
    endTime: '2024-01-15 11:00:00',
  },
  {
    id: 'test-4',
    ticketID: 'T004',
    problemDescription: '测试工单 - 已归档状态（测试删除权限）',
    stage: '已归档',
    creator: 'testuser',
    feedbackPerson: 'testuser',
    handler: 'admin',
    devProcessor: 'admin',
    result: '问题已解决并归档',
    cause: '系统升级导致',
    enterTime: '2024-01-15 07:00:00',
    startTime: '2024-01-15 08:30:00',
    endTime: '2024-01-15 10:00:00',
    responseTime: '2024-01-15 12:00:00',
  },
  {
    id: 'test-5',
    ticketID: 'T005',
    problemDescription: '测试工单 - 已处理状态（非创建人，测试驳回权限）',
    stage: '已处理',
    creator: 'otheruser',
    feedbackPerson: 'otheruser',
    handler: 'admin',
    devProcessor: 'admin',
    result: '问题已解决',
    cause: '配置问题',
    enterTime: '2024-01-15 06:00:00',
    startTime: '2024-01-15 07:30:00',
    endTime: '2024-01-15 09:00:00',
  },
]

// 当前选择的工单
const selectedTicket = ref<Ticket>(mockTickets[0])

// 使用修复后的权限系统
const {
  checkPermission,
  checkTicketStatus,
  getAvailableActions,
  canExecuteAction,
  getUserRoles,
  getCurrentUsername,
} = useTicketOperations()

// 计算权限检查结果
const permissionResults = computed(() => {
  const actions = ['handle', 'assign', 'complete', 'check', 'reject', 'urge', 'delete']

  return actions.map((action) => {
    const hasPermission = checkPermission(action, selectedTicket.value)
    const hasValidStatus = checkTicketStatus(action, selectedTicket.value)
    const { allowed, reason } = canExecuteAction(action, selectedTicket.value)

    return {
      action,
      hasPermission,
      hasValidStatus,
      allowed,
      reason,
    }
  })
})

// 获取可用操作
const availableActions = computed(() => {
  return getAvailableActions(selectedTicket.value)
})

// 当前用户信息
const currentUserInfo = computed(() => ({
  username: getCurrentUsername(),
  roles: getUserRoles(),
}))
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="mb-6">
      <h1 class="mb-2 text-3xl font-bold">
        工单权限测试
      </h1>
      <p class="text-muted-foreground">
        测试修复后的工单权限控制和环节管理功能，使用当前登录用户的权限进行测试
      </p>
    </div>

    <!-- 测试控制面板 -->
    <div class="mb-8">
      <!-- 选择工单 -->
      <Card>
        <CardHeader>
          <CardTitle>选择测试工单</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid gap-2">
            <Button
              v-for="ticket in mockTickets"
              :key="ticket.id"
              :variant="selectedTicket.id === ticket.id ? 'default' : 'outline'"
              class="justify-start"
              @click="selectedTicket = ticket"
            >
              <div class="text-left">
                <div class="font-medium">
                  {{ ticket.ticketID }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ ticket.stage }}
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- 当前测试状态 -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle>当前测试状态</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid gap-4 md:grid-cols-3">
          <div>
            <div class="text-sm text-muted-foreground">
              测试工单
            </div>
            <div class="font-medium">
              {{ selectedTicket.ticketID }} - {{ selectedTicket.stage }}
            </div>
          </div>
          <div>
            <div class="text-sm text-muted-foreground">
              当前用户
            </div>
            <div class="font-medium">
              {{ currentUserInfo.username }} ({{ currentUserInfo.roles.join(', ') }})
            </div>
          </div>
          <div>
            <div class="text-sm text-muted-foreground">
              工单处理人
            </div>
            <div class="font-medium">
              {{ selectedTicket.handler || selectedTicket.devProcessor || '未分配' }}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 权限检查结果 -->
    <Card class="mb-6">
      <CardHeader>
        <CardTitle>权限检查结果</CardTitle>
        <div class="text-sm text-muted-foreground">
          详细的权限和状态检查结果
        </div>
      </CardHeader>
      <CardContent>
        <div class="grid gap-3">
          <div
            v-for="result in permissionResults"
            :key="result.action"
            class="border rounded-lg p-4"
            :class="result.allowed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'"
          >
            <div class="mb-3 flex items-center justify-between">
              <h3 class="font-medium">
                {{ result.action }}
              </h3>
              <Badge :variant="result.allowed ? 'default' : 'destructive'">
                {{ result.allowed ? '允许' : '禁止' }}
              </Badge>
            </div>

            <div class="grid gap-2 text-sm">
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">角色权限检查:</span>
                <Badge :variant="result.hasPermission ? 'default' : 'secondary'" class="text-xs">
                  {{ result.hasPermission ? '通过' : '失败' }}
                </Badge>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-muted-foreground">状态流转检查:</span>
                <Badge :variant="result.hasValidStatus ? 'default' : 'secondary'" class="text-xs">
                  {{ result.hasValidStatus ? '通过' : '失败' }}
                </Badge>
              </div>
              <div class="mt-2 text-xs text-muted-foreground">
                <strong>说明:</strong> {{ result.reason }}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 可用操作按钮 -->
    <Card>
      <CardHeader>
        <CardTitle>可用操作按钮</CardTitle>
        <div class="text-sm text-muted-foreground">
          根据权限和状态生成的操作按钮
        </div>
      </CardHeader>
      <CardContent>
        <div class="flex flex-wrap gap-3">
          <Button
            v-for="action in availableActions"
            :key="action.action"
            :variant="action.variant"
            :disabled="action.disabled"
            class="relative"
          >
            {{ action.label }}
            <span v-if="action.disabled" class="sr-only">{{ action.reason }}</span>
          </Button>
        </div>

        <div v-if="availableActions.length === 0" class="py-8 text-center text-muted-foreground">
          当前状态下没有可用的操作
        </div>
      </CardContent>
    </Card>
  </div>
</template>
