<script setup lang="ts">
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { useNotification } from '~/composables/useNotification'

// 页面标题
useHead({
  title: 'Toast 居中测试',
})

const notification = useNotification()

function showSuccessToast() {
  notification.success('完成工单成功', '操作成功')
}

function showErrorToast() {
  notification.error('操作失败，请重试', '错误提示')
}

function showWarningToast() {
  notification.warning('请注意检查输入内容', '警告提示')
}

function showInfoToast() {
  notification.info('这是一条信息提示', '信息提示')
}

function showLongMessageToast() {
  notification.success('这是一条很长的成功消息，用来测试多行文本的垂直居中效果是否正常显示', '长消息测试')
}

function showOnlyDescriptionToast() {
  notification.success('只有描述信息的通知')
}
</script>

<template>
  <div class="container mx-auto py-8 space-y-8">
    <div class="text-center space-y-2">
      <h1 class="text-3xl font-bold">
        Toast 垂直居中测试
      </h1>
      <p class="text-muted-foreground">
        测试不同类型的 Toast 通知的垂直居中效果
      </p>
    </div>

    <Card>
      <CardHeader>
        <CardTitle>Toast 通知测试</CardTitle>
        <CardDescription>
          点击按钮测试不同类型的通知消息的垂直居中效果
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Button @click="showSuccessToast" variant="default" class="w-full">
            成功通知
          </Button>
          
          <Button @click="showErrorToast" variant="destructive" class="w-full">
            错误通知
          </Button>
          
          <Button @click="showWarningToast" variant="outline" class="w-full">
            警告通知
          </Button>
          
          <Button @click="showInfoToast" variant="secondary" class="w-full">
            信息通知
          </Button>
          
          <Button @click="showLongMessageToast" variant="default" class="w-full">
            长消息测试
          </Button>
          
          <Button @click="showOnlyDescriptionToast" variant="outline" class="w-full">
            只有描述
          </Button>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>测试说明</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2 text-sm text-muted-foreground">
          <p>• 检查图标和文本是否垂直居中对齐</p>
          <p>• 检查多行文本的情况下是否保持居中</p>
          <p>• 检查只有标题或只有描述的情况</p>
          <p>• 检查不同变体（成功、错误、警告、信息）的对齐效果</p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
