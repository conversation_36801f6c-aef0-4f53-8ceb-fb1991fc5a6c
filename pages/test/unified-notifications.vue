<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/components/ui/toast'
import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-vue-next'
import { toast as sonnerToast } from 'vue-sonner'
import { useModal } from '~/composables/useModal'
import { useNotification } from '~/composables/useNotification'

const modal = useModal()
const notification = useNotification()

// Toast 测试函数
function showToastInfo() {
  toast({
    variant: 'info',
    title: '信息提示',
    description: '这是一个信息类型的 Toast 通知，使用了统一的蓝色主题。',
  })
}

function showToastSuccess() {
  toast({
    variant: 'success',
    title: '操作成功',
    description: '这是一个成功类型的 Toast 通知，使用了统一的绿色主题。',
  })
}

function showToastWarning() {
  toast({
    variant: 'warning',
    title: '警告提示',
    description: '这是一个警告类型的 Toast 通知，使用了统一的黄色主题。',
  })
}

function showToastError() {
  toast({
    variant: 'destructive',
    title: '错误提示',
    description: '这是一个错误类型的 Toast 通知，使用了统一的红色主题。',
  })
}

// Sonner 测试函数
function showSonnerInfo() {
  sonnerToast.info('这是一个信息类型的 Sonner 通知，使用了统一的蓝色主题。')
}

function showSonnerSuccess() {
  sonnerToast.success('这是一个成功类型的 Sonner 通知，使用了统一的绿色主题。')
}

function showSonnerWarning() {
  sonnerToast.warning('这是一个警告类型的 Sonner 通知，使用了统一的黄色主题。')
}

function showSonnerError() {
  sonnerToast.error('这是一个错误类型的 Sonner 通知，使用了统一的红色主题。')
}

// Modal 测试函数
async function showModalInfo() {
  await modal.info('这是一个信息类型的 Modal 弹窗，使用了统一的蓝色主题。', '信息提示')
}

async function showModalSuccess() {
  await modal.success('这是一个成功类型的 Modal 弹窗，使用了统一的绿色主题。', '操作成功')
}

async function showModalWarning() {
  await modal.warning('这是一个警告类型的 Modal 弹窗，使用了统一的黄色主题。', '警告提示')
}

async function showModalError() {
  await modal.error('这是一个错误类型的 Modal 弹窗，使用了统一的红色主题。', '错误提示')
}

// useNotification 测试函数
function showNotificationInfo() {
  notification.info('这是一个信息类型的 useNotification 通知，自动消失时间为4秒。', '信息提示')
}

function showNotificationSuccess() {
  notification.success('这是一个成功类型的 useNotification 通知，自动消失时间为4秒。', '操作成功')
}

function showNotificationWarning() {
  notification.warning('这是一个警告类型的 useNotification 通知，自动消失时间为4秒。', '警告提示')
}

function showNotificationError() {
  notification.error('这是一个错误类型的 useNotification 通知，自动消失时间为4秒。', '错误提示')
}

function showNotificationPersistent() {
  notification.info('这是一个持久化的 useNotification 通知，不会自动消失。', '持久化通知', { persistent: true })
}

definePageMeta({
  title: '优化后的通知系统测试',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">
          🚀 优化后的通知系统测试
        </h1>
        <p class="mt-2 text-muted-foreground">
          测试优化后的 useNotification：合理的自动消失时间、统一的样式、移除兼容模式
        </p>
      </div>

      <!-- useNotification 测试 -->
      <Card>
        <CardHeader>
          <CardTitle>🔧 useNotification 通知</CardTitle>
          <CardDescription>
            测试优化后的 useNotification，统一样式，合理的自动消失时间（4秒）
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <Button variant="outline" @click="showNotificationInfo">
              <Info class="mr-2 h-4 w-4 text-blue-500" />
              信息通知
            </Button>
            <Button variant="outline" @click="showNotificationSuccess">
              <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
              成功通知
            </Button>
            <Button variant="outline" @click="showNotificationWarning">
              <AlertTriangle class="mr-2 h-4 w-4 text-yellow-500" />
              警告通知
            </Button>
            <Button variant="outline" @click="showNotificationError">
              <XCircle class="mr-2 h-4 w-4 text-red-500" />
              错误通知
            </Button>
          </div>
          <div class="pt-2">
            <Button variant="secondary" @click="showNotificationPersistent">
              持久化通知（不自动消失）
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Toast 测试 -->
      <Card>
        <CardHeader>
          <CardTitle>📋 Toast 通知</CardTitle>
          <CardDescription>
            测试原生 Toast 样式，包含图标和统一的颜色主题
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <Button variant="outline" @click="showToastInfo">
              <Info class="mr-2 h-4 w-4 text-blue-500" />
              信息 Toast
            </Button>
            <Button variant="outline" @click="showToastSuccess">
              <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
              成功 Toast
            </Button>
            <Button variant="outline" @click="showToastWarning">
              <AlertTriangle class="mr-2 h-4 w-4 text-yellow-500" />
              警告 Toast
            </Button>
            <Button variant="outline" @click="showToastError">
              <XCircle class="mr-2 h-4 w-4 text-red-500" />
              错误 Toast
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Sonner 测试 -->
      <Card>
        <CardHeader>
          <CardTitle>🔔 Sonner 通知</CardTitle>
          <CardDescription>
            测试新的 Sonner 样式，与 Modal 保持一致的设计风格
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <Button variant="outline" @click="showSonnerInfo">
              <Info class="mr-2 h-4 w-4 text-blue-500" />
              信息 Sonner
            </Button>
            <Button variant="outline" @click="showSonnerSuccess">
              <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
              成功 Sonner
            </Button>
            <Button variant="outline" @click="showSonnerWarning">
              <AlertTriangle class="mr-2 h-4 w-4 text-yellow-500" />
              警告 Sonner
            </Button>
            <Button variant="outline" @click="showSonnerError">
              <XCircle class="mr-2 h-4 w-4 text-red-500" />
              错误 Sonner
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Modal 对比 -->
      <Card>
        <CardHeader>
          <CardTitle>💬 Modal 弹窗</CardTitle>
          <CardDescription>
            对比 Modal 样式，确保设计风格一致
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <Button variant="outline" @click="showModalInfo">
              <Info class="mr-2 h-4 w-4 text-blue-500" />
              信息 Modal
            </Button>
            <Button variant="outline" @click="showModalSuccess">
              <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
              成功 Modal
            </Button>
            <Button variant="outline" @click="showModalWarning">
              <AlertTriangle class="mr-2 h-4 w-4 text-yellow-500" />
              警告 Modal
            </Button>
            <Button variant="outline" @click="showModalError">
              <XCircle class="mr-2 h-4 w-4 text-red-500" />
              错误 Modal
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 设计对比说明 -->
      <Card>
        <CardHeader>
          <CardTitle>🎨 统一设计特点</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div class="border rounded-lg bg-blue-50 p-4 dark:bg-blue-950">
                <h4 class="mb-2 text-blue-800 font-medium dark:text-blue-200">
                  🎯 统一的图标设计
                </h4>
                <ul class="text-sm text-blue-700 space-y-1 dark:text-blue-300">
                  <li>• 圆形背景的图标</li>
                  <li>• 一致的图标大小</li>
                  <li>• 类型化的颜色主题</li>
                </ul>
              </div>
              <div class="border rounded-lg bg-green-50 p-4 dark:bg-green-950">
                <h4 class="mb-2 text-green-800 font-medium dark:text-green-200">
                  🌈 统一的颜色系统
                </h4>
                <ul class="text-sm text-green-700 space-y-1 dark:text-green-300">
                  <li>• 信息：蓝色主题</li>
                  <li>• 成功：绿色主题</li>
                  <li>• 警告：黄色主题</li>
                  <li>• 错误：红色主题</li>
                </ul>
              </div>
              <div class="border rounded-lg bg-purple-50 p-4 dark:bg-purple-950">
                <h4 class="mb-2 text-purple-800 font-medium dark:text-purple-200">
                  📐 统一的布局风格
                </h4>
                <ul class="text-sm text-purple-700 space-y-1 dark:text-purple-300">
                  <li>• 一致的圆角设计</li>
                  <li>• 统一的间距规范</li>
                  <li>• 相同的阴影效果</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
