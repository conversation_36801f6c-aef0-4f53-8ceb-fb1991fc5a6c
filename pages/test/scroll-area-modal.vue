<script setup lang="ts">
const isOpen = ref(false)

function openModal() {
  isOpen.value = true
}

function closeModal() {
  isOpen.value = false
}
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-4">
      <h1 class="text-2xl font-bold">
        ScrollArea 弹窗测试
      </h1>
      <p class="text-muted-foreground">
        测试弹窗中 ScrollArea 的滚动功能
      </p>

      <Button @click="openModal">
        打开测试弹窗
      </Button>
    </div>

    <!-- 测试弹窗 -->
    <Dialog v-model:open="isOpen">
      <DialogContent class="grid grid-rows-[auto_minmax(0,1fr)_auto] max-h-[90vh] max-w-4xl p-0">
        <!-- 固定顶部 -->
        <DialogHeader class="border-b border-border/20 px-6 py-4">
          <DialogTitle>ScrollArea 测试弹窗</DialogTitle>
          <DialogDescription>
            这是一个测试 ScrollArea 滚动功能的弹窗
          </DialogDescription>
        </DialogHeader>

        <!-- 可滚动内容区域 -->
        <ScrollArea class="overflow-y-auto">
          <div class="px-6 py-6 space-y-6">
            <!-- 生成大量内容用于测试滚动 -->
            <div v-for="i in 50" :key="i" class="space-y-2">
              <Label class="text-sm font-medium">
                测试字段 {{ i }} <span class="text-destructive">*</span>
              </Label>
              <Input
                :placeholder="`请输入测试内容 ${i}`"
                class="w-full"
              />
              <p class="text-xs text-muted-foreground">
                这是第 {{ i }} 个测试字段的说明文字。这个弹窗使用了 shadcn-ui 的 ScrollArea 组件，
                应该可以正常滚动。如果内容超出了可视区域，应该会出现滚动条。
              </p>
            </div>
          </div>
        </ScrollArea>

        <!-- 固定底部 -->
        <DialogFooter class="border-t border-border/20 px-6 py-4">
          <Button type="button" variant="outline" @click="closeModal">
            取消
          </Button>
          <Button @click="closeModal">
            确定
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>
