<template>
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">🎯 统一弹窗系统测试</h1>
        <p class="mt-2 text-muted-foreground">
          测试新的 useModal 统一弹窗系统
        </p>
      </div>

      <!-- 基础弹窗测试 -->
      <Card>
        <CardHeader>
          <CardTitle>📋 基础弹窗类型</CardTitle>
          <CardDescription>
            测试不同类型的弹窗效果
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <Button variant="outline" @click="showInfoModal">
              <Info class="mr-2 h-4 w-4 text-blue-500" />
              信息弹窗
            </Button>
            <Button variant="outline" @click="showSuccessModal">
              <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
              成功弹窗
            </Button>
            <Button variant="outline" @click="showWarningModal">
              <AlertTriangle class="mr-2 h-4 w-4 text-yellow-500" />
              警告弹窗
            </Button>
            <Button variant="outline" @click="showErrorModal">
              <XCircle class="mr-2 h-4 w-4 text-red-500" />
              错误弹窗
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 确认对话框测试 -->
      <Card>
        <CardHeader>
          <CardTitle>❓ 确认对话框</CardTitle>
          <CardDescription>
            测试确认对话框的交互
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
            <Button variant="outline" @click="showConfirmModal">
              <HelpCircle class="mr-2 h-4 w-4 text-blue-500" />
              确认对话框
            </Button>
            <Button variant="outline" @click="showDeleteConfirm">
              <Trash2 class="mr-2 h-4 w-4 text-red-500" />
              删除确认
            </Button>
          </div>
          <div v-if="lastResult" class="p-3 bg-muted rounded-lg">
            <p class="text-sm">
              <strong>上次操作结果:</strong> {{ lastResult }}
            </p>
          </div>
        </CardContent>
      </Card>

      <!-- 认证过期测试 -->
      <Card>
        <CardHeader>
          <CardTitle>🔐 认证过期处理</CardTitle>
          <CardDescription>
            测试统一的认证过期对话框
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
            <Button variant="outline" @click="showAuthExpired">
              <Shield class="mr-2 h-4 w-4 text-orange-500" />
              认证过期对话框
            </Button>
            <Button variant="outline" @click="showCustomAuthExpired">
              <Key class="mr-2 h-4 w-4 text-red-500" />
              自定义重定向路径
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 高级功能测试 -->
      <Card>
        <CardHeader>
          <CardTitle>⚙️ 高级功能</CardTitle>
          <CardDescription>
            测试弹窗的高级配置选项
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
            <Button variant="outline" @click="showPersistentModal">
              <Lock class="mr-2 h-4 w-4 text-purple-500" />
              持久化弹窗
            </Button>
            <Button variant="outline" @click="showNoCloseModal">
              <X class="mr-2 h-4 w-4 text-gray-500" />
              不可关闭
            </Button>
            <Button variant="outline" @click="showCustomButtonsModal">
              <Settings class="mr-2 h-4 w-4 text-blue-500" />
              自定义按钮
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 对比说明 -->
      <Card>
        <CardHeader>
          <CardTitle>📊 统一弹窗系统优势</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div class="border rounded-lg p-4 bg-green-50 dark:bg-green-950">
                <h4 class="font-medium text-green-800 dark:text-green-200 mb-2">
                  ✅ 优化后
                </h4>
                <ul class="text-sm text-green-700 dark:text-green-300 space-y-1">
                  <li>• 统一的 useModal 系统</li>
                  <li>• 一致的样式和交互</li>
                  <li>• 支持多种弹窗类型</li>
                  <li>• 更好的可配置性</li>
                  <li>• 简化的维护成本</li>
                </ul>
              </div>
              <div class="border rounded-lg p-4 bg-red-50 dark:bg-red-950">
                <h4 class="font-medium text-red-800 dark:text-red-200 mb-2">
                  ❌ 优化前
                </h4>
                <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                  <li>• 多个分散的弹窗文件</li>
                  <li>• 样式不统一</li>
                  <li>• 重复的代码逻辑</li>
                  <li>• 难以维护和扩展</li>
                  <li>• 用户体验不一致</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Info, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  HelpCircle, 
  Trash2, 
  Shield, 
  Key, 
  Lock, 
  X, 
  Settings 
} from 'lucide-vue-next'
import { useModal } from '~/composables/useModal'

const modal = useModal()
const lastResult = ref<string>('')

// 基础弹窗测试
const showInfoModal = async () => {
  await modal.info('这是一个信息弹窗示例，用于显示重要信息。', '信息提示')
}

const showSuccessModal = async () => {
  await modal.success('操作已成功完成！', '成功')
}

const showWarningModal = async () => {
  await modal.warning('请注意，此操作可能会产生影响。', '警告')
}

const showErrorModal = async () => {
  await modal.error('操作失败，请检查输入并重试。', '错误')
}

// 确认对话框测试
const showConfirmModal = async () => {
  const result = await modal.confirm('您确定要执行此操作吗？', '确认操作')
  lastResult.value = result ? '用户点击了确定' : '用户点击了取消'
}

const showDeleteConfirm = async () => {
  const result = await modal.confirm(
    '此操作将永久删除数据，无法恢复。您确定要继续吗？',
    '删除确认',
    {
      confirmText: '删除',
      cancelText: '保留',
      type: 'error'
    }
  )
  lastResult.value = result ? '用户确认删除' : '用户取消删除'
}

// 认证过期测试
const showAuthExpired = async () => {
  await modal.showAuthExpiredModal()
}

const showCustomAuthExpired = async () => {
  await modal.showAuthExpiredModal({
    redirectPath: '/custom-login',
    confirmText: '前往登录',
    cancelText: '稍后再说'
  })
}

// 高级功能测试
const showPersistentModal = async () => {
  await modal.info(
    '这是一个持久化弹窗，不能通过点击外部或按ESC键关闭。',
    '持久化弹窗',
    {
      persistent: true,
      confirmText: '我知道了'
    }
  )
}

const showNoCloseModal = async () => {
  await modal.info(
    '这个弹窗没有关闭按钮，只能通过按钮操作关闭。',
    '不可关闭弹窗',
    {
      closable: false,
      confirmText: '关闭弹窗'
    }
  )
}

const showCustomButtonsModal = async () => {
  const result = await modal.confirm(
    '这是一个自定义按钮文本的弹窗示例。',
    '自定义按钮',
    {
      confirmText: '同意并继续',
      cancelText: '暂不处理',
      type: 'info'
    }
  )
  lastResult.value = result ? '用户同意并继续' : '用户暂不处理'
}

definePageMeta({
  title: '统一弹窗系统测试'
})
</script>
