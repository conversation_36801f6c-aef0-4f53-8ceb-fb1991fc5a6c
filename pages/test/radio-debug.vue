<script setup lang="ts">
import { ref } from 'vue'

const selectedValue = ref('option1')

const options = [
  { value: 'option1', label: '选项一', description: '这是第一个选项' },
  { value: 'option2', label: '选项二', description: '这是第二个选项' },
  { value: 'option3', label: '选项三', description: '这是第三个选项' }
]
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">单选组件调试</h1>
      <p class="text-muted-foreground">
        测试 RadioGroupCard 组件是否正常工作
      </p>
    </div>

    <Card>
      <CardHeader>
        <CardTitle>RadioGroupCard 测试</CardTitle>
        <CardDescription>
          当前选择值: {{ selectedValue }}
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <!-- 使用传统 RadioGroup -->
        <div class="space-y-3">
          <Label class="text-sm font-medium">传统 RadioGroup</Label>
          <RadioGroup v-model="selectedValue" class="flex gap-4">
            <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
              <RadioGroupItem :id="`traditional-${option.value}`" :value="option.value" />
              <Label :for="`traditional-${option.value}`" class="cursor-pointer text-sm">{{ option.label }}</Label>
            </div>
          </RadioGroup>
        </div>

        <!-- 使用 RadioGroupCard -->
        <div class="space-y-3">
          <Label class="text-sm font-medium">RadioGroupCard 测试</Label>
          <RadioGroup v-model="selectedValue" class="flex gap-2">
            <RadioGroupCard
              v-for="option in options"
              :key="option.value"
              :id="`card-${option.value}`"
              :value="option.value"
              :label="option.label"
              :description="option.description"
              :checked="selectedValue === option.value"
              class="min-w-[160px]"
            />
          </RadioGroup>
        </div>

        <div class="text-sm text-muted-foreground">
          当前选择: <Badge variant="outline">{{ selectedValue }}</Badge>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
