<script setup lang="ts">
import { ref } from 'vue'

const selectedPriority = ref('medium')
const selectedDevices = ref(['iOS', 'Android'])
const selectedNotifications = ref(['email'])

const priorityOptions = [
  { value: 'high', label: '高优先级', description: '紧急问题，需要立即处理', icon: 'i-lucide-alert-triangle' },
  { value: 'medium', label: '中优先级', description: '重要问题，需要及时处理', icon: 'i-lucide-alert-circle' },
  { value: 'low', label: '低优先级', description: '一般问题，可以稍后处理', icon: 'i-lucide-info' }
]

const deviceOptions = [
  { value: 'iOS', label: 'iOS', description: 'iPhone、iPad 设备', icon: 'i-lucide-smartphone' },
  { value: 'Android', label: 'Android', description: '安卓手机、平板', icon: 'i-lucide-tablet' },
  { value: 'PC', label: 'PC', description: 'Windows、Mac 电脑', icon: 'i-lucide-monitor' },
  { value: 'Web', label: 'Web', description: '浏览器网页版', icon: 'i-lucide-globe' }
]

const notificationOptions = [
  { value: 'email', label: '邮件通知', description: '发送邮件提醒', icon: 'i-lucide-mail' },
  { value: 'sms', label: '短信通知', description: '发送短信提醒', icon: 'i-lucide-message-square' },
  { value: 'push', label: '推送通知', description: '应用内推送', icon: 'i-lucide-bell' },
  { value: 'webhook', label: 'Webhook', description: 'API 回调通知', icon: 'i-lucide-webhook' }
]

function handleDeviceChange(value: string, checked: boolean) {
  if (checked) {
    selectedDevices.value.push(value)
  } else {
    const index = selectedDevices.value.indexOf(value)
    if (index > -1) {
      selectedDevices.value.splice(index, 1)
    }
  }
}

function handleNotificationChange(value: string, checked: boolean) {
  if (checked) {
    selectedNotifications.value.push(value)
  } else {
    const index = selectedNotifications.value.indexOf(value)
    if (index > -1) {
      selectedNotifications.value.splice(index, 1)
    }
  }
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">水平平铺表单控件</h1>
      <p class="text-muted-foreground">
        类似"设备系统"选项的卡片式布局，水平平铺，扩大可点击区域
      </p>
      <div class="flex gap-2">
        <Badge variant="outline">水平平铺</Badge>
        <Badge variant="outline">扩大点击区域</Badge>
        <Badge variant="outline">卡片式设计</Badge>
        <Badge variant="outline">不换行布局</Badge>
      </div>
    </div>

    <!-- 单选示例 -->
    <Card>
      <CardHeader>
        <CardTitle>单选组件 - 优先级选择</CardTitle>
        <CardDescription>
          水平平铺的单选卡片，每个卡片都是完整的可点击区域
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="space-y-3">
          <Label class="text-sm font-medium">选择问题优先级</Label>
          <RadioGroup v-model="selectedPriority" class="flex gap-3 overflow-x-auto">
            <RadioGroupCard
              v-for="option in priorityOptions"
              :key="option.value"
              :id="`priority-${option.value}`"
              :value="option.value"
              :label="option.label"
              :description="option.description"
              :icon="option.icon"
              :checked="selectedPriority === option.value"
              class="min-w-[200px]"
            />
          </RadioGroup>
        </div>

        <div class="text-sm text-muted-foreground">
          当前选择: <Badge variant="outline">{{ priorityOptions.find(opt => opt.value === selectedPriority)?.label }}</Badge>
        </div>
      </CardContent>
    </Card>

    <!-- 多选示例 -->
    <Card>
      <CardHeader>
        <CardTitle>多选组件 - 设备系统选择</CardTitle>
        <CardDescription>
          水平平铺的多选卡片，支持选择多个设备类型
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="space-y-3">
          <Label class="text-sm font-medium">选择支持的设备系统</Label>
          <div class="flex gap-3 overflow-x-auto">
            <CheckboxCard
              v-for="option in deviceOptions"
              :key="option.value"
              :id="`device-${option.value}`"
              :checked="selectedDevices.includes(option.value)"
              :label="option.label"
              :description="option.description"
              :icon="option.icon"
              class="min-w-[180px]"
              @update:checked="(checked) => handleDeviceChange(option.value, checked)"
            />
          </div>
        </div>

        <div class="text-sm text-muted-foreground">
          已选择设备: 
          <div class="flex flex-wrap gap-1 mt-1">
            <Badge v-for="device in selectedDevices" :key="device" variant="outline">
              {{ deviceOptions.find(opt => opt.value === device)?.label }}
            </Badge>
            <Badge v-if="selectedDevices.length === 0" variant="secondary">无选择</Badge>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 通知设置示例 -->
    <Card>
      <CardHeader>
        <CardTitle>通知方式选择</CardTitle>
        <CardDescription>
          多选通知方式，支持同时选择多种通知类型
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="space-y-3">
          <Label class="text-sm font-medium">选择通知方式</Label>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
            <CheckboxCard
              v-for="option in notificationOptions"
              :key="option.value"
              :id="`notification-${option.value}`"
              :checked="selectedNotifications.includes(option.value)"
              :label="option.label"
              :description="option.description"
              :icon="option.icon"
              @update:checked="(checked) => handleNotificationChange(option.value, checked)"
            />
          </div>
        </div>

        <div class="text-sm text-muted-foreground">
          已选择通知: 
          <div class="flex flex-wrap gap-1 mt-1">
            <Badge v-for="notification in selectedNotifications" :key="notification" variant="outline">
              {{ notificationOptions.find(opt => opt.value === notification)?.label }}
            </Badge>
            <Badge v-if="selectedNotifications.length === 0" variant="secondary">无选择</Badge>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 实际表单示例 -->
    <Card>
      <CardHeader>
        <CardTitle>完整表单示例</CardTitle>
        <CardDescription>
          在实际表单中的应用效果，所有控件都采用水平平铺布局
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form class="space-y-6">
          <!-- 问题类型 -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">问题类型</Label>
            <RadioGroup v-model="selectedPriority" class="flex gap-2">
              <RadioGroupCard
                id="bug"
                value="bug"
                label="Bug"
                :checked="selectedPriority === 'bug'"
              />
              <RadioGroupCard
                id="feature"
                value="feature"
                label="功能请求"
                :checked="selectedPriority === 'feature'"
              />
              <RadioGroupCard
                id="improvement"
                value="improvement"
                label="改进建议"
                :checked="selectedPriority === 'improvement'"
              />
            </RadioGroup>
          </div>

          <!-- 影响范围 -->
          <div class="space-y-3">
            <Label class="text-sm font-medium">影响范围</Label>
            <div class="flex gap-2 flex-wrap">
              <CheckboxCard
                id="ui"
                label="界面"
                :checked="selectedNotifications.includes('ui')"
                @update:checked="(checked) => handleNotificationChange('ui', checked)"
              />
              <CheckboxCard
                id="api"
                label="接口"
                :checked="selectedNotifications.includes('api')"
                @update:checked="(checked) => handleNotificationChange('api', checked)"
              />
              <CheckboxCard
                id="performance"
                label="性能"
                :checked="selectedNotifications.includes('performance')"
                @update:checked="(checked) => handleNotificationChange('performance', checked)"
              />
              <CheckboxCard
                id="security"
                label="安全"
                :checked="selectedNotifications.includes('security')"
                @update:checked="(checked) => handleNotificationChange('security', checked)"
              />
            </div>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 设计说明 -->
    <Card>
      <CardHeader>
        <CardTitle>设计特点</CardTitle>
        <CardDescription>
          新的卡片式布局的优势和特点
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-4 md:grid-cols-2">
          <div class="space-y-3">
            <h4 class="font-medium text-sm">布局优势</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 水平平铺，充分利用屏幕宽度</li>
              <li>• 不换行布局，保持视觉连续性</li>
              <li>• 支持横向滚动，适应不同屏幕</li>
              <li>• 响应式设计，移动端友好</li>
            </ul>
          </div>
          <div class="space-y-3">
            <h4 class="font-medium text-sm">交互优势</h4>
            <ul class="text-sm text-muted-foreground space-y-1">
              <li>• 扩大可点击区域，提升操作体验</li>
              <li>• 清晰的视觉反馈和状态指示</li>
              <li>• 支持图标和描述文本</li>
              <li>• 平滑的过渡动画效果</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
