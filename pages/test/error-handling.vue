<template>
  <div class="container mx-auto p-6">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">错误处理示例</h1>
        <p class="text-muted-foreground mt-2">
          演示如何在实际应用中使用新的错误处理机制
        </p>
      </div>

      <!-- 基础用法示例 -->
      <Card>
        <CardHeader>
          <CardTitle>基础用法</CardTitle>
          <CardDescription>
            使用 handleApiCall 函数处理 API 调用
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="flex gap-2">
            <Button @click="basicSuccessExample" :disabled="loading">
              成功示例
            </Button>
            <Button @click="basicErrorExample" :disabled="loading" variant="destructive">
              错误示例
            </Button>
          </div>
          <div v-if="basicResult" class="p-4 bg-muted rounded-md">
            <pre class="text-sm">{{ JSON.stringify(basicResult, null, 2) }}</pre>
          </div>
        </CardContent>
      </Card>

      <!-- Composable 用法示例 -->
      <Card>
        <CardHeader>
          <CardTitle>Composable 用法</CardTitle>
          <CardDescription>
            使用 useApiErrorHandler composable
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="flex gap-2">
            <Button @click="composableSuccessExample" :disabled="apiHandler.loading.value">
              <Loader2 v-if="apiHandler.loading.value" class="mr-2 h-4 w-4 animate-spin" />
              成功示例
            </Button>
            <Button @click="composableErrorExample" :disabled="apiHandler.loading.value" variant="destructive">
              <Loader2 v-if="apiHandler.loading.value" class="mr-2 h-4 w-4 animate-spin" />
              错误示例
            </Button>
          </div>
          <div v-if="apiHandler.error.value" class="p-4 bg-red-50 border border-red-200 rounded-md">
            <h4 class="font-medium text-red-800">错误信息:</h4>
            <p class="text-red-600">{{ apiHandler.error.value.message }}</p>
            <p class="text-sm text-red-500">类型: {{ apiHandler.error.value.type }} | 分类: {{ apiHandler.error.value.category }}</p>
          </div>
          <div v-if="composableResult" class="p-4 bg-green-50 border border-green-200 rounded-md">
            <h4 class="font-medium text-green-800">成功结果:</h4>
            <pre class="text-sm text-green-600">{{ JSON.stringify(composableResult, null, 2) }}</pre>
          </div>
        </CardContent>
      </Card>

      <!-- 批量处理示例 -->
      <Card>
        <CardHeader>
          <CardTitle>批量处理</CardTitle>
          <CardDescription>
            使用 handleBatchApiCalls 处理多个 API 调用
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="flex gap-2">
            <Button @click="batchExample" :disabled="batchLoading">
              <Loader2 v-if="batchLoading" class="mr-2 h-4 w-4 animate-spin" />
              批量处理示例
            </Button>
          </div>
          <div v-if="batchResult" class="space-y-2">
            <div class="p-4 bg-muted rounded-md">
              <h4 class="font-medium">批量处理结果:</h4>
              <p>成功: {{ batchResult.successCount }} | 失败: {{ batchResult.errorCount }}</p>
            </div>
            <div class="max-h-60 overflow-auto">
              <div v-for="(result, index) in batchResult.results" :key="index" 
                   class="p-2 border rounded-md mb-2"
                   :class="result.error ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'">
                <p class="text-sm font-medium">调用 {{ index + 1 }}:</p>
                <p class="text-sm" :class="result.error ? 'text-red-600' : 'text-green-600'">
                  {{ result.error ? result.error.message : '成功' }}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 错误类型检查示例 -->
      <Card>
        <CardHeader>
          <CardTitle>错误类型检查</CardTitle>
          <CardDescription>
            演示不同类型错误的检查和处理
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button @click="testAuthError" variant="outline" size="sm">
              认证错误
            </Button>
            <Button @click="testPermissionError" variant="outline" size="sm">
              权限错误
            </Button>
            <Button @click="testDataError" variant="outline" size="sm">
              数据错误
            </Button>
            <Button @click="testSystemError" variant="outline" size="sm">
              系统错误
            </Button>
          </div>
          <div v-if="errorTypeResult" class="p-4 bg-muted rounded-md">
            <h4 class="font-medium">错误分析结果:</h4>
            <div class="grid grid-cols-2 gap-4 mt-2 text-sm">
              <div>
                <p><strong>错误码:</strong> {{ errorTypeResult.code }}</p>
                <p><strong>消息:</strong> {{ errorTypeResult.message }}</p>
              </div>
              <div>
                <p><strong>类型:</strong> {{ errorTypeResult.type }}</p>
                <p><strong>分类:</strong> {{ errorTypeResult.category }}</p>
                <p><strong>认证错误:</strong> {{ errorTypeResult.isAuthError ? '是' : '否' }}</p>
                <p><strong>需要重定向:</strong> {{ errorTypeResult.shouldRedirect ? '是' : '否' }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-vue-next'
import { handleApiCall, useApiErrorHandler, handleBatchApiCalls } from '~/utils/errorUtils'
import { ErrorCodeHandler, type ErrorHandleResult } from '~/utils/errorHandler'

// 基础用法状态
const loading = ref(false)
const basicResult = ref<any>(null)

// Composable 用法
const apiHandler = useApiErrorHandler()
const composableResult = ref<any>(null)

// 批量处理状态
const batchLoading = ref(false)
const batchResult = ref<any>(null)

// 错误类型检查状态
const errorTypeResult = ref<ErrorHandleResult | null>(null)

// 模拟 API 调用函数
const mockApiSuccess = async () => {
  await new Promise(resolve => setTimeout(resolve, 1000))
  return { message: '操作成功', data: { id: 1, name: '测试数据' } }
}

const mockApiError = async (code: number) => {
  await new Promise(resolve => setTimeout(resolve, 1000))
  throw { code, msg: `模拟错误 ${code}` }
}

// 基础用法示例
const basicSuccessExample = async () => {
  loading.value = true
  const result = await handleApiCall(mockApiSuccess, {
    showSuccess: true,
    successMessage: '基础示例执行成功！'
  })
  basicResult.value = result
  loading.value = false
}

const basicErrorExample = async () => {
  loading.value = true
  const result = await handleApiCall(() => mockApiError(1002), {
    showError: true
  })
  basicResult.value = result
  loading.value = false
}

// Composable 用法示例
const composableSuccessExample = async () => {
  const result = await apiHandler.execute(mockApiSuccess, {
    showSuccess: true,
    successMessage: 'Composable 示例执行成功！'
  })
  composableResult.value = result
}

const composableErrorExample = async () => {
  await apiHandler.execute(() => mockApiError(2005))
  composableResult.value = null
}

// 批量处理示例
const batchExample = async () => {
  batchLoading.value = true
  
  const apiCalls = [
    () => mockApiSuccess(),
    () => mockApiError(1001),
    () => mockApiSuccess(),
    () => mockApiError(2004),
    () => mockApiSuccess()
  ]
  
  const result = await handleBatchApiCalls(apiCalls, {
    showErrors: false,
    continueOnError: true
  })
  
  batchResult.value = result
  batchLoading.value = false
}

// 错误类型检查示例
const testAuthError = () => {
  const error = { code: 2004, msg: 'Token无效' }
  errorTypeResult.value = ErrorCodeHandler.handleApiError(error)
}

const testPermissionError = () => {
  const error = { code: 2005, msg: '无权限编辑' }
  errorTypeResult.value = ErrorCodeHandler.handleApiError(error)
}

const testDataError = () => {
  const error = { code: 1002, msg: '查询失败，参数错误' }
  errorTypeResult.value = ErrorCodeHandler.handleApiError(error)
}

const testSystemError = () => {
  const error = { code: -1, msg: '未知错误，请联系管理员' }
  errorTypeResult.value = ErrorCodeHandler.handleApiError(error)
}

definePageMeta({
  title: '错误处理示例'
})
</script>
