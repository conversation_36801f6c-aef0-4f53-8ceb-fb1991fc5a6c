<script setup lang="ts">
import { Button } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { useTicketOperations } from '~/composables/useTicketOperations'

// 页面标题
useHead({
  title: '工单通知测试',
})

const ticketOperations = useTicketOperations()

// 模拟工单数据
const mockTicket = {
  id: 'test-ticket-001',
  title: '测试工单',
  description: '这是一个测试工单',
  stage: '待处理',
  creator: 'test-user',
  handler: '',
  apps: ['TT语音'],
  createTime: new Date().toISOString(),
  updateTime: new Date().toISOString(),
}

// 测试各种工单操作的通知
async function testHandleTicket() {
  await ticketOperations.handleTicketAction(mockTicket)
}

async function testRejectTicket() {
  await ticketOperations.rejectTicketAction(mockTicket, '测试驳回原因')
}

async function testCheckTicket() {
  await ticketOperations.checkTicketAction(mockTicket)
}

async function testUrgeTicket() {
  await ticketOperations.urgeTicketAction(mockTicket)
}

async function testDeleteTicket() {
  await ticketOperations.deleteTicketAction(mockTicket)
}

async function testAssignTicket() {
  await ticketOperations.assignTicketAction(mockTicket, 'test-assignee', ['TT语音'])
}

async function testCompleteTicket() {
  await ticketOperations.completeTicketAction(
    mockTicket,
    '问题已解决，修复了相关bug',
    '代码逻辑错误导致的问题',
  )
}

async function testBatchOperation() {
  const tickets = [mockTicket, { ...mockTicket, id: 'test-ticket-002' }]
  await ticketOperations.batchOperation(
    tickets,
    ticket => ticketOperations.handleTicketAction(ticket),
    '认领',
  )
}
</script>

<template>
  <div class="mx-auto py-8 container space-y-8">
    <div class="text-center space-y-2">
      <h1 class="text-3xl font-bold">
        工单操作通知测试
      </h1>
      <p class="text-muted-foreground">
        测试工单操作的通知是否正确显示标题
      </p>
    </div>

    <Card>
      <CardHeader>
        <CardTitle>单个工单操作测试</CardTitle>
        <CardDescription>
          点击按钮测试不同工单操作的通知效果，检查是否显示正确的标题
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
          <Button variant="default" class="w-full" @click="testHandleTicket">
            认领工单
          </Button>

          <Button variant="outline" class="w-full" @click="testAssignTicket">
            指派工单
          </Button>

          <Button variant="default" class="w-full" @click="testCompleteTicket">
            完成工单
          </Button>

          <Button variant="outline" class="w-full" @click="testCheckTicket">
            归档工单
          </Button>

          <Button variant="destructive" class="w-full" @click="testRejectTicket">
            驳回工单
          </Button>

          <Button variant="secondary" class="w-full" @click="testUrgeTicket">
            加急工单
          </Button>

          <Button variant="destructive" class="w-full" @click="testDeleteTicket">
            删除工单
          </Button>

          <Button variant="outline" class="w-full" @click="testBatchOperation">
            批量操作
          </Button>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>测试说明</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="text-sm text-muted-foreground space-y-2">
          <p>• <strong>认领工单</strong>：应显示"工单操作"标题和"认领完成"消息</p>
          <p>• <strong>指派工单</strong>：应显示"工单操作"标题和"指派完成"消息</p>
          <p>• <strong>完成工单</strong>：应显示"操作成功"标题和"完成工单成功"消息</p>
          <p>• <strong>归档工单</strong>：应显示"工单操作"标题和"归档完成"消息</p>
          <p>• <strong>驳回工单</strong>：应显示"工单操作"标题和"驳回完成"消息</p>
          <p>• <strong>加急工单</strong>：应显示"工单操作"标题和"加急完成"消息</p>
          <p>• <strong>删除工单</strong>：应显示"工单操作"标题和"删除完成"消息</p>
          <p>• <strong>批量操作</strong>：应显示"批量操作"相关标题</p>
        </div>
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>模拟工单信息</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="text-sm space-y-2">
          <div><strong>工单ID:</strong> {{ mockTicket.id }}</div>
          <div><strong>标题:</strong> {{ mockTicket.title }}</div>
          <div><strong>状态:</strong> {{ mockTicket.stage }}</div>
          <div><strong>创建人:</strong> {{ mockTicket.creator }}</div>
          <div><strong>应用:</strong> {{ mockTicket.apps.join(', ') }}</div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
