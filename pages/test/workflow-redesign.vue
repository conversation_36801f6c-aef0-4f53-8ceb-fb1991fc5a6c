<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="text-center">
      <h1 class="text-3xl font-bold mb-2">重新设计的工单流程图</h1>
      <p class="text-muted-foreground">基于itmp-frontend的6步流程重新设计</p>
    </div>

    <!-- 流程概览 -->
    <Card>
      <CardHeader>
        <CardTitle>流程步骤概览</CardTitle>
        <CardDescription>新的6步工单处理流程</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div
            v-for="(step, index) in WORKFLOW_STEPS"
            :key="step.id"
            class="text-center p-4 rounded-lg border"
            :class="getStepCardClass(step.color)"
          >
            <div class="mb-2">
              <component
                :is="getStepIconComponent(step.icon)"
                class="h-8 w-8 mx-auto"
                :class="getStepIconColor(step.color)"
              />
            </div>
            <h3 class="font-medium text-sm mb-1">{{ step.title }}</h3>
            <p class="text-xs text-muted-foreground">{{ step.description }}</p>
            <div class="mt-2">
              <Badge variant="outline" class="text-xs">
                步骤 {{ index + 1 }}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 状态转换规则 -->
    <Card>
      <CardHeader>
        <CardTitle>状态转换规则</CardTitle>
        <CardDescription>各状态之间的转换关系和条件</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="rule in transitionRules"
            :key="`${rule.from}-${rule.action}-${rule.to}`"
            class="flex items-center justify-between p-3 border rounded-lg"
          >
            <div class="flex items-center space-x-4">
              <Badge :variant="getStatusVariant(rule.from)">{{ getStatusLabel(rule.from) }}</Badge>
              <ArrowRight class="h-4 w-4 text-muted-foreground" />
              <Badge :variant="getStatusVariant(rule.to)">{{ getStatusLabel(rule.to) }}</Badge>
            </div>
            <div class="text-right">
              <div class="font-medium text-sm">{{ rule.action }}</div>
              <div class="text-xs text-muted-foreground">{{ rule.description }}</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 示例工单流程展示 -->
    <Card>
      <CardHeader>
        <CardTitle>示例工单流程展示</CardTitle>
        <CardDescription>不同状态的工单流程图展示</CardDescription>
      </CardHeader>
      <CardContent class="space-y-8">
        <div
          v-for="ticket in sampleTickets"
          :key="ticket.id"
          class="space-y-4"
        >
          <div class="flex items-center justify-between">
            <h3 class="font-medium">{{ ticket.title }}</h3>
            <Badge :variant="getStatusVariant(getTicketStatus(ticket))">
              {{ getTicketStatus(ticket) }}
            </Badge>
          </div>
          
          <!-- 水平流程图 -->
          <TicketWorkflowSteps
            :ticket="ticket"
            orientation="horizontal"
            :show-progress="true"
            :show-timestamps="true"
            :show-operators="true"
          />
          
          <!-- 进度信息 -->
          <div class="text-sm text-muted-foreground">
            进度: {{ getProgressPercentage(ticket) }}% | 
            当前步骤: {{ getCurrentStepIndex(ticket) + 1 }}/{{ WORKFLOW_STEPS.length }}
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 垂直布局示例 -->
    <Card>
      <CardHeader>
        <CardTitle>垂直布局流程图</CardTitle>
        <CardDescription>垂直方向的流程展示</CardDescription>
      </CardHeader>
      <CardContent>
        <TicketWorkflowSteps
          :ticket="sampleTickets[1]"
          orientation="vertical"
          :show-progress="true"
          :show-timestamps="true"
          :show-operators="true"
        />
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ArrowRight, Archive, CheckCircle, Clock, FileText, Hand, Play } from 'lucide-vue-next'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import TicketWorkflowSteps from '~/components/feedback/TicketWorkflowSteps.vue'
import { useTicketWorkflow, TicketStatus } from '~/composables/useTicketWorkflow'
import { useTicketStatusTransition } from '~/composables/useTicketStatusTransition'
import type { Ticket } from '~/types/ticket'

const { 
  WORKFLOW_STEPS, 
  getCurrentStepIndex, 
  getProgressPercentage,
  getTicketStatus 
} = useTicketWorkflow()

const { transitionRules, getStatusDisplayInfo } = useTicketStatusTransition()

// 示例工单数据
const sampleTickets: Ticket[] = [
  {
    id: '1',
    title: '登录功能异常',
    stage: TicketStatus.PENDING,
    enterTime: '2024-01-15 09:00:00',
    feedbackPerson: '张三',
  },
  {
    id: '2', 
    title: '数据同步问题',
    stage: TicketStatus.PROCESSING,
    enterTime: '2024-01-14 14:30:00',
    responseTime: '2024-01-15 10:00:00',
    feedbackPerson: '李四',
    handler: '王五',
  },
  {
    id: '3',
    title: '界面显示错误',
    stage: TicketStatus.PROCESSED,
    enterTime: '2024-01-13 16:00:00',
    responseTime: '2024-01-14 09:00:00',
    endTime: '2024-01-15 11:30:00',
    feedbackPerson: '赵六',
    handler: '钱七',
  },
  {
    id: '4',
    title: '性能优化需求',
    stage: TicketStatus.ARCHIVED,
    enterTime: '2024-01-10 10:00:00',
    responseTime: '2024-01-11 14:00:00',
    endTime: '2024-01-12 16:00:00',
    feedbackPerson: '孙八',
    handler: '周九',
  },
  {
    id: '5',
    title: '功能需求变更',
    stage: TicketStatus.REJECTED,
    enterTime: '2024-01-12 11:00:00',
    responseTime: '2024-01-13 15:00:00',
    feedbackPerson: '吴十',
    handler: '郑十一',
  },
]

// 获取步骤图标组件
function getStepIconComponent(iconName: string) {
  const iconMap = {
    FileText,
    Clock,
    Hand,
    Play,
    CheckCircle,
    Archive,
  }
  return iconMap[iconName as keyof typeof iconMap] || Clock
}

// 获取步骤卡片样式
function getStepCardClass(color: string) {
  const colorMap = {
    blue: 'border-blue-200 bg-blue-50',
    orange: 'border-orange-200 bg-orange-50',
    yellow: 'border-yellow-200 bg-yellow-50',
    green: 'border-green-200 bg-green-50',
    gray: 'border-gray-200 bg-gray-50',
  }
  return colorMap[color as keyof typeof colorMap] || 'border-gray-200 bg-gray-50'
}

// 获取步骤图标颜色
function getStepIconColor(color: string) {
  const colorMap = {
    blue: 'text-blue-600',
    orange: 'text-orange-600',
    yellow: 'text-yellow-600',
    green: 'text-green-600',
    gray: 'text-gray-600',
  }
  return colorMap[color as keyof typeof colorMap] || 'text-gray-600'
}

// 获取状态标签
function getStatusLabel(status: string) {
  return getStatusDisplayInfo(status).label
}

// 获取状态变体
function getStatusVariant(status: string) {
  const color = getStatusDisplayInfo(status).color
  const variantMap = {
    blue: 'default',
    orange: 'secondary',
    yellow: 'outline',
    green: 'default',
    gray: 'secondary',
    red: 'destructive',
  }
  return variantMap[color as keyof typeof variantMap] || 'secondary'
}

// 设置页面标题
useHead({
  title: '重新设计的工单流程图 - 测试页面',
})
</script>
