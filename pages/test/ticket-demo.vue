<script setup lang="ts">
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { UserSelect } from '@/components/ui/user-select'
import { Calendar, Clock, MessageSquare, Paperclip, User } from 'lucide-vue-next'

// 页面标题
useHead({
  title: 'Ticket Demo - Linear User Select',
})

// 模拟工单数据
const ticket = ref({
  id: 'TICKET-123',
  title: '登录页面无法正常显示验证码',
  description: '用户反馈在登录页面点击验证码图片后，验证码无法刷新，导致无法正常登录系统。',
  status: 'open',
  priority: 'high',
  assignee: '',
  reviewers: [],
  watchers: [],
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-15T14:20:00Z',
  reporter: '<EMAIL>',
})

// 状态选项
const statusOptions = [
  { value: 'open', label: 'Open', color: 'bg-blue-500' },
  { value: 'in-progress', label: 'In Progress', color: 'bg-yellow-500' },
  { value: 'review', label: 'Review', color: 'bg-purple-500' },
  { value: 'done', label: 'Done', color: 'bg-green-500' },
  { value: 'closed', label: 'Closed', color: 'bg-gray-500' },
]

// 优先级选项
const priorityOptions = [
  { value: 'low', label: 'Low', color: 'bg-gray-500' },
  { value: 'medium', label: 'Medium', color: 'bg-blue-500' },
  { value: 'high', label: 'High', color: 'bg-orange-500' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-500' },
]

// 获取状态信息
const currentStatus = computed(() => {
  return statusOptions.find(s => s.value === ticket.value.status) || statusOptions[0]
})

// 获取优先级信息
const currentPriority = computed(() => {
  return priorityOptions.find(p => p.value === ticket.value.priority) || priorityOptions[0]
})

// 事件处理
function handleAssigneeChange(value: string | string[], users: any) {
  console.log('Assignee changed:', { value, users })
  ticket.value.assignee = Array.isArray(value) ? value[0] : value
}

function handleReviewersChange(value: string | string[], users: any) {
  console.log('Reviewers changed:', { value, users })
  ticket.value.reviewers = Array.isArray(value) ? value : [value].filter(Boolean)
}

function handleWatchersChange(value: string | string[], users: any) {
  console.log('Watchers changed:', { value, users })
  ticket.value.watchers = Array.isArray(value) ? value : [value].filter(Boolean)
}

function handleInviteUser(searchValue: string) {
  console.log('Invite user:', searchValue)
  // 这里可以实现邀请用户的逻辑
}

function handleNoAssignee() {
  console.log('No assignee selected')
  ticket.value.assignee = ''
}

function saveTicket() {
  console.log('Saving ticket:', ticket.value)
  // 这里可以实现保存工单的逻辑
}

// 格式化日期
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}
</script>

<template>
  <div class="mx-auto max-w-4xl p-6 container">
    <!-- 页面标题 -->
    <div class="mb-6">
      <div class="mb-2 flex items-center gap-2 text-sm text-muted-foreground">
        <span>Tickets</span>
        <span>/</span>
        <span>{{ ticket.id }}</span>
      </div>
      <h1 class="text-2xl font-bold">
        {{ ticket.title }}
      </h1>
    </div>

    <div class="grid gap-6 lg:grid-cols-3">
      <!-- 主要内容 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 工单详情 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <MessageSquare class="h-5 w-5" />
              工单详情
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div>
              <Label for="title">标题</Label>
              <Input id="title" v-model="ticket.title" class="mt-1" />
            </div>

            <div>
              <Label for="description">描述</Label>
              <Textarea
                id="description"
                v-model="ticket.description"
                class="mt-1 min-h-[100px]"
                placeholder="请描述问题详情..."
              />
            </div>

            <div class="flex gap-4">
              <Button @click="saveTicket">
                保存更改
              </Button>
              <Button variant="outline">
                <Paperclip class="mr-2 h-4 w-4" />
                添加附件
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- 评论区域 -->
        <Card>
          <CardHeader>
            <CardTitle>评论</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="py-8 text-center text-muted-foreground">
              暂无评论
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- 侧边栏 -->
      <div class="space-y-6">
        <!-- 状态和优先级 -->
        <Card>
          <CardHeader>
            <CardTitle>状态</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex items-center gap-2">
              <div :class="cn('w-2 h-2 rounded-full', currentStatus.color)" />
              <Badge variant="outline">
                {{ currentStatus.label }}
              </Badge>
            </div>

            <Separator />

            <div>
              <Label class="text-sm font-medium">优先级</Label>
              <div class="mt-1 flex items-center gap-2">
                <div :class="cn('w-2 h-2 rounded-full', currentPriority.color)" />
                <Badge variant="outline">
                  {{ currentPriority.label }}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 人员分配 -->
        <Card>
          <CardHeader>
            <CardTitle>人员分配</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- 指派人 -->
            <div>
              <Label class="text-sm font-medium">指派人</Label>
              <div class="mt-1">
                <UserSelect
                  v-model="ticket.assignee"
                  placeholder="No assignee"
                  size="sm"
                  :show-no-assignee="true"
                  :show-invite-user="true"
                  :virtual-threshold="30"
                  :item-height="56"
                  :list-height="250"
                  @change="handleAssigneeChange"
                  @invite-user="handleInviteUser"
                  @no-assignee="handleNoAssignee"
                />
              </div>
            </div>

            <!-- 审核人 -->
            <div>
              <Label class="text-sm font-medium">审核人</Label>
              <div class="mt-1">
                <UserSelect
                  v-model="ticket.reviewers"
                  multiple
                  placeholder="No reviewers"
                  size="sm"
                  :show-no-assignee="false"
                  :show-invite-user="true"
                  :virtual-threshold="30"
                  :item-height="56"
                  :list-height="250"
                  @change="handleReviewersChange"
                  @invite-user="handleInviteUser"
                />
              </div>
            </div>

            <!-- 关注者 -->
            <div>
              <Label class="text-sm font-medium">关注者</Label>
              <div class="mt-1">
                <UserSelect
                  v-model="ticket.watchers"
                  multiple
                  placeholder="No watchers"
                  size="sm"
                  :show-no-assignee="false"
                  :show-invite-user="true"
                  :virtual-threshold="30"
                  :item-height="56"
                  :list-height="250"
                  @change="handleWatchersChange"
                  @invite-user="handleInviteUser"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- 时间信息 -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Clock class="h-4 w-4" />
              时间信息
            </CardTitle>
          </CardHeader>
          <CardContent class="text-sm space-y-3">
            <div class="flex justify-between">
              <span class="text-muted-foreground">创建时间</span>
              <span>{{ formatDate(ticket.createdAt) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">更新时间</span>
              <span>{{ formatDate(ticket.updatedAt) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">报告人</span>
              <span>{{ ticket.reporter }}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 可以在这里添加自定义样式 */
</style>
