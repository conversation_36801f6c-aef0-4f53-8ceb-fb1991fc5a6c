<script setup lang="ts">
import type { Ticket } from '~/types/ticket'
import Layout from '~/components/feedback/Layout.vue'

// 页面配置
definePageMeta({
  title: '工单选择测试',
  layout: false,
})

// 模拟工单数据
const mockTickets: Ticket[] = [
  {
    id: '1749714907000030',
    ticketID: '1749714907000030',
    title: '测试工单1',
    text: '这是第一个测试工单',
    status: '待处理',
    stage: '待处理',
    creator: 'user1',
    feedbackPerson: 'user1',
    handler: '',
    devProcessor: '',
    createdAt: '2024-01-01 10:00:00',
    enterTime: '2024-01-01 10:00:00',
  },
  {
    id: '1749714907000031',
    ticketID: '1749714907000031',
    title: '测试工单2',
    text: '这是第二个测试工单',
    status: '处理中',
    stage: '处理中',
    creator: 'user2',
    feedbackPerson: 'user2',
    handler: 'admin',
    devProcessor: 'admin',
    createdAt: '2024-01-02 11:00:00',
    enterTime: '2024-01-02 11:00:00',
  },
  {
    id: '1749714907000032',
    ticketID: '1749714907000032',
    title: '测试工单3',
    text: '这是第三个测试工单',
    status: '已处理',
    stage: '已处理',
    creator: 'user3',
    feedbackPerson: 'user3',
    handler: 'admin',
    devProcessor: 'admin',
    createdAt: '2024-01-03 12:00:00',
    enterTime: '2024-01-03 12:00:00',
  },
]

// 响应式数据
const tickets = ref<Ticket[]>(mockTickets)
const activeMenu = ref('all')
const selectedTicketId = ref('')
const loading = ref(false)
const error = ref(null)

// 从路由参数获取工单ID
const route = useRoute()
const routeTicketId = computed(() => {
  return route.query.ticketId as string || ''
})

// 处理菜单变化
function handleMenuChange(menu: string) {
  activeMenu.value = menu
  console.log('菜单切换到:', menu)
  
  // 模拟不同菜单的工单数据
  if (menu === 'all') {
    tickets.value = mockTickets
  } else if (menu === 'pending') {
    tickets.value = mockTickets.filter(t => t.status === '待处理')
  } else if (menu === 'processing') {
    tickets.value = mockTickets.filter(t => t.status === '处理中')
  } else if (menu === 'done') {
    tickets.value = mockTickets.filter(t => t.status === '已处理')
  } else if (menu === 'created') {
    tickets.value = mockTickets.filter(t => t.creator === 'user1')
  } else if (menu === 'todo') {
    tickets.value = [] // 模拟空列表
  } else {
    tickets.value = []
  }
}

// 处理工单创建
function handleTicketCreated(ticket: Ticket) {
  tickets.value.unshift(ticket)
}

// 处理刷新
function handleRefresh() {
  console.log('刷新工单列表')
  // 重新加载当前菜单的数据
  handleMenuChange(activeMenu.value)
}

// 处理工单操作刷新
function handleTicketActionRefresh() {
  console.log('工单操作后刷新')
  handleRefresh()
}

// 处理当前工单刷新
function handleRefreshCurrentTicket() {
  console.log('刷新当前工单')
  handleRefresh()
}

// 测试函数
function testDirectAccess() {
  // 模拟直接访问工单详情页
  const testTicketId = '1749714907000030'
  selectedTicketId.value = testTicketId
  console.log('测试直接访问工单:', testTicketId)
}

function testEmptyList() {
  // 测试空列表情况
  tickets.value = []
  console.log('测试空列表情况')
}

function testMenuSwitch() {
  // 测试菜单切换
  handleMenuChange('todo') // 切换到空列表菜单
  console.log('测试菜单切换到空列表')
}

onMounted(() => {
  // 如果URL中有工单ID，设置选中状态
  if (routeTicketId.value) {
    selectedTicketId.value = routeTicketId.value
  }
})
</script>

<template>
  <div class="p-4">
    <div class="mb-4 space-y-2">
      <h1 class="text-2xl font-bold">工单选择测试页面</h1>
      <p class="text-muted-foreground">
        测试工单选择逻辑：直接访问、菜单切换、空列表处理
      </p>
      
      <!-- 测试按钮 -->
      <div class="flex gap-2 mb-4">
        <Button @click="testDirectAccess">
          测试直接访问工单
        </Button>
        <Button @click="testEmptyList" variant="outline">
          测试空列表
        </Button>
        <Button @click="testMenuSwitch" variant="outline">
          测试菜单切换
        </Button>
        <Button @click="handleRefresh" variant="outline">
          重置数据
        </Button>
      </div>
      
      <!-- 状态显示 -->
      <div class="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
        <div>
          <strong>当前菜单:</strong> {{ activeMenu }}
        </div>
        <div>
          <strong>选中工单:</strong> {{ selectedTicketId || '无' }}
        </div>
        <div>
          <strong>工单总数:</strong> {{ tickets.length }}
        </div>
        <div>
          <strong>路由工单ID:</strong> {{ routeTicketId || '无' }}
        </div>
      </div>
    </div>

    <!-- 工单布局 -->
    <div class="h-[600px] border rounded-lg">
      <Layout
        :tickets="tickets"
        :loading="loading"
        :error="error"
        :nav-collapsed-size="4"
        :active-menu="activeMenu"
        :selected-ticket-id="selectedTicketId"
        @update:active-menu="handleMenuChange"
        @ticket-created="handleTicketCreated"
        @refresh-tickets="handleRefresh"
        @ticket-action-refresh="handleTicketActionRefresh"
        @refresh-current-ticket="handleRefreshCurrentTicket"
      />
    </div>
  </div>
</template>
