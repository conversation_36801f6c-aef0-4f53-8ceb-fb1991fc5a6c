<script setup lang="ts">
import DateTimePicker from '@/components/ui/date-time-picker/DateTimePicker.vue'

// 测试数据
const basicDateTime = ref('')
const presetDateTime = ref('2024-12-20T14:30')
const formData = ref({
  startTime: '',
  endTime: '',
  reminderTime: '',
})

// 处理变化
function handleBasicChange(value: string) {
  console.log('Basic DateTime changed:', value)
}

function handlePresetChange(value: string) {
  console.log('Preset DateTime changed:', value)
}

// 表单提交
function handleSubmit() {
  console.log('Form submitted:', formData.value)
  alert(`表单提交成功！\n${JSON.stringify(formData.value, null, 2)}`)
}

// 重置表单
function resetForm() {
  formData.value = {
    startTime: '',
    endTime: '',
    reminderTime: '',
  }
  basicDateTime.value = ''
}

// 设置示例数据
function setExampleData() {
  const now = new Date()
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
  
  basicDateTime.value = now.toISOString().slice(0, 16)
  formData.value.startTime = now.toISOString().slice(0, 16)
  formData.value.endTime = tomorrow.toISOString().slice(0, 16)
  formData.value.reminderTime = nextWeek.toISOString().slice(0, 16)
}
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold tracking-tight">
        增强版日期时间选择器
      </h1>
      <p class="text-muted-foreground">
        基于 @vuepic/vue-datepicker 的增强版组件，支持键盘输入、快捷选项和美化样式
      </p>
    </div>

    <!-- 基础功能演示 -->
    <Card>
      <CardHeader>
        <CardTitle>基础功能演示</CardTitle>
        <CardDescription>
          展示键盘输入、快捷选项和基本交互功能
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid gap-6 md:grid-cols-2">
          <div class="space-y-3">
            <Label for="basic">基础选择器</Label>
            <DateTimePicker
              id="basic"
              v-model="basicDateTime"
              placeholder="支持键盘输入：YYYY-MM-DD HH:mm"
              @update:model-value="handleBasicChange"
            />
            <div class="text-xs text-muted-foreground space-y-1">
              <p>• 点击输入框直接键盘输入</p>
              <p>• 点击日历图标打开选择器</p>
              <p>• 支持快捷选项：此刻、1小时后等</p>
            </div>
          </div>

          <div class="space-y-3">
            <Label for="preset">预设值选择器</Label>
            <DateTimePicker
              id="preset"
              v-model="presetDateTime"
              placeholder="带预设值的选择器"
              @update:model-value="handlePresetChange"
            />
            <div class="text-xs text-muted-foreground space-y-1">
              <p>• 初始值：2024-12-20 14:30</p>
              <p>• 可以清空或修改</p>
              <p>• 支持所有交互功能</p>
            </div>
          </div>
        </div>

        <div class="grid gap-4 md:grid-cols-2">
          <div class="space-y-2">
            <Label>基础选择器当前值</Label>
            <div class="rounded-md bg-muted p-3 font-mono text-sm">
              {{ basicDateTime || '(空)' }}
            </div>
          </div>
          <div class="space-y-2">
            <Label>预设选择器当前值</Label>
            <div class="rounded-md bg-muted p-3 font-mono text-sm">
              {{ presetDateTime || '(空)' }}
            </div>
          </div>
        </div>

        <div class="flex gap-2">
          <Button @click="setExampleData">
            设置示例数据
          </Button>
          <Button variant="outline" @click="resetForm">
            重置所有
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 表单集成演示 -->
    <Card>
      <CardHeader>
        <CardTitle>表单集成演示</CardTitle>
        <CardDescription>
          在实际表单中的使用效果，包含验证和提交功能
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid gap-4 md:grid-cols-2">
            <div class="space-y-2">
              <Label for="startTime" class="required">开始时间</Label>
              <DateTimePicker
                id="startTime"
                v-model="formData.startTime"
                placeholder="请选择开始时间"
                required
              />
            </div>

            <div class="space-y-2">
              <Label for="endTime" class="required">结束时间</Label>
              <DateTimePicker
                id="endTime"
                v-model="formData.endTime"
                placeholder="请选择结束时间"
                required
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="reminderTime">提醒时间（可选）</Label>
            <DateTimePicker
              id="reminderTime"
              v-model="formData.reminderTime"
              placeholder="请选择提醒时间"
            />
          </div>

          <div class="flex gap-2">
            <Button 
              type="submit" 
              :disabled="!formData.startTime || !formData.endTime"
            >
              提交表单
            </Button>
            <Button type="button" variant="outline" @click="resetForm">
              重置表单
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- 状态演示 -->
    <Card>
      <CardHeader>
        <CardTitle>状态演示</CardTitle>
        <CardDescription>
          不同状态下的组件表现
        </CardDescription>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="grid gap-4 md:grid-cols-3">
          <div class="space-y-2">
            <Label>正常状态</Label>
            <DateTimePicker
              placeholder="正常状态"
            />
          </div>

          <div class="space-y-2">
            <Label>禁用状态</Label>
            <DateTimePicker
              model-value="2024-12-20T14:30"
              placeholder="禁用状态"
              disabled
            />
          </div>

          <div class="space-y-2">
            <Label>必填状态</Label>
            <DateTimePicker
              placeholder="必填字段"
              required
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 功能特性说明 -->
    <Card>
      <CardHeader>
        <CardTitle>功能特性</CardTitle>
        <CardDescription>
          增强版日期时间选择器的主要特性和优势
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-6 md:grid-cols-2">
          <div class="space-y-4">
            <h4 class="font-semibold text-sm">交互功能</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>支持键盘直接输入日期时间</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>点击日历图标打开选择器</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>内置清空按钮，快速清除</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-green-500"></div>
                <span>焦点状态视觉反馈</span>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <h4 class="font-semibold text-sm">快捷选项</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>此刻 - 当前时间</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>1小时后 - 当前时间+1小时</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>明天此时 - 当前时间+24小时</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                <span>下周此时 - 当前时间+7天</span>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <h4 class="font-semibold text-sm">样式特性</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-purple-500"></div>
                <span>完全兼容 shadcn-ui 设计系统</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-purple-500"></div>
                <span>支持明暗主题自动切换</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-purple-500"></div>
                <span>平滑的动画和过渡效果</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-purple-500"></div>
                <span>美化的日历界面和交互</span>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <h4 class="font-semibold text-sm">技术特性</h4>
            <div class="space-y-2 text-sm">
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>基于成熟的 @vuepic/vue-datepicker</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>TypeScript 类型支持</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>无障碍访问支持</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="h-2 w-2 rounded-full bg-orange-500"></div>
                <span>移动端友好的响应式设计</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
.required::after {
  content: ' *';
  color: hsl(var(--destructive));
}
</style>
