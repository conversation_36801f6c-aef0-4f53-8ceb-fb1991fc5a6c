# 聊天功能模块

这个目录包含了完整的聊天功能实现，包括聊天主页面、详情页面、设置页面和可嵌入的聊天组件。

## 目录结构

```
pages/chat/
├── index.vue          # 聊天主页面 - 双栏布局，聊天列表和消息区域
├── [id].vue          # 聊天详情页面 - 单个聊天的全屏视图
├── settings.vue      # 聊天设置页面 - 通知、外观、隐私等设置
└── README.md         # 本文档

components/chat/
└── ChatWidget.vue    # 可嵌入的聊天组件 - 固定在页面右下角
```

## 功能特性

### 1. 聊天主页面 (`/chat`)

- **双栏布局**：左侧聊天列表，右侧消息区域
- **可调整面板**：用户可以调整左右面板的大小
- **可折叠侧边栏**：左侧聊天列表支持折叠
- **搜索功能**：可以搜索聊天列表中的联系人
- **在线状态**：显示用户在线/离线状态
- **未读消息**：显示未读消息数量
- **双击跳转**：双击聊天项可跳转到详情页面
- **高度自适应**：页面高度自动适应屏幕大小

### 2. 聊天详情页面 (`/chat/[id]`)

- **全屏聊天**：专注的聊天体验
- **返回按钮**：可以返回聊天列表
- **独立消息**：每个聊天的消息完全独立
- **实时滚动**：新消息自动滚动到底部
- **模拟回复**：发送消息后会收到模拟回复

### 3. 聊天设置页面 (`/chat/settings`)

- **通知设置**：控制消息通知、声音提醒等
- **外观设置**：主题、字体大小、消息气泡样式
- **隐私设置**：已读回执、最后在线时间、正在输入状态
- **存储管理**：缓存清理、数据导出等

### 4. 聊天组件 (`ChatWidget`)

- **可嵌入**：可以在任何页面中使用
- **固定定位**：固定在页面右下角
- **最小化/最大化**：支持收起和展开
- **独立状态**：不影响页面其他功能
- **响应式设计**：适配不同屏幕尺寸

## 技术实现

### 数据隔离

每个聊天的消息数据完全独立存储，避免了聊天内容共享的问题：

```typescript
// 每个聊天的消息数据独立存储
const chatMessages = ref<Record<number, any[]>>({
  1: [...], // 张三的消息
  2: [...], // 李四的消息
  3: [...], // 王五的消息
})
```

### 高度自适应

使用 Flexbox 布局和视口高度单位实现完美的高度自适应：

```vue
<template>
  <div class="h-screen flex flex-col">
    <div class="flex-shrink-0">
      <!-- 固定头部 -->
    </div>
    <div class="min-h-0 flex-1">
      <!-- 可伸缩内容 -->
    </div>
  </div>
</template>
```

### 响应式设计

- 使用 ResizablePanel 组件实现可调整的面板
- 支持面板折叠和展开
- 移动端友好的交互设计

## 使用方法

### 1. 通过左侧菜单访问

在左侧菜单栏中，您可以找到"聊天功能"分组，包含以下入口：

- **聊天主页** - 主要的聊天界面，支持多人对话管理
- **聊天设置** - 个性化聊天体验设置
- **聊天组件演示** - 查看可嵌入聊天组件的使用示例

### 2. 直接访问聊天页面

```
http://localhost:3456/chat           # 聊天主页面
http://localhost:3456/chat/1         # 与用户1的聊天详情
http://localhost:3456/chat/settings  # 聊天设置
```

### 3. 使用聊天组件

在任何页面中嵌入聊天组件：

```vue
<script setup>
const showChat = ref(false)
</script>

<template>
  <div>
    <!-- 页面内容 -->
    <div>...</div>

    <!-- 聊天组件 -->
    <ChatWidget
      v-if="showChat"
      :user-id="1"
      user-name="客服"
      user-avatar="/avatars/01.png"
      @close="showChat = false"
    />
  </div>
</template>
```

### 4. 测试页面

访问测试页面查看聊天组件的使用示例：

```
http://localhost:3456/test/chat-widget
```

## 自定义配置

### 聊天组件属性

| 属性         | 类型      | 默认值              | 说明             |
| ------------ | --------- | ------------------- | ---------------- |
| `userId`     | `number`  | `1`                 | 聊天对象的用户ID |
| `userName`   | `string`  | `'客服'`            | 聊天对象的用户名 |
| `userAvatar` | `string`  | `'/avatars/01.png'` | 聊天对象的头像   |
| `minimized`  | `boolean` | `false`             | 是否最小化显示   |

### 聊天组件事件

| 事件        | 说明                 |
| ----------- | -------------------- |
| `@close`    | 关闭聊天组件时触发   |
| `@minimize` | 最小化聊天组件时触发 |
| `@maximize` | 最大化聊天组件时触发 |

## 扩展功能

可以根据需要扩展以下功能：

1. **真实的WebSocket连接**：替换模拟数据
2. **文件发送**：支持图片、文档等文件传输
3. **表情符号**：添加表情选择器
4. **消息状态**：已发送、已读等状态显示
5. **群聊功能**：支持多人聊天
6. **消息搜索**：在聊天记录中搜索
7. **消息撤回**：撤回已发送的消息
8. **语音消息**：支持语音录制和播放

## 注意事项

1. **数据持久化**：当前使用内存存储，刷新页面会丢失数据
2. **用户认证**：需要配合认证中间件使用
3. **性能优化**：大量消息时需要考虑虚拟滚动
4. **移动端适配**：在小屏幕设备上的交互优化
