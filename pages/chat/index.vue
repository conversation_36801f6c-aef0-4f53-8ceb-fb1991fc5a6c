<script setup lang="ts">
import { MessageCircle, MoreVertical, Phone, RotateCcw, Search, Send, Users, Video } from 'lucide-vue-next'

// 页面元数据
definePageMeta({
  title: '聊天',
  middleware: 'auth',
})

// 统一的角色数据定义
const CHARACTERS = {
  jarvis: {
    id: 'jarvis',
    chatId: 2,
    name: '<PERSON>',
    avatar: '/avatars/jarvis.png',
    role: 'AI助手',
    status: 'AI助手',
    fallbackText: 'AI',
  },
  zhangsan: {
    id: 'zhangsan',
    chatId: 3,
    name: '张三',
    avatar: '/avatars/kapi3.png',
    role: '运维工程师',
    status: '运维工程师',
    fallbackText: '张',
  },
  lisi: {
    id: 'lisi',
    chatId: 4,
    name: '李四',
    avatar: '/avatars/kapi6.png',
    role: '开发工程师',
    status: '开发工程师',
    fallbackText: '李',
  },
  wangwu: {
    id: 'wangwu',
    chatId: 5,
    name: '王五',
    avatar: '/avatars/kapi5.png',
    role: '测试工程师',
    status: '测试工程师',
    fallbackText: '王',
  },
  zhaoliu: {
    id: 'zhaoliu',
    chatId: 6,
    name: '赵六',
    avatar: '/avatars/kapi2.png',
    role: '产品经理',
    status: '产品经理',
    fallbackText: '赵',
  },
  me: {
    id: 'me',
    chatId: 7,
    name: '我',
    avatar: '/avatars/kapi0.png',
    role: '个人笔记',
    status: '个人笔记',
    fallbackText: '我',
  },
}

// 根据角色ID获取角色信息的辅助函数
const getCharacter = (characterId: string) => CHARACTERS[characterId as keyof typeof CHARACTERS]
const getCharacterByChatId = (chatId: number) => Object.values(CHARACTERS).find(char => char.chatId === chatId)

// 创建消息的辅助函数
function createMessage(characterId: string, content: string, time?: string) {
  const character = getCharacter(characterId)
  if (!character)
    throw new Error(`Character ${characterId} not found`)

  const now = new Date()
  return {
    id: Date.now() + Math.random(), // 临时ID，实际使用时会被重新分配
    senderId: character.id,
    senderName: character.name,
    content,
    time: time || now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    timestamp: now.getTime(), // 添加时间戳
    type: 'text',
    avatar: character.avatar,
  }
}

// 创建聊天列表的辅助函数
function createChatFromCharacter(characterId: string, lastMessage: string, time: string, unread = 0) {
  const character = getCharacter(characterId)
  if (!character)
    throw new Error(`Character ${characterId} not found`)

  return {
    id: character.chatId,
    name: character.name,
    avatar: character.avatar,
    lastMessage,
    time,
    unread,
    isGroup: false,
    memberCount: characterId === 'me' ? 1 : 2,
    status: character.status,
  }
}

// 模拟聊天数据
const chatList = ref([
  createChatFromCharacter('jarvis', '你好，有什么可以帮助您的吗？', '20:30'),
  createChatFromCharacter('zhangsan', '服务器状态检查完毕', '20:25'),
  createChatFromCharacter('lisi', '代码审查已完成', '20:20'),
  createChatFromCharacter('wangwu', '测试用例执行中', '20:15'),
  createChatFromCharacter('zhaoliu', '需求文档已更新', '20:10'),
  createChatFromCharacter('me', '这是我的个人笔记', '20:05'),
])

// 每个聊天的消息数据 - 独立存储
const chatMessages = ref<Record<number, any[]>>({
  1: [
    {
      id: 1,
      type: 'system',
      content: '欢迎加入群组',
      subContent: '你可以尝试以下操作，快速开始协作',
      time: '',
      actions: [
        { text: '置顶重要链接', type: 'secondary' },
        { text: '完善群信息', type: 'secondary' },
        { text: '完善群公告', type: 'secondary' },
      ],
    },
    {
      id: 2,
      type: 'system',
      content: 'Jarvis 创建群聊，并邀请 张三、李四、王五、赵六 加入群聊',
      time: '',
      mentions: ['Jarvis', '张三', '李四', '王五', '赵六'],
    },
    { ...createMessage('jarvis', '检测到用户无法进入房间的故障，已创建此群聊进行协作处理。', '20:31'), id: 3 },
    { ...createMessage('zhangsan', '收到，我这边检查一下服务器状态。', '20:32'), id: 4 },
    { ...createMessage('lisi', '我查看一下代码逻辑，看是否有bug。', '20:33'), id: 5 },
    { ...createMessage('wangwu', '我这边准备测试用例，验证修复效果。', '20:34'), id: 6 },
    { ...createMessage('zhaoliu', '好的，我联系用户了解具体情况，大家辛苦了！', '20:35'), id: 7 },
  ],
  2: [
    { ...createMessage('jarvis', '你好，有什么可以帮助您的吗？', '20:30'), id: 1 },
  ],
  3: [
    { ...createMessage('zhangsan', '你好，我是运维工程师张三，有什么服务器问题需要我协助吗？', '20:25'), id: 1 },
  ],
  4: [
    { ...createMessage('lisi', '嗨，我是开发工程师李四，有代码相关的问题可以找我。', '20:20'), id: 1 },
  ],
  5: [
    { ...createMessage('wangwu', '你好，我是测试工程师王五，负责质量保证工作。', '20:15'), id: 1 },
  ],
  6: [
    { ...createMessage('zhaoliu', '大家好，我是产品经理赵六，有产品需求问题可以联系我。', '20:10'), id: 1 },
  ],
  7: [
    { ...createMessage('me', '这里是我的个人笔记空间，可以记录一些想法和备忘。', '20:05'), id: 1 },
  ],
})

// 状态管理
const selectedChat = ref<any>(null)
const newMessage = ref('')
const searchValue = ref('')
const isCollapsed = ref(false)

// 预设输入内容
const presetMessages = ref([
  {
    id: 1,
    text: '出故障了，用户无法进入房间',
    icon: '⚠️',
  },
])

// 故障处理群聊快捷回复选项
const faultGroupQuickReplies = ref([
  {
    id: 1,
    text: '@Jarvis 打电话给张工',
    icon: '📞',
    description: '云呼叫工具',
  },
  {
    id: 2,
    text: '@Jarvis account服务最近有没有变更',
    icon: '🔍',
    description: '变更检查',
  },
  {
    id: 3,
    text: '@Jarvis account服务是否正常',
    icon: '📊',
    description: '状态检查',
  },
  {
    id: 4,
    text: '@Jarvis 故障结束',
    icon: '✅',
    description: '结束流程',
  },
])

// 是否显示预设消息
const showPresetMessages = computed(() => {
  return selectedChat.value?.id === 2 && selectedChat.value?.name === 'Jarvis'
})

// 是否显示故障群聊快捷回复
const showFaultGroupQuickReplies = computed(() => {
  return selectedChat.value?.id === 1 && selectedChat.value?.isGroup
})

// 计算属性
const filteredChatList = computed(() => {
  if (!searchValue.value)
    return chatList.value
  return chatList.value.filter(chat =>
    chat.name.toLowerCase().includes(searchValue.value.toLowerCase()),
  )
})

const currentMessages = computed(() => {
  if (!selectedChat.value)
    return []
  return chatMessages.value[selectedChat.value.id] || []
})

// 动态placeholder
const inputPlaceholder = computed(() => {
  if (!selectedChat.value)
    return '请选择一个聊天'

  if (selectedChat.value.id === 2) {
    // Jarvis聊天
    return '发送给 Jarvis '
  }
  else if (selectedChat.value.isGroup) {
    // 群聊 - 根据群聊名称提供不同的placeholder
    if (selectedChat.value.name.includes('故障')) {
      return `在故障处理群中发送消息...`
    }
    else {
      return `发送消息到 ${selectedChat.value.name}`
    }
  }
  else {
    // 其他单聊
    return `发送消息给 ${selectedChat.value.name}`
  }
})

// 消息队列发送器
function sendMessagesInQueue(messages: Array<{
  content: string
  type?: 'text' | 'interactive_card'
  actions?: Array<{ id: string, text: string, type: string, icon?: string }>
  chatId?: number
  senderId?: string
}>, startDelay = 1000) {
  messages.forEach((messageConfig, index) => {
    setTimeout(() => {
      const targetChatId = messageConfig.chatId || selectedChat.value?.id
      if (!targetChatId)
        return

      const senderId = messageConfig.senderId || 'jarvis'
      const character = getCharacter(senderId)

      const now = new Date()
      const message = {
        id: (chatMessages.value[targetChatId]?.length || 0) + 1,
        senderId,
        senderName: character?.name || 'Jarvis',
        content: messageConfig.content,
        time: now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        timestamp: now.getTime(), // 添加时间戳用于分析
        type: messageConfig.type || 'text',
        avatar: character?.avatar || '/avatars/jarvis.png',
        ...(messageConfig.actions && { actions: messageConfig.actions }),
      }

      // 确保聊天消息数组存在
      if (!chatMessages.value[targetChatId]) {
        chatMessages.value[targetChatId] = []
      }

      chatMessages.value[targetChatId].push(message)

      // 更新聊天列表
      const chatIndex = chatList.value.findIndex(c => c.id === targetChatId)
      if (chatIndex !== -1) {
        chatList.value[chatIndex].lastMessage = messageConfig.type === 'interactive_card'
          ? `${messageConfig.content.substring(0, 30)}...`
          : messageConfig.content
        chatList.value[chatIndex].time = message.time
      }
    }, startDelay + (index * 2000)) // 每条消息间隔2秒
  })
}

// 模拟故障处理流程 - 体现AI Agent自主规划能力 (已废弃)
/* function _simulateFaultHandlingProcess() {
  const faultHandlingMessages = [
    // 第1阶段：AI Agent自主规划和目标设定 (1-6秒)
    { content: '🎯 AI Agent启动故障处理模式，设定目标：快速恢复用户房间访问功能', senderId: 'jarvis', chatId: 1 },
    { content: '� 自主生成处理计划：\n✅ 创建处理群（已完成）\n⏳ 识别受影响业务并邀请相关人员\n⏳ 评估是否需要电话通知\n⏳ 设置30分钟未恢复自动提醒\n⏳ 定时同步处理进度\n⏳ 故障恢复后生成简报', senderId: 'jarvis', chatId: 1 },
    { content: '� 调用工具：检索以往相似故障...', senderId: 'jarvis', chatId: 1 },
    { content: '� 检索结果：发现3起相似的"用户无法进入房间"故障，平均恢复时间18分钟', senderId: 'jarvis', chatId: 1 },
    { content: '🎯 基于历史数据，调整目标：15分钟内恢复服务，避免影响扩大', senderId: 'jarvis', chatId: 1 },
    { content: '👥 调用工具：邀请相关业务人员进群...已识别并邀请房间业务负责人', senderId: 'jarvis', chatId: 1 },

    // 第2阶段：AI Agent评估和决策 (7-12秒)
    { content: '📞 评估通知级别：影响用户数>100，调用工具：电话呼叫业务负责人', senderId: 'jarvis', chatId: 1 },
    { content: '☎️ 已自动拨打电话通知业务负责人，预计1分钟内加入群聊', senderId: 'jarvis', chatId: 1 },
    { content: '⏰ 设置自动提醒：如30分钟内未恢复，将自动升级为P0级故障', senderId: 'jarvis', chatId: 1 },
    { content: '🔧 调用工具：根因定位分析...', senderId: 'jarvis', chatId: 1 },
    { content: '📈 根因定位结果：WebSocket服务异常，疑似内存泄漏导致进程崩溃', senderId: 'jarvis', chatId: 1 },
    { content: '🎯 更新处理策略：优先重启服务快速恢复，并行进行根因修复', senderId: 'jarvis', chatId: 1 },

    // 第3阶段：团队协作执行 (13-20秒)
    { content: '收到通知，我立即检查服务器状态。', senderId: 'zhangsan', chatId: 1 },
    { content: '我查看应用日志，配合根因分析。', senderId: 'lisi', chatId: 1 },
    { content: '我准备测试验证和回归测试。', senderId: 'wangwu', chatId: 1 },
    { content: '我已联系用户，正在收集详细反馈。', senderId: 'zhaoliu', chatId: 1 },
    { content: '确认WebSocket服务进程异常退出，端口8080无响应。', senderId: 'zhangsan', chatId: 1 },
    { content: '应用日志显示内存溢出错误，连接池未正确释放。', senderId: 'lisi', chatId: 1 },
    { content: '✅ 根因定位工具分析准确，执行服务重启恢复方案', senderId: 'jarvis', chatId: 1 },
    { content: 'WebSocket服务已重启，端口恢复正常。', senderId: 'zhangsan', chatId: 1 },

    // 第4阶段：AI Agent进度同步和监控 (21-26秒)
    { content: '📊 自动进度同步：服务已恢复，用户连接成功率回升至95%', senderId: 'jarvis', chatId: 1 },
    { content: '测试验证通过，房间功能恢复正常。', senderId: 'wangwu', chatId: 1 },
    { content: '用户确认问题解决，投诉量明显下降。', senderId: 'zhaoliu', chatId: 1 },
    { content: '🎯 阶段目标达成：服务已恢复，用时13分钟，优于预期目标', senderId: 'jarvis', chatId: 1 },
    { content: '🔧 启动根因修复：调用工具进行深度代码分析...', senderId: 'jarvis', chatId: 1 },
    { content: '已定位并修复连接池泄漏问题，提交代码审查。', senderId: 'lisi', chatId: 1 },

    // 第5阶段：AI Agent自动总结和归档 (27-32秒)
    { content: '代码审查通过，修复版本已部署。', senderId: 'wangwu', chatId: 1 },
    { content: '生产环境监控正常，内存使用稳定。', senderId: 'zhangsan', chatId: 1 },
    { content: '� 自动生成故障简报：\n故障类型：WebSocket服务异常\n影响范围：127名用户\n故障时长：13分钟\n根本原因：连接池内存泄漏\n修复措施：重启服务+代码修复', senderId: 'jarvis', chatId: 1 },
    { content: '� 自动录入故障管理平台：故障ID #2025010701 已创建', senderId: 'jarvis', chatId: 1 },
    { content: '🔄 更新相似故障处理经验库，优化未来处理效率', senderId: 'jarvis', chatId: 1 },
    { content: '🎯 所有目标已完成！故障处理流程结束，感谢团队协作！', senderId: 'jarvis', chatId: 1 },
  ]

  sendMessagesInQueue(faultHandlingMessages, 1000)
} */

// AI Agent状态管理
const agentState = ref({
  currentGoal: '',
  currentPhase: '',
  progress: 0,
  isActive: false,
  lastAnalysisTime: 0,
  lastDecisionTime: 0,
  lastContactAlert: 0,
  lastServiceInquiry: 0,
  hasPerformedAnalysis: false,
  nextActions: [] as string[],
  contextAnalysis: {
    teamActivity: 0,
    progressIndicators: [] as string[],
    blockers: [] as string[],
    nextActions: [] as string[],
  },
})

// AI Agent智能监控（仅监控联系困难和关键进展）
function startIntelligentMonitoring() {
  const monitoringInterval = setInterval(() => {
    if (!agentState.value.isActive) {
      clearInterval(monitoringInterval)
      return
    }

    // 仅监控联系困难和关键进展
    analyzeTeamActivityForKeyEvents()

    agentState.value.lastAnalysisTime = Date.now()
  }, 5000) // 每5秒监控一次，降低频率
}

// 原有的自主监控和决策循环（保留但不使用）
function _startAutonomousDecisionLoop() {
  const analysisInterval = setInterval(() => {
    if (!agentState.value.isActive) {
      clearInterval(analysisInterval)
      return
    }

    // 实时分析群内动态
    _analyzeTeamActivity()

    // 基于分析结果自主决策
    _makeAutonomousDecisions()

    agentState.value.lastAnalysisTime = Date.now()
  }, 3000) // 每3秒分析一次
}

// 仅监控关键事件的团队活动分析（克制版）
function analyzeTeamActivityForKeyEvents() {
  const currentMessages = chatMessages.value[1] || []
  const now = Date.now()
  const recentMessages = currentMessages.filter((msg) => {
    // 使用timestamp字段，如果没有则尝试解析time字段，否则认为是最近的消息
    const msgTime = msg.timestamp || (msg.time ? new Date(`2025-01-01 ${msg.time}`).getTime() : now)
    return now - msgTime < 60000 // 最近60秒的消息，增加时间窗口
  })

  // 只监控联系困难的情况
  detectContactIssues(recentMessages)

  // 检测@Jarvis询问服务状态的情况
  detectServiceInquiries(recentMessages)

  // 收集较多群聊消息时，进行根因定位和检索相似故障案例
  if (currentMessages.length >= 15 && !agentState.value.hasPerformedAnalysis) {
    performComprehensiveAnalysis()
    agentState.value.hasPerformedAnalysis = true
  }
}

// 综合分析函数（当收集到足够多的群聊消息时执行）
function performComprehensiveAnalysis() {
  // 直接提供分析结果，不推送启动消息
  setTimeout(() => {
    provideAnalysisResults()
  }, 3000) // 3秒后直接提供结果
}

// 提供分析结果
function provideAnalysisResults() {
  const resultMessages = [
    {
      content: `✅ AI Agent分析完成

🔧 根因定位结果：
• 故障类型：WebSocket连接池内存泄漏
• 影响范围：127名用户
• 根本原因：连接池清理机制失效

📚 相似故障案例：
• 发现3起高相似度案例
• 历史成功率：100%
• 平均恢复时间：18分钟

💡 推荐处理方案：
1. 立即重启WebSocket服务（临时恢复）
2. 修复连接池清理逻辑（根本解决）
3. 增强内存监控告警（预防措施）`,
      senderId: 'jarvis',
      chatId: 1,
      type: 'interactive_card' as const,
      actions: [
        { id: 'view_detailed_analysis', text: '查看详细分析', type: 'primary', icon: 'MessageCircle' },
        { id: 'view_recovery_plan', text: '查看恢复方案', type: 'secondary', icon: 'MessageCircle' },
      ],
    },
  ]

  sendMessagesInQueue(resultMessages, 0)
}

// 分析团队活动和处理进展（原版本，保留但不使用）
function _analyzeTeamActivity() {
  const currentMessages = chatMessages.value[1] || []
  const now = Date.now()
  const recentMessages = currentMessages.filter((msg) => {
    // 使用timestamp字段，如果没有则尝试解析time字段，否则认为是最近的消息
    const msgTime = msg.timestamp || (msg.time ? new Date(`2025-01-01 ${msg.time}`).getTime() : now)
    return now - msgTime < 30000 // 最近30秒的消息
  })

  console.log('当前消息总数:', currentMessages.length, '最近30秒消息数:', recentMessages.length)

  // 分析团队活跃度
  agentState.value.contextAnalysis.teamActivity = recentMessages.length

  // 识别进展指标
  const progressKeywords = ['完成', '恢复', '正常', '成功', '解决']
  const blockerKeywords = ['失败', '异常', '错误', '阻塞', '问题']

  agentState.value.contextAnalysis.progressIndicators = recentMessages
    .filter(msg => progressKeywords.some(keyword => msg.content?.includes(keyword)))
    .map(msg => `${msg.senderName}: ${msg.content?.substring(0, 30)}...`)

  agentState.value.contextAnalysis.blockers = recentMessages
    .filter(msg => blockerKeywords.some(keyword => msg.content?.includes(keyword)))
    .map(msg => `${msg.senderName}: ${msg.content?.substring(0, 30)}...`)

  // 智能感知联系不上的情况
  detectContactIssues(recentMessages)
}

// 智能感知联系不上的情况
function detectContactIssues(messages: any[]) {
  const contactKeywords = ['联系不上', '找不到', '没接电话', '不在线', '联系不到', '打不通']

  messages.forEach((msg) => {
    if (msg.senderId !== 'jarvis' && contactKeywords.some(keyword => msg.content?.includes(keyword))) {
      console.log('检测到联系困难消息:', msg.content)

      // 提取被联系人的姓名 - 改进的正则表达式
      const namePattern = /([张李王赵刘陈杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段漕钱汤尹黎易常武乔贺赖龚文][一-龯]{1,3})/g
      const matches = msg.content.match(namePattern)

      if (matches && matches.length > 0) {
        // 取最后一个匹配的姓名（通常是被联系的人）
        const contactName = matches[matches.length - 1]
        console.log('提取到联系人姓名:', contactName)

        // 避免重复提醒
        const lastContactAlert = agentState.value.lastContactAlert || 0
        const now = Date.now()

        if (now - lastContactAlert > 60000) { // 1分钟冷却时间
          agentState.value.lastContactAlert = now
          console.log('触发云呼叫协助，联系人:', contactName)

          setTimeout(() => {
            offerCloudCallService(contactName, msg.senderName || '用户', msg.content || '')
          }, 2000) // 2秒后提供帮助
        }
        else {
          console.log('在冷却期内，跳过提醒')
        }
      }
      else {
        console.log('未能提取到联系人姓名')
      }
    }
  })
}

// 提供云呼叫服务
function offerCloudCallService(contactName: string, requesterName: string, originalMessage: string) {
  const offerMessages = [
    {
      content: `💬 引用回复：${requesterName}
"${originalMessage}"

🤖 AI Agent检测到联系问题

📞 检测到您提及联系不上${contactName}

💡 Jarvis可以使用云呼叫工具帮助您联系${contactName}，是否需要我来尝试呼叫？

🔧 云呼叫功能：
• 智能多渠道呼叫（手机、座机、企业微信）
• 实时呼叫状态监控
• 自动语音留言
• 呼叫记录和结果反馈`,
      senderId: 'jarvis',
      chatId: 1,
      type: 'interactive_card' as const,
      actions: [
        { id: `cloud_call_${contactName}`, text: `呼叫${contactName}`, type: 'primary', icon: 'Phone' },
        { id: 'decline_call', text: '暂不需要', type: 'secondary', icon: 'MessageCircle' },
      ],
    },
  ]

  sendMessagesInQueue(offerMessages, 0)
}

// 检测@Jarvis询问服务状态的情况
function detectServiceInquiries(messages: any[]) {
  const mentionKeywords = ['@Jarvis', '@jarvis', 'Jarvis', 'jarvis']

  messages.forEach((msg) => {
    if (msg.senderId !== 'jarvis' && mentionKeywords.some(keyword => msg.content?.includes(keyword))) {
      console.log('检测到@Jarvis消息:', msg.content)

      // 避免重复响应
      const lastServiceInquiry = agentState.value.lastServiceInquiry || 0
      const now = Date.now()

      if (now - lastServiceInquiry > 5000) { // 5秒冷却时间
        agentState.value.lastServiceInquiry = now

        // 场景1：云呼叫请求
        if (msg.content?.includes('打电话给')) {
          const contactName = extractContactNameFromCall(msg.content)
          console.log('触发云呼叫，联系人:', contactName)
          setTimeout(() => {
            performCloudCall(contactName, msg.senderName || '用户', msg.content || '')
          }, 1000)
        }
        // 场景2：变更检查
        else if (msg.content?.includes('变更') || msg.content?.includes('有没有变更')) {
          const serviceName = extractServiceName(msg.content)
          console.log('触发变更检查，服务:', serviceName)
          setTimeout(() => {
            performChangeCheck(serviceName, msg.senderName || '用户', msg.content || '')
          }, 1000)
        }
        // 场景3：状态检查
        else if (msg.content?.includes('是否正常') || msg.content?.includes('状态') || msg.content?.includes('异常')) {
          const serviceName = extractServiceName(msg.content)
          console.log('触发状态检查，服务:', serviceName)
          setTimeout(() => {
            performStatusCheck(serviceName, msg.senderName || '用户', msg.content || '')
          }, 1000)
        }
        // 场景4：故障结束
        else if (msg.content?.includes('故障结束')) {
          console.log('触发故障结束流程')
          setTimeout(() => {
            performFaultCompletion(msg.senderName || '用户', msg.content || '')
          }, 1000)
        }
        // 其他通用服务询问
        else {
          const serviceKeywords = ['服务', '应用', '系统']
          if (serviceKeywords.some(keyword => msg.content?.includes(keyword))) {
            const serviceName = extractServiceName(msg.content)
            console.log('触发通用服务排查，服务:', serviceName)
            setTimeout(() => {
              performServiceDiagnosis(serviceName, msg.senderName || '用户', msg.content || '')
            }, 1000)
          }
        }
      }
      else {
        console.log('在冷却期内，跳过@Jarvis响应')
      }
    }
  })
}

// 提取服务名称
function extractServiceName(content: string): string {
  // 常见服务名称模式
  const servicePatterns = [
    /WebSocket[服务]*/g,
    /数据库[服务]*/g,
    /缓存[服务]*/g,
    /Redis[服务]*/g,
    /MySQL[服务]*/g,
    /API[服务]*/g,
    /用户[服务]*/g,
    /房间[服务]*/g,
    /支付[服务]*/g,
    /消息[服务]*/g,
    /推送[服务]*/g,
    /登录[服务]*/g,
    /认证[服务]*/g,
  ]

  for (const pattern of servicePatterns) {
    const matches = content.match(pattern)
    if (matches && matches.length > 0) {
      return matches[0]
    }
  }

  // 如果没有匹配到具体服务，返回通用名称
  return '相关服务'
}

// 从云呼叫请求中提取联系人姓名
function extractContactNameFromCall(content: string): string {
  // 匹配"打电话给XXX"模式
  const callPattern = /打电话给(.+?)(?:\s|$|[，。])/
  const match = content.match(callPattern)

  if (match && match[1]) {
    return match[1].trim()
  }

  // 如果没有匹配到，返回默认名称
  return '相关人员'
}

// 场景1：执行云呼叫
function performCloudCall(contactName: string, requesterName: string, originalMessage: string) {
  const callMessages = [
    {
      content: `💬 引用回复：${requesterName}
"${originalMessage}"

📞 收到云呼叫请求，正在为您联系${contactName}

🛠️ 调用云呼叫工具：
• 查找联系方式...
• 建立呼叫连接...
• 等待接听...

⏳ 预计30秒内建立通话`,
      senderId: 'jarvis',
      chatId: 1,
      type: 'interactive_card' as const,
      actions: [
        { id: 'view_call_progress', text: '查看呼叫进度', type: 'secondary', icon: 'Phone' },
      ],
    },
  ]

  sendMessagesInQueue(callMessages, 0)

  // 模拟呼叫过程
  setTimeout(() => {
    executeCloudCall(contactName)
  }, 2000) // 2秒后开始呼叫过程
}

// 场景2：执行变更检查
function performChangeCheck(serviceName: string, requesterName: string, originalMessage: string) {
  const checkCard = {
    id: (chatMessages.value[1]?.length || 0) + 1,
    senderId: 'jarvis',
    senderName: 'Jarvis',
    content: `💬 引用回复：${requesterName}
"${originalMessage}"

🔍 正在调用根因定位MCP工具检查${serviceName}变更记录

🛠️ MCP工具执行中：
• 连接变更管理系统...
• 查询部署记录...
• 分析配置变更...
• 检查代码提交...

⏳ 预计20秒内完成检查`,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'interactive_card',
    avatar: '/avatars/jarvis.png',
    actions: [
      { id: 'view_change_progress', text: '查看检查进度', type: 'secondary', icon: 'MessageCircle' },
    ],
  }

  // 确保聊天消息数组存在
  if (!chatMessages.value[1]) {
    chatMessages.value[1] = []
  }

  chatMessages.value[1].push(checkCard)

  // 更新聊天列表
  const chatIndex = chatList.value.findIndex(c => c.id === 1)
  if (chatIndex !== -1) {
    chatList.value[chatIndex].lastMessage = `正在检查${serviceName}变更...`
    chatList.value[chatIndex].time = checkCard.time
  }

  // 模拟变更检查过程
  setTimeout(() => {
    const cardIndex = chatMessages.value[1].findIndex(msg => msg.id === checkCard.id)
    if (cardIndex !== -1) {
      chatMessages.value[1][cardIndex].content = `✅ ${serviceName}变更检查完成

🔍 MCP根因定位工具检查结果：

📊 变更记录分析：
• 最近24小时：发现2次变更
• 最近7天：发现8次变更
• 风险等级：中等

📋 详细变更记录：
【变更1】2小时前 14:30
• 变更类型：代码部署
• 版本变更：v2.1.2 → v2.1.3
• 变更模块：连接池管理器
• 执行人员：张工
• 影响评估：中等风险

【变更2】昨天 16:45
• 变更类型：配置调整
• 变更内容：数据库连接池大小 50→80
• 执行人员：李工
• 影响评估：低风险

🎯 变更影响分析：
• v2.1.3版本部署后，${serviceName}性能出现下降
• 连接池管理器变更可能导致内存泄漏
• 建议考虑回滚到v2.1.2版本

💡 处理建议：
1. 立即回滚到稳定版本v2.1.2
2. 修复v2.1.3版本连接池问题
3. 加强变更前的测试验证`
      chatMessages.value[1][cardIndex].actions = [
        { id: 'view_change_details', text: '查看变更详情', type: 'primary', icon: 'MessageCircle' },
        { id: 'execute_rollback', text: '执行回滚', type: 'secondary', icon: 'MessageCircle' },
      ]
    }
  }, 2000) // 2秒后显示结果
}

// 场景3：执行状态检查
function performStatusCheck(serviceName: string, requesterName: string, originalMessage: string) {
  const statusCard = {
    id: (chatMessages.value[1]?.length || 0) + 1,
    senderId: 'jarvis',
    senderName: 'Jarvis',
    content: `💬 引用回复：${requesterName}
"${originalMessage}"

📊 正在调用根因定位MCP工具检查${serviceName}状态

🛠️ MCP工具执行中：
• 连接监控系统...
• 获取实时指标...
• 分析性能趋势...
• 检查健康状态...

⏳ 预计25秒内完成检查`,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'interactive_card',
    avatar: '/avatars/jarvis.png',
    actions: [
      { id: 'view_status_progress', text: '查看检查进度', type: 'secondary', icon: 'MessageCircle' },
    ],
  }

  // 确保聊天消息数组存在
  if (!chatMessages.value[1]) {
    chatMessages.value[1] = []
  }

  chatMessages.value[1].push(statusCard)

  // 更新聊天列表
  const chatIndex = chatList.value.findIndex(c => c.id === 1)
  if (chatIndex !== -1) {
    chatList.value[chatIndex].lastMessage = `正在检查${serviceName}状态...`
    chatList.value[chatIndex].time = statusCard.time
  }

  // 模拟状态检查过程
  setTimeout(() => {
    const cardIndex = chatMessages.value[1].findIndex(msg => msg.id === statusCard.id)
    if (cardIndex !== -1) {
      chatMessages.value[1][cardIndex].content = `✅ ${serviceName}状态检查完成

📊 MCP根因定位工具检查结果：

🔍 服务健康状态：
• 整体状态：⚠️ 异常 (性能下降)
• 可用性：95.2% (低于SLA 99%)
• 响应时间：450ms (正常200ms)
• 错误率：2.1% (正常<0.5%)

📈 关键指标分析：
【CPU使用率】
• 当前值：45%
• 趋势：稳定
• 状态：✅ 正常

【内存使用率】
• 当前值：85%
• 趋势：📈 持续上升
• 状态：⚠️ 异常 (超出阈值80%)

【连接数】
• 当前值：1,247
• 趋势：📈 异常增长
• 状态：⚠️ 异常 (正常范围800-1000)

【响应时间】
• 当前值：450ms
• 趋势：📈 持续恶化
• 状态：❌ 异常 (SLA要求<300ms)

📊 性能趋势分析：
• 最近1小时：性能持续下降
• 最近24小时：出现3次性能峰值
• 根本原因：内存泄漏导致性能衰减

💡 处理建议：
1. 🔴 紧急：重启服务释放内存
2. 🟡 中期：回滚到稳定版本
3. 🟢 长期：修复内存泄漏问题`

      chatMessages.value[1][cardIndex].actions = [
        { id: 'view_status_details', text: '查看详细指标', type: 'primary', icon: 'MessageCircle' },
        { id: 'restart_service', text: '重启服务', type: 'secondary', icon: 'MessageCircle' },
      ]
    }
  }, 2000) // 2秒后显示结果
}

// 场景4：执行故障结束流程
function performFaultCompletion(requesterName: string, originalMessage: string) {
  const completionCard = {
    id: (chatMessages.value[1]?.length || 0) + 1,
    senderId: 'jarvis',
    senderName: 'Jarvis',
    content: `💬 引用回复：${requesterName}
"${originalMessage}"

✅ 收到故障结束指令，正在执行结束流程

🛠️ 执行步骤：
• 生成故障简报...
• 调用MCP工具...
• 录入大禹平台...
• 归档处理记录...

⏳ 预计1分钟内完成所有流程`,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'interactive_card',
    avatar: '/avatars/jarvis.png',
    actions: [
      { id: 'view_completion_progress', text: '查看结束进度', type: 'secondary', icon: 'MessageCircle' },
    ],
  }

  // 确保聊天消息数组存在
  if (!chatMessages.value[1]) {
    chatMessages.value[1] = []
  }

  chatMessages.value[1].push(completionCard)

  // 更新聊天列表
  const chatIndex = chatList.value.findIndex(c => c.id === 1)
  if (chatIndex !== -1) {
    chatList.value[chatIndex].lastMessage = '正在执行故障结束流程...'
    chatList.value[chatIndex].time = completionCard.time
  }

  // 开始故障结束流程
  setTimeout(() => {
    simulateReportGeneration()
  }, 2000) // 2秒后开始生成简报

  // 停止AI Agent
  setTimeout(() => {
    stopAIAgent()
  }, 10000) // 10秒后停止AI Agent
}

// 执行服务诊断
function performServiceDiagnosis(serviceName: string, requesterName: string, originalMessage: string) {
  // 创建诊断卡片，显示MCP工具调用过程
  const diagnosisCard = {
    id: (chatMessages.value[1]?.length || 0) + 1,
    senderId: 'jarvis',
    senderName: 'Jarvis',
    content: `💬 引用回复：${requesterName}
"${originalMessage}"

🔍 正在调用根因定位MCP工具排查${serviceName}

🛠️ MCP工具执行中：
• 连接监控系统...
• 分析服务状态...
• 检查变更记录...
• 运行诊断算法...

⏳ 预计30秒内完成排查`,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'interactive_card',
    avatar: '/avatars/jarvis.png',
    actions: [
      { id: 'view_diagnosis_progress', text: '查看排查进度', type: 'secondary', icon: 'MessageCircle' },
    ],
  }

  // 确保聊天消息数组存在
  if (!chatMessages.value[1]) {
    chatMessages.value[1] = []
  }

  chatMessages.value[1].push(diagnosisCard)

  // 更新聊天列表
  const chatIndex = chatList.value.findIndex(c => c.id === 1)
  if (chatIndex !== -1) {
    chatList.value[chatIndex].lastMessage = `正在排查${serviceName}...`
    chatList.value[chatIndex].time = diagnosisCard.time
  }

  // 模拟MCP工具执行过程
  simulateMCPDiagnosis(diagnosisCard.id, serviceName)
}

// 模拟MCP根因定位工具执行过程
function simulateMCPDiagnosis(cardId: number, serviceName: string) {
  const diagnosisSteps = [
    {
      delay: 5000,
      content: `🔍 MCP根因定位工具排查${serviceName}

🛠️ 工具执行进度：
• 连接监控系统：✅ 完成
• 获取服务状态：进行中... 60%
• 分析性能指标：等待中...
• 检查变更记录：等待中...

📊 初步发现：
• 服务进程：运行中
• 响应时间：异常增长
• 内存使用：持续上升`,
      actions: [{ id: 'view_diagnosis_progress', text: '排查中...', type: 'secondary', icon: 'MessageCircle' }],
    },
    {
      delay: 15000,
      content: `🔍 MCP根因定位工具排查${serviceName}

🛠️ 工具执行进度：
• 连接监控系统：✅ 完成
• 获取服务状态：✅ 完成
• 分析性能指标：进行中... 80%
• 检查变更记录：进行中... 70%

📊 深度分析：
• CPU使用率：45% (正常)
• 内存使用率：78% → 85% (异常增长)
• 连接数：1,247 (超出正常范围)
• 错误率：0.3% → 2.1% (显著上升)`,
      actions: [{ id: 'view_diagnosis_progress', text: '深度分析中...', type: 'secondary', icon: 'MessageCircle' }],
    },
    {
      delay: 25000,
      content: `✅ MCP根因定位工具排查完成

🔍 ${serviceName}诊断结果：

📊 服务状态分析：
• 服务状态：运行中但性能下降
• 主要问题：内存泄漏导致性能衰减
• 影响程度：中等 (响应时间增加40%)

🕐 变更记录检查：
• 最近变更：2小时前部署v2.1.3版本
• 变更内容：连接池配置优化
• 风险评估：中等风险变更

🎯 根因定位：
• 根本原因：新版本连接池清理机制存在缺陷
• 触发条件：高并发场景下连接对象未正确释放
• 影响范围：所有使用该服务的功能模块

💡 处理建议：
1. 立即回滚到v2.1.2版本 (临时解决)
2. 修复连接池清理逻辑 (根本解决)
3. 增强内存监控告警 (预防措施)`,
      actions: [
        { id: 'view_detailed_diagnosis', text: '查看详细诊断', type: 'primary', icon: 'MessageCircle' },
        { id: 'execute_rollback', text: '执行回滚', type: 'secondary', icon: 'MessageCircle' },
      ],
    },
  ]

  diagnosisSteps.forEach((step) => {
    setTimeout(() => {
      const cardIndex = chatMessages.value[1].findIndex(msg => msg.id === cardId)
      if (cardIndex !== -1) {
        chatMessages.value[1][cardIndex].content = step.content
        chatMessages.value[1][cardIndex].actions = step.actions
      }
    }, step.delay)
  })
}

// 执行云呼叫
function executeCloudCall(contactName: string) {
  // 创建呼叫状态卡片
  const callCard = {
    id: (chatMessages.value[1]?.length || 0) + 1,
    senderId: 'jarvis',
    senderName: 'Jarvis',
    content: `📞 正在使用云呼叫工具联系${contactName}

🔄 呼叫状态：正在连接...
📱 尝试渠道：手机号码
⏱️ 开始时间：${new Date().toLocaleTimeString()}

请稍候，正在为您建立连接...`,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'interactive_card',
    avatar: '/avatars/jarvis.png',
    actions: [
      { id: 'calling_status', text: '呼叫中...', type: 'secondary', icon: 'Phone' },
    ],
  }

  // 确保聊天消息数组存在
  if (!chatMessages.value[1]) {
    chatMessages.value[1] = []
  }

  chatMessages.value[1].push(callCard)

  // 更新聊天列表
  const chatIndex = chatList.value.findIndex(c => c.id === 1)
  if (chatIndex !== -1) {
    chatList.value[chatIndex].lastMessage = `正在呼叫${contactName}...`
    chatList.value[chatIndex].time = callCard.time
  }

  // 模拟呼叫过程
  simulateCallProcess(callCard.id, contactName)
}

// 模拟呼叫过程
function simulateCallProcess(cardId: number, contactName: string) {
  const callSteps = [
    {
      delay: 2000,
      content: `📞 云呼叫工具联系${contactName}

✅ 呼叫完成
📱 通话渠道：手机号码 138****5678
⏱️ 通话时长：2分15秒
📋 通话结果：成功联系

通话要点：
• ${contactName}确认收到故障通知
• 已了解当前故障情况和处理进度
• 将立即开始相关技术支持工作
• 确认可以随时配合故障处理

💡 ${contactName}表示会立即处理相关问题`,
      actions: [
        { id: 'call_completed', text: '通话完成', type: 'primary', icon: 'MessageCircle' },
        { id: 'call_summary', text: '查看通话记录', type: 'secondary', icon: 'MessageCircle' },
      ],
    },
  ]

  callSteps.forEach((step) => {
    setTimeout(() => {
      const cardIndex = chatMessages.value[1].findIndex(msg => msg.id === cardId)
      if (cardIndex !== -1) {
        chatMessages.value[1][cardIndex].content = step.content
        chatMessages.value[1][cardIndex].actions = step.actions
      }
    }, step.delay)
  })

  // 云呼叫完成，不自动拉人进群
}

// 基于分析结果自主决策（原版本，保留但不使用）
function _makeAutonomousDecisions() {
  const { progressIndicators, blockers, teamActivity } = agentState.value.contextAnalysis

  // 避免频繁触发相同决策，增加冷却时间
  const now = Date.now()
  const lastDecisionTime = agentState.value.lastDecisionTime || 0
  const cooldownPeriod = 30000 // 30秒冷却时间

  if (now - lastDecisionTime < cooldownPeriod) {
    return // 在冷却期内，不做新决策
  }

  // 决策逻辑：如果检测到阻塞或进展缓慢，自主采取行动
  if (blockers.length > 0 && agentState.value.currentPhase !== '应急处理') {
    // 检测到阻塞，自主升级处理
    agentState.value.currentPhase = '应急处理'
    agentState.value.nextActions = ['升级处理优先级', '调用额外资源', '启动应急预案']
    agentState.value.lastDecisionTime = now

    // 自主发送分析和建议消息
    setTimeout(() => {
      sendAutonomousAnalysisMessage('检测到处理阻塞，自主启动应急响应')
    }, 1000)
  }
  else if (progressIndicators.length >= 2 && agentState.value.currentPhase !== '验证恢复' && agentState.value.progress >= 70) {
    // 检测到积极进展，自主推进到验证阶段
    agentState.value.currentPhase = '验证恢复'
    agentState.value.nextActions = ['验证恢复效果', '监控系统稳定性', '准备总结报告']
    agentState.value.lastDecisionTime = now

    setTimeout(() => {
      sendAutonomousAnalysisMessage('检测到积极进展，自主推进恢复验证')
    }, 1000)
  }
  else if (teamActivity === 0 && agentState.value.progress < 90 && agentState.value.progress > 20) {
    // 检测到团队活动停滞，但只在处理中期才提醒
    agentState.value.nextActions = ['提醒团队成员', '同步当前进展', '识别潜在阻塞']
    agentState.value.lastDecisionTime = now

    setTimeout(() => {
      sendAutonomousAnalysisMessage('检测到协作节奏放缓，自主同步进展状态')
    }, 1000)
  }
}

// 发送AI Agent自主分析消息
function sendAutonomousAnalysisMessage(analysisType: string) {
  const analysisMessages = [
    {
      content: `🤖 AI Agent自主分析：${analysisType}

📊 实时监控状态：
• 当前阶段：${agentState.value.currentPhase}
• 处理进度：${agentState.value.progress}%
• 团队活跃度：${agentState.value.contextAnalysis.teamActivity} 条消息/30秒
• 检测到的进展指标：${agentState.value.contextAnalysis.progressIndicators.length} 个
• 识别的阻塞因素：${agentState.value.contextAnalysis.blockers.length} 个

🎯 基于分析结果，AI Agent正在自主调整处理策略...`,
      senderId: 'jarvis',
      chatId: 1,
      type: 'interactive_card' as const,
      actions: [
        { id: 'view_analysis_details', text: '查看分析详情', type: 'primary', icon: 'MessageCircle' },
        { id: 'adjust_strategy', text: '调整策略', type: 'secondary', icon: 'MessageCircle' },
      ],
    },
  ]

  sendMessagesInQueue(analysisMessages, 0)
}

// AI Agent自主规划和决策引擎
function simulateAIAgentFaultHandling() {
  // 初始化AI Agent状态
  agentState.value = {
    currentGoal: '快速恢复用户房间访问功能',
    currentPhase: '初始化',
    progress: 0,
    isActive: true,
    lastAnalysisTime: Date.now(),
    lastDecisionTime: 0,
    lastContactAlert: 0,
    lastServiceInquiry: 0,
    hasPerformedAnalysis: false,
    nextActions: ['分析故障影响', '检索历史案例', '制定处理计划'],
    contextAnalysis: {
      teamActivity: 0,
      progressIndicators: [],
      blockers: [],
      nextActions: ['分析故障影响', '检索历史案例', '制定处理计划'],
    },
  }

  // 第1阶段：Jarvis简述故障情况和支持功能（刚拉进群时）
  const initialMessages = [
    {
      content: `🚨 故障处理群聊已创建

📊 故障概况：
• 故障类型：用户无法进入房间
• 影响范围：正在评估中
• 处理目标：快速恢复服务

🤖 Jarvis支持功能：
• @Jarvis 询问应用和服务状况
• 云呼叫工具协助联系相关人员
• 根因分析和相似故障检索

💡 如需帮助，请直接@我或提及"联系不上XXX"`,
      senderId: 'jarvis',
      chatId: 1,
      type: 'interactive_card' as const,
      actions: [
        { id: 'check_service_status', text: '检查服务状态', type: 'primary', icon: 'MessageCircle' },
        { id: 'manual_cloud_call', text: '云呼叫工具', type: 'secondary', icon: 'Phone' },
      ],
    },
  ]

  // 发送初始消息
  sendMessagesInQueue(initialMessages, 2000)

  // 启动AI Agent智能感知（仅监控联系困难）
  startIntelligentMonitoring()

  // 等待团队协作，不主动推送消息
  // 只有在特定条件下才会主动分析和推送

  // 模拟团队协作消息（用于演示）
  setTimeout(() => {
    const teamMessages = [
      { content: '收到通知，我立即检查服务器状态。', senderId: 'zhangsan', chatId: 1 },
      { content: '我查看应用日志，配合根因分析。', senderId: 'lisi', chatId: 1 },
      { content: '我准备测试验证和回归测试。', senderId: 'wangwu', chatId: 1 },
      { content: '我已联系用户，正在收集详细反馈。', senderId: 'zhaoliu', chatId: 1 },
      { content: '确认WebSocket服务进程异常退出，端口8080无响应。', senderId: 'zhangsan', chatId: 1 },
      { content: '应用日志显示内存溢出错误，连接池未正确释放。', senderId: 'lisi', chatId: 1 },
      { content: '需要联系运维负责人李明确认服务器状态，但是联系不上他，电话一直没人接', senderId: 'wangwu', chatId: 1 },
      { content: 'WebSocket服务已重启，端口恢复正常。', senderId: 'zhangsan', chatId: 1 },
      { content: '测试验证通过，房间功能恢复正常。', senderId: 'wangwu', chatId: 1 },
      { content: '用户确认问题解决，投诉量明显下降。', senderId: 'zhaoliu', chatId: 1 },
      { content: '已定位并修复连接池泄漏问题，提交代码审查。', senderId: 'lisi', chatId: 1 },
      { content: '代码审查通过，修复版本已部署。', senderId: 'wangwu', chatId: 1 },
      { content: '生产环境监控正常，内存使用稳定。', senderId: 'zhangsan', chatId: 1 },
    ]
    sendMessagesInQueue(teamMessages, 10000) // 10秒后开始团队协作
  }, 0)
}

// 在故障处理完成时停止AI Agent
function stopAIAgent() {
  agentState.value.isActive = false
  agentState.value.currentPhase = '已完成'
  agentState.value.progress = 100
}

// 模拟故障简报生成过程
function simulateReportGeneration() {
  const reportCard = {
    id: (chatMessages.value[1]?.length || 0) + 1,
    senderId: 'jarvis',
    senderName: 'Jarvis',
    content: `📋 正在生成故障简报

🔄 收集处理数据：
• 故障时间线：✅ 已收集
• 处理步骤：✅ 已记录
• 影响范围：✅ 已统计
• 根因分析：✅ 已完成

⏳ 正在生成简报文档...`,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'interactive_card',
    avatar: '/avatars/jarvis.png',
    actions: [
      { id: 'generating_report', text: '生成中...', type: 'secondary', icon: 'MessageCircle' },
    ],
  }

  chatMessages.value[1].push(reportCard)

  // 更新聊天列表
  const chatIndex = chatList.value.findIndex(c => c.id === 1)
  if (chatIndex !== -1) {
    chatList.value[chatIndex].lastMessage = '正在生成故障简报...'
    chatList.value[chatIndex].time = reportCard.time
  }

  // 第一阶段：简报生成完成 (1秒后)
  setTimeout(() => {
    const cardIndex = chatMessages.value[1].findIndex(msg => msg.id === reportCard.id)
    if (cardIndex !== -1) {
      chatMessages.value[1][cardIndex].content = `✅ 故障简报生成完成

📋 故障处理简报：
• 故障ID：#2025010701
• 处理时长：13分钟
• 影响用户：127人
• 根本原因：WebSocket连接池内存泄漏
• 解决方案：服务重启 + 代码修复

🛠️ 正在调用MCP工具录入大禹平台...`

      chatMessages.value[1][cardIndex].actions = [
        { id: 'uploading_to_dayu', text: '录入中...', type: 'secondary', icon: 'MessageCircle' },
      ]
    }
  }, 1000)

  // 第二阶段：MCP工具调用和大禹平台录入 (2秒后)
  setTimeout(() => {
    const cardIndex = chatMessages.value[1].findIndex(msg => msg.id === reportCard.id)
    if (cardIndex !== -1) {
      chatMessages.value[1][cardIndex].content = `✅ 故障处理流程全部完成

📋 故障简报：
• 故障ID：#2025010701
• 处理时长：13分钟
• 影响用户：127人
• 根本原因：WebSocket连接池内存泄漏
• 解决方案：服务重启 + 代码修复

�️ MCP工具执行结果：
• ✅ 故障简报已生成
• ✅ 大禹平台录入完成
• ✅ 故障报告ID：DY-2025010701
• ✅ 相关人员已自动通知

📊 大禹平台录入信息：
• 故障等级：P1 (高优先级)
• 业务影响：中等
• 恢复时间：13分钟
• 处理团队：张三、李四、王五、赵六
• 后续跟进：代码修复和监控优化

🎯 故障处理流程已全部结束，感谢团队协作！`

      chatMessages.value[1][cardIndex].actions = [
        { id: 'view_report', text: '查看完整简报', type: 'primary', icon: 'MessageCircle' },
        { id: 'view_dayu_record', text: '查看大禹记录', type: 'secondary', icon: 'MessageCircle' },
      ]
    }
  }, 2000)
}

// 方法
function selectChat(chat: any) {
  selectedChat.value = chat
  chat.unread = 0

  // 确保聊天有消息数组
  if (!chatMessages.value[chat.id]) {
    chatMessages.value[chat.id] = []
  }

  // 如果选择的是故障处理群聊(ID为1)，且还没有开始故障处理流程，则开始故障处理流程
  if (chat.id === 1 && chatMessages.value[1]) {
    // 检查是否已经有Jarvis的故障处理消息（不包括系统消息）
    const hasJarvisMessages = chatMessages.value[1].some(msg => msg.senderId === 'jarvis')
    if (!hasJarvisMessages) {
      simulateAIAgentFaultHandling()
    }
  }

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

function sendMessage() {
  if (!newMessage.value.trim() || !selectedChat.value)
    return

  const message = {
    ...createMessage('me', newMessage.value),
    id: (currentMessages.value.length + 1),
  }

  // 添加到当前聊天的消息列表
  chatMessages.value[selectedChat.value.id].push(message)

  // 更新聊天列表中的最后一条消息
  const chatIndex = chatList.value.findIndex(c => c.id === selectedChat.value.id)
  if (chatIndex !== -1) {
    chatList.value[chatIndex].lastMessage = newMessage.value
    chatList.value[chatIndex].time = message.time
  }

  // 检查是否是故障报告消息
  const isFaultReport = newMessage.value.includes('出故障了') || newMessage.value.includes('用户无法进入房间')

  // 检查是否是@Jarvis命令（在故障处理群聊中）
  const isJarvisCommand = selectedChat.value.id === 1 && selectedChat.value.isGroup && newMessage.value.includes('@Jarvis')

  // 如果是向Jarvis发送故障消息，显示交互卡片
  if (selectedChat.value.id === 2 && selectedChat.value.name === 'Jarvis' && isFaultReport) {
    showJarvisFaultCard()
  }

  // 如果是@Jarvis命令，立即检测并响应
  if (isJarvisCommand) {
    // 创建临时消息对象用于检测
    const tempMessage = {
      senderId: 'me',
      senderName: '我',
      content: newMessage.value,
      timestamp: Date.now(),
    }

    // 立即检测@Jarvis命令
    setTimeout(() => {
      detectServiceInquiries([tempMessage])
    }, 100) // 100ms后检测，确保消息已添加
  }

  newMessage.value = ''

  // 模拟自动回复（与"我"聊天时不自动回复）
  if (selectedChat.value.id !== 7) { // 7是"我"的chatId
    if (!(selectedChat.value.id === 2 && isFaultReport) && !isJarvisCommand) {
      // 如果不是故障报告或@Jarvis命令，正常回复
      simulateAutoReply()
    }
  }
}

// 点击预设消息
function clickPresetMessage(presetText: string) {
  newMessage.value = presetText
  sendMessage()
}

// 显示Jarvis故障处理卡片
function showJarvisFaultCard() {
  const messages = [
    {
      content: '检测到用户无法进入房间的故障，建议创建协作群聊快速解决问题。',
      type: 'text' as const,
    },
    {
      content: '我可以为您创建故障处理群聊，邀请相关技术人员协作解决问题。',
      type: 'interactive_card' as const,
      actions: [
        {
          id: 'create_fault_group',
          text: '创建故障群',
          type: 'primary',
          icon: 'Users',
        },
        {
          id: 'cancel',
          text: '暂不需要',
          type: 'secondary',
        },
      ],
    },
  ]

  sendMessagesInQueue(messages)
}

// 处理交互卡片按钮点击
function handleCardAction(actionId: string) {
  if (actionId === 'create_fault_group') {
    const messages = [
      {
        content: '好的，我正在为您创建故障处理群聊...',
        type: 'text' as const,
      },
      {
        content: '正在邀请相关技术人员加入群聊...',
        type: 'text' as const,
      },
    ]

    sendMessagesInQueue(messages)

    // 创建群聊
    setTimeout(() => {
      createGroupChatFromJarvis()
    }, 3000)
  }
  else if (actionId === 'cancel') {
    const messages = [
      {
        content: '好的，已取消群聊创建。',
        type: 'text' as const,
      },
      {
        content: '如果需要协作处理故障，随时可以联系我创建群聊。',
        type: 'text' as const,
      },
    ]

    sendMessagesInQueue(messages)
  }
  else if (actionId === 'go_to_group') {
    const messages = [
      {
        content: '好的，正在为您切换到故障处理群聊...',
        type: 'text' as const,
        chatId: 2, // 发送到Jarvis聊天
      },
    ]

    sendMessagesInQueue(messages)

    // 切换到群聊并发送确认消息
    setTimeout(() => {
      const groupChat = chatList.value.find(chat => chat.id === 1)
      if (groupChat) {
        selectChat(groupChat)

        const switchMessages = [
          {
            content: '已切换到故障处理群聊，您可以在这里与团队成员协作解决问题。',
            type: 'text' as const,
            chatId: 2, // 发送到Jarvis聊天
          },
        ]

        sendMessagesInQueue(switchMessages)
      }
    }, 2000)
  }
  else if (actionId === 'stay_here') {
    const messages = [
      {
        content: '好的，群聊已创建完成。',
        type: 'text' as const,
      },
      {
        content: '您可以随时在左侧聊天列表中找到故障处理群聊。',
        type: 'text' as const,
      },
    ]

    sendMessagesInQueue(messages)
  }
  else if (actionId === 'view_autonomous_plan') {
    // 查看自主规划详情
    const planMessages = [
      {
        content: `🧠 AI Agent自主规划系统详情：

🎯 当前目标：${agentState.value.currentGoal}
📊 处理进度：${agentState.value.progress}%
🔄 当前阶段：${agentState.value.currentPhase}

📋 自主决策流程：
1. 实时感知群内动态和处理进展
2. 基于目标驱动的智能分析
3. 自主调用适配工具执行操作
4. 动态调整策略和优先级
5. 持续监控并推动流程进展

🔧 可用工具集：
• 历史故障检索分析
• 智能人员邀请协调
• 自动电话通知升级
• AI根因定位诊断
• 实时进度监控同步`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(planMessages)
  }
  else if (actionId === 'view_real_time_analysis') {
    // 查看实时分析面板
    const analysisMessages = [
      {
        content: `📊 AI Agent实时分析面板：

🔍 群内动态监控：
• 团队活跃度：${agentState.value.contextAnalysis.teamActivity} 条消息/30秒
• 进展指标：${agentState.value.contextAnalysis.progressIndicators.length} 个积极信号
• 阻塞因素：${agentState.value.contextAnalysis.blockers.length} 个潜在问题

🎯 下一步行动计划：
${agentState.value.contextAnalysis.nextActions.map(action => `• ${action}`).join('\n')}

⏱️ 最后分析时间：${new Date(agentState.value.lastAnalysisTime).toLocaleTimeString()}`,
        type: 'interactive_card' as const,
        actions: [
          { id: 'refresh_analysis', text: '刷新分析', type: 'primary', icon: 'MessageCircle' },
          { id: 'view_decision_log', text: '决策日志', type: 'secondary', icon: 'MessageCircle' },
        ],
      },
    ]
    sendMessagesInQueue(analysisMessages)
  }
  else if (actionId === 'view_analysis_details') {
    // 查看自主分析详情
    const detailMessages = [
      {
        content: `🤖 AI Agent自主分析详细报告：

📈 实时监控数据：
• 消息频率：${agentState.value.contextAnalysis.teamActivity} 条/30秒
• 关键词匹配：进展(${agentState.value.contextAnalysis.progressIndicators.length}) vs 阻塞(${agentState.value.contextAnalysis.blockers.length})
• 团队响应速度：正常

🧠 决策推理过程：
• 基于目标导向的优先级排序
• 多维度数据融合分析
• 动态风险评估和策略调整
• 自主触发条件匹配

🎯 推荐行动方案：
${agentState.value.contextAnalysis.nextActions.map(action => `• ${action}`).join('\n')}`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(detailMessages)
  }
  else if (actionId === 'adjust_strategy') {
    // 调整策略
    const strategyMessages = [
      {
        content: `🔧 AI Agent策略调整：

基于当前分析结果，自主调整处理策略：

📊 当前状态评估：
• 目标完成度：${agentState.value.progress}%
• 团队协作效率：${agentState.value.contextAnalysis.teamActivity > 2 ? '高效' : '需要推动'}
• 潜在风险等级：${agentState.value.contextAnalysis.blockers.length > 0 ? '中等' : '低'}

🎯 策略优化建议：
• 加强实时监控频率
• 优化工具调用时机
• 动态调整团队协作节奏
• 提前预警潜在风险点`,
        type: 'interactive_card' as const,
        actions: [
          { id: 'apply_strategy', text: '应用新策略', type: 'primary', icon: 'MessageCircle' },
          { id: 'keep_current', text: '保持当前策略', type: 'secondary', icon: 'MessageCircle' },
        ],
      },
    ]
    sendMessagesInQueue(strategyMessages)
  }
  else if (actionId === 'view_todo') {
    // 查看TODO详细计划
    const todoMessages = [
      {
        content: '📋 故障处理详细计划：',
        type: 'interactive_card' as const,
        actions: [
          { id: 'close_todo', text: '关闭', type: 'secondary' },
        ],
      },
    ]
    sendMessagesInQueue(todoMessages)
  }
  else if (actionId === 'tool_search') {
    // 查看检索结果详情
    const searchMessages = [
      {
        content: '🔍 相似故障检索详细结果：\\n\\n故障1: 2024-12-15 WebSocket连接超时 (恢复时间: 16分钟)\\n故障2: 2024-11-28 房间服务异常 (恢复时间: 22分钟)\\n故障3: 2024-10-10 连接池泄漏 (恢复时间: 15分钟)\\n\\n建议处理方案: 优先检查WebSocket服务状态',
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(searchMessages)
  }
  else if (actionId === 'tool_invite') {
    // 查看邀请结果
    const inviteMessages = [
      {
        content: '👥 业务人员邀请结果：\\n\\n✅ 房间业务负责人 - 李经理 (已加入)\\n✅ 技术架构师 - 王工 (已加入)\\n⏳ 产品负责人 - 张总监 (邀请中)',
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(inviteMessages)
  }
  else if (actionId === 'tool_call') {
    // 查看通话记录
    const callMessages = [
      {
        content: '📞 电话呼叫记录：\\n\\n呼叫对象: 李经理 (房间业务负责人)\\n呼叫时间: 14:32:15\\n通话时长: 2分30秒\\n通话状态: 已接听\\n\\n通话要点:\\n- 确认故障影响范围\\n- 同意立即处理\\n- 将配合故障处理工作',
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(callMessages)
  }
  else if (actionId === 'tool_analysis') {
    // 查看分析报告
    const analysisMessages = [
      {
        content: '🔧 根因定位分析报告：\\n\\n故障组件: WebSocket服务\\n故障类型: 内存泄漏\\n影响范围: 127个用户连接\\n\\n技术细节:\\n- 连接池未正确释放\\n- 内存使用持续增长\\n- 14:30开始出现异常\\n\\n建议方案:\\n1. 立即重启WebSocket服务\\n2. 修复连接池清理逻辑\\n3. 加强监控告警',
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(analysisMessages)
  }
  else if (actionId === 'view_report') {
    // 查看完整故障简报
    const reportMessages = [
      {
        content: `📋 故障处理完整简报：

【基本信息】
故障ID: #2025010701
故障类型: WebSocket服务异常
发生时间: 2025-01-07 14:30
恢复时间: 2025-01-07 14:43
故障时长: 13分钟

【影响范围】
受影响用户: 127人
受影响功能: 房间进入
业务损失: 约2.1万元

【处理过程】
14:30 - 故障检测
14:32 - 创建处理群
14:35 - 根因定位
14:38 - 服务重启
14:43 - 功能恢复

【根本原因】
连接池内存泄漏导致WebSocket服务崩溃

【修复措施】
1. 重启WebSocket服务(临时)
2. 修复连接池清理逻辑(永久)
3. 增强监控告警机制

【预防措施】
1. 定期内存使用检查
2. 连接池健康监控
3. 自动故障检测优化`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(reportMessages)
  }
  else if (actionId === 'download_report') {
    // 下载简报
    const downloadMessages = [
      {
        content: `📥 故障简报下载链接已生成：

文件名: 故障简报_2025010701.pdf
文件大小: 2.3MB
有效期: 7天

下载地址: https://fault-reports.company.com/download/2025010701

✅ 简报已同步发送至相关负责人邮箱`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(downloadMessages)
  }
  else if (actionId.startsWith('cloud_call_')) {
    // 处理云呼叫请求
    const contactName = actionId.replace('cloud_call_', '')
    executeCloudCall(contactName)
  }
  else if (actionId === 'decline_call') {
    // 拒绝云呼叫
    const declineMessages = [
      {
        content: `好的，已取消云呼叫服务。

如果后续需要联系相关人员，随时可以告诉我，我会立即为您提供云呼叫协助。`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(declineMessages)
  }
  else if (actionId === 'call_summary') {
    // 查看通话记录
    const summaryMessages = [
      {
        content: `📞 云呼叫详细记录：

【通话基本信息】
呼叫时间：${new Date().toLocaleTimeString()}
通话时长：2分15秒
通话质量：良好
呼叫结果：成功接通

【呼叫过程】
00:00 - 开始拨号
00:03 - 正在连接
00:06 - 对方响铃
00:10 - 通话接通
02:25 - 通话结束

【通话要点】
• 确认收到故障处理通知
• 了解当前处理进度和团队情况
• 同意立即参与故障协作
• 将配合提供相关技术支持
• 确认可随时联系协调

【后续行动】
✅ 已通知故障处理情况
✅ 已同步当前故障处理状态
✅ 已确认协作参与意愿`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(summaryMessages)
  }
  else if (actionId === 'view_coordination_status') {
    // 查看协调状态
    const statusMessages = [
      {
        content: `🤝 团队协调状态详情：

📊 当前协作情况：
• 在线成员：4人
• 活跃参与：张三、李四、王五、赵六
• 响应速度：平均2分钟内回复
• 协作效率：高效

🔍 智能感知状态：
• 消息监控：实时分析中
• 关键词检测：联系困难、找不到、打不通等
• 响应机制：2秒内提供云呼叫协助
• 成功率：100%（历史数据）

📞 云呼叫工具状态：
• 服务状态：正常运行
• 可用渠道：手机、座机、企业微信
• 平均接通率：95%
• 平均响应时间：8秒

💡 如需联系任何人员，直接在群内提及"联系不上XXX"，我将立即提供协助`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(statusMessages)
  }
  else if (actionId === 'manual_cloud_call') {
    // 手动云呼叫
    const manualCallMessages = [
      {
        content: `📞 手动云呼叫服务

请告诉我您需要联系的人员姓名，我将立即为您发起云呼叫。

🔧 支持的呼叫方式：
• 手机号码呼叫
• 座机号码呼叫
• 企业微信语音通话
• 钉钉语音通话

💡 使用方法：
直接回复"呼叫XXX"或"联系XXX"即可`,
        type: 'interactive_card' as const,
        actions: [
          { id: 'cloud_call_李明', text: '呼叫李明', type: 'primary', icon: 'Phone' },
          { id: 'cloud_call_张工', text: '呼叫张工', type: 'primary', icon: 'Phone' },
          { id: 'cloud_call_王经理', text: '呼叫王经理', type: 'primary', icon: 'Phone' },
        ],
      },
    ]
    sendMessagesInQueue(manualCallMessages)
  }
  else if (actionId === 'check_service_status') {
    // 检查服务状态
    const statusMessages = [
      {
        content: `🔍 正在检查服务状态...

📊 系统监控结果：
• WebSocket服务：异常 ❌
• 数据库连接：正常 ✅
• 缓存服务：正常 ✅
• 负载均衡：正常 ✅

⚠️ 发现问题：
• WebSocket服务进程异常退出
• 端口8080无响应
• 内存使用率异常增长

💡 建议立即重启WebSocket服务进行临时恢复`,
        type: 'interactive_card' as const,
        actions: [
          { id: 'restart_service', text: '重启服务', type: 'primary', icon: 'MessageCircle' },
          { id: 'view_detailed_status', text: '查看详细状态', type: 'secondary', icon: 'MessageCircle' },
        ],
      },
    ]
    sendMessagesInQueue(statusMessages)
  }
  else if (actionId === 'view_analysis_progress') {
    // 查看分析进度
    const progressMessages = [
      {
        content: `📊 AI Agent分析进度详情：

🔍 根因定位分析：
• 系统日志分析：进行中... 60%
• 性能指标评估：进行中... 75%
• 错误模式识别：进行中... 45%

📚 相似故障检索：
• 历史案例匹配：进行中... 80%
• 成功方案提取：进行中... 65%
• 风险评估分析：进行中... 55%

⏱️ 预计完成时间：1分30秒`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(progressMessages)
  }
  else if (actionId === 'view_similar_cases') {
    // 查看相似案例
    const casesMessages = [
      {
        content: `📚 相似故障案例检索：

【案例1】2024-12-15
• 故障类型：WebSocket连接超时
• 影响用户：89人
• 恢复时间：16分钟
• 解决方案：重启服务+连接池优化

【案例2】2024-11-28
• 故障类型：房间服务异常
• 影响用户：156人
• 恢复时间：22分钟
• 解决方案：数据库连接池修复

【案例3】2024-10-10
• 故障类型：连接池内存泄漏
• 影响用户：203人
• 恢复时间：15分钟
• 解决方案：重启+代码修复

💡 基于历史经验，建议优先重启服务快速恢复`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(casesMessages)
  }
  else if (actionId === 'view_diagnosis_progress') {
    // 查看诊断进度
    const progressMessages = [
      {
        content: `🔍 MCP根因定位工具执行详情：

📊 当前执行状态：
• 工具连接：已建立
• 数据采集：进行中
• 算法分析：排队中
• 结果生成：等待中

🛠️ 执行步骤：
1. ✅ 连接监控系统
2. 🔄 获取实时性能数据
3. ⏳ 分析历史趋势
4. ⏳ 检查变更记录
5. ⏳ 运行根因算法
6. ⏳ 生成诊断报告

⏱️ 预计剩余时间：15秒`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(progressMessages)
  }
  else if (actionId === 'view_detailed_diagnosis') {
    // 查看详细诊断
    const detailMessages = [
      {
        content: `🔍 MCP根因定位工具详细诊断报告：

【系统性能分析】
• CPU使用率：45% (正常范围)
• 内存使用率：85% (超出阈值80%)
• 磁盘I/O：正常
• 网络延迟：正常

【服务健康检查】
• 进程状态：运行中
• 端口监听：正常
• 连接池状态：异常 (连接泄漏)
• 响应时间：平均450ms (正常200ms)

【变更影响分析】
• 变更时间：2小时前 14:30
• 变更版本：v2.1.2 → v2.1.3
• 变更模块：连接池管理器
• 影响评估：中等风险

【根因链路追踪】
1. 部署新版本 → 连接池配置变更
2. 高并发请求 → 连接对象创建增加
3. 清理机制缺陷 → 连接对象未释放
4. 内存持续增长 → 性能逐步下降
5. 响应时间增加 → 用户体验受影响

【修复优先级】
🔴 P0: 立即回滚版本 (恢复服务性能)
🟡 P1: 修复连接池清理逻辑
🟢 P2: 增强监控告警机制`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(detailMessages)
  }
  else if (actionId === 'execute_rollback') {
    // 执行回滚
    const rollbackMessages = [
      {
        content: `🔄 正在执行版本回滚...

📋 回滚计划：
• 目标版本：v2.1.2 (稳定版本)
• 回滚范围：连接池管理模块
• 预计时间：3-5分钟
• 影响评估：短暂服务重启

⚠️ 注意事项：
• 回滚期间服务将短暂不可用
• 建议在业务低峰期执行
• 回滚后需验证服务状态

💡 是否确认执行回滚操作？`,
        type: 'interactive_card' as const,
        actions: [
          { id: 'confirm_rollback', text: '确认回滚', type: 'primary', icon: 'MessageCircle' },
          { id: 'cancel_rollback', text: '取消操作', type: 'secondary', icon: 'MessageCircle' },
        ],
      },
    ]
    sendMessagesInQueue(rollbackMessages)
  }
  else if (actionId === 'confirm_rollback') {
    // 确认回滚
    const confirmMessages = [
      {
        content: `✅ 版本回滚执行完成

📊 回滚结果：
• 版本状态：v2.1.3 → v2.1.2 ✅
• 服务状态：已重启并恢复正常
• 性能指标：已恢复到正常水平
• 响应时间：200ms (恢复正常)

🔍 验证结果：
• 内存使用率：65% (正常)
• 连接池状态：正常
• 错误率：0.1% (正常水平)
• 用户反馈：性能问题已解决

💡 后续建议：
1. 监控服务稳定性24小时
2. 修复v2.1.3版本连接池问题
3. 完善测试用例覆盖连接池场景`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(confirmMessages)
  }
  else if (actionId === 'cancel_rollback') {
    // 取消回滚
    const cancelMessages = [
      {
        content: `❌ 已取消版本回滚操作

💡 其他处理建议：
• 可以尝试重启服务释放内存
• 调整连接池参数临时缓解
• 联系开发团队紧急修复

如需重新考虑回滚，随时可以告诉我。`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(cancelMessages)
  }
  else if (actionId === 'view_change_progress') {
    // 查看变更检查进度
    const progressMessages = [
      {
        content: `🔍 变更检查进度详情：

📊 检查状态：
• 连接变更管理系统：✅ 完成
• 查询部署记录：进行中... 80%
• 分析配置变更：进行中... 60%
• 检查代码提交：等待中...

📋 已发现变更：
• 代码部署：2次
• 配置调整：1次
• 数据库变更：0次

⏱️ 预计剩余时间：10秒`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(progressMessages)
  }
  else if (actionId === 'view_change_details') {
    // 查看变更详情
    const detailMessages = [
      {
        content: `📋 详细变更记录：

【变更记录1】
• 变更ID：CHG-2025010701
• 变更时间：2小时前 14:30
• 变更类型：代码部署
• 版本变更：v2.1.2 → v2.1.3
• 变更模块：连接池管理器
• 执行人员：张工 (工号:001234)
• 审批人员：李经理
• 变更内容：
  - 优化连接池清理逻辑
  - 增加内存监控功能
  - 调整超时参数配置
• 影响评估：中等风险
• 回滚方案：已准备

【变更记录2】
• 变更ID：CHG-2025010602
• 变更时间：昨天 16:45
• 变更类型：配置调整
• 变更内容：数据库连接池大小 50→80
• 执行人员：李工 (工号:001235)
• 影响评估：低风险
• 验证结果：✅ 通过

🔍 变更影响分析：
• v2.1.3版本部署后，服务性能出现明显下降
• 内存使用率从65%上升到85%
• 响应时间从200ms增加到450ms
• 错误率从0.1%上升到2.1%

💡 建议措施：
1. 立即回滚v2.1.3到v2.1.2版本
2. 重新测试连接池清理逻辑
3. 加强变更前的性能测试`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(detailMessages)
  }
  else if (actionId === 'view_status_progress') {
    // 查看状态检查进度
    const statusProgressMessages = [
      {
        content: `📊 状态检查进度详情：

🛠️ MCP工具执行状态：
• 连接监控系统：✅ 完成
• 获取实时指标：进行中... 90%
• 分析性能趋势：进行中... 70%
• 检查健康状态：进行中... 50%

📈 已获取指标：
• CPU使用率：✅ 已获取
• 内存使用率：✅ 已获取
• 连接数统计：✅ 已获取
• 响应时间：✅ 已获取
• 错误率统计：进行中...

⏱️ 预计剩余时间：15秒`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(statusProgressMessages)
  }
  else if (actionId === 'view_status_details') {
    // 查看状态详细指标
    const statusDetailMessages = [
      {
        content: `📊 详细性能指标报告：

【CPU使用率分析】
• 当前值：45%
• 最近1小时平均：42%
• 最近24小时峰值：68%
• 趋势：稳定
• 状态：✅ 正常

【内存使用率分析】
• 当前值：85%
• 最近1小时趋势：持续上升
• 内存泄漏检测：⚠️ 疑似泄漏
• 垃圾回收频率：异常增高
• 状态：❌ 异常

【连接数分析】
• 当前活跃连接：1,247
• 正常范围：800-1,000
• 连接池状态：⚠️ 接近饱和
• 连接泄漏检测：发现异常
• 状态：⚠️ 异常

【响应时间分析】
• 当前平均响应时间：450ms
• SLA要求：<300ms
• 最近1小时趋势：持续恶化
• P95响应时间：780ms
• P99响应时间：1.2s
• 状态：❌ 超出SLA

【错误率分析】
• 当前错误率：2.1%
• 正常阈值：<0.5%
• 主要错误类型：连接超时
• 错误趋势：上升
• 状态：❌ 异常

🎯 综合诊断：
服务整体状态异常，主要问题集中在内存泄漏和连接池管理，建议立即采取恢复措施。`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(statusDetailMessages)
  }
  else if (actionId === 'view_completion_progress') {
    // 查看故障结束进度
    const completionProgressMessages = [
      {
        content: `✅ 故障结束流程进度：

📋 简报生成：
• 收集故障数据：✅ 完成
• 分析处理过程：✅ 完成
• 统计影响范围：✅ 完成
• 生成简报文档：✅ 完成

🛠️ MCP工具调用：
• 连接大禹平台：✅ 完成
• 上传故障数据：进行中... 80%
• 创建故障记录：等待中...
• 通知相关人员：等待中...

📊 平台录入：
• 故障等级评估：✅ 完成
• 处理团队记录：✅ 完成
• 时间线整理：✅ 完成
• 后续跟进计划：进行中...

⏱️ 预计剩余时间：30秒`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(completionProgressMessages)
  }
  else if (actionId === 'view_report') {
    // 查看完整简报
    const reportMessages = [
      {
        content: `📋 故障处理完整简报

【基本信息】
• 故障ID：#2025010701
• 故障等级：P1 (高优先级)
• 开始时间：2025-01-07 14:15
• 结束时间：2025-01-07 14:28
• 处理时长：13分钟
• 影响用户：127人

【故障描述】
• 故障现象：用户无法进入房间
• 影响范围：全部房间功能
• 业务损失：约2.1万元/小时

【根因分析】
• 根本原因：WebSocket连接池内存泄漏
• 触发条件：v2.1.3版本部署后高并发访问
• 技术细节：连接池清理机制存在缺陷

【处理过程】
1. 14:15 故障发现和报告
2. 14:17 创建处理群聊，拉入相关人员
3. 14:20 AI Agent启动自主分析
4. 14:22 完成根因定位
5. 14:25 执行服务重启恢复
6. 14:28 验证恢复完成

【处理团队】
• 张三：服务器运维
• 李四：应用开发
• 王五：测试验证
• 赵六：用户沟通
• Jarvis：AI智能协助

【解决方案】
• 临时方案：重启WebSocket服务
• 根本方案：修复连接池清理逻辑
• 预防措施：增强内存监控告警

【后续跟进】
• 代码修复：已完成并部署
• 监控优化：进行中
• 流程改进：计划中`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(reportMessages)
  }
  else if (actionId === 'view_dayu_record') {
    // 查看大禹记录
    const dayuMessages = [
      {
        content: `📊 大禹平台故障记录

【平台信息】
• 记录ID：DY-2025010701
• 录入时间：2025-01-07 14:30
• 录入方式：MCP工具自动录入
• 状态：✅ 已完成

【故障分类】
• 一级分类：系统故障
• 二级分类：服务异常
• 三级分类：内存泄漏
• 影响等级：P1

【业务影响】
• 影响系统：房间服务
• 影响用户数：127人
• 业务损失：2.1万元
• 恢复时间：13分钟

【处理记录】
• 发现方式：用户反馈
• 响应时间：2分钟
• 定位时间：7分钟
• 恢复时间：4分钟
• 处理效率：优秀

【质量评估】
• 处理及时性：A级
• 沟通协调：A级
• 技术方案：A级
• 用户满意度：95%

【经验总结】
• 成功经验：AI Agent自主分析提高效率
• 改进建议：加强变更前测试
• 知识沉淀：已更新故障案例库

【相关链接】
• 大禹平台：https://dayu.internal/fault/DY-2025010701
• 监控面板：https://monitor.internal/dashboard
• 代码修复：https://git.internal/fix/memory-leak`,
        type: 'text' as const,
      },
    ]
    sendMessagesInQueue(dayuMessages)
  }
  else if (actionId.startsWith('autonomous_tool_') && actionId.endsWith('_details')) {
    // 处理自主工具调用详情查看
    const toolType = actionId.replace('autonomous_tool_', '').replace('_details', '')

    let detailMessages: any[] = []

    if (toolType === 'search') {
      detailMessages = [
        {
          content: `🔍 AI Agent自主检索详细分析：

🧠 推理过程：
• 目标导向：快速获取历史处理经验
• 算法选择：机器学习相似度匹配
• 数据源：故障知识库 + 处理案例库
• 权重分配：故障类型(40%) + 影响范围(30%) + 恢复时间(30%)

📊 检索结果：
【高相似度案例1】
时间: 2024-12-15 14:25
相似度: 94.2%
问题: WebSocket连接超时
恢复策略: 服务重启 + 连接池优化

【高相似度案例2】
时间: 2024-11-28 09:15
相似度: 89.7%
问题: 房间服务异常
恢复策略: 数据库连接池修复

🎯 AI决策逻辑：
基于历史成功率100%的案例，自主决定采用双轨策略：立即重启恢复 + 并行根因修复`,
          type: 'text' as const,
        },
      ]
    }
    else if (toolType === 'invite') {
      detailMessages = [
        {
          content: `👥 AI Agent智能人员邀请分析：

🧠 推理过程：
• 故障类型分析：WebSocket服务异常
• 技能匹配算法：专业领域 + 历史协作效率
• 团队配置优化：技术修复 + 业务协调 + 监控分析
• 响应时间预测：基于历史数据建模

🎯 智能决策结果：
【核心技术专家】
✅ WebSocket专家李工 - 匹配度95%
   专业领域：WebSocket架构 + 性能优化
   历史协作：3次类似故障，平均恢复时间12分钟

【业务协调负责人】
✅ 业务负责人王经理 - 匹配度88%
   协调能力：用户沟通 + 影响评估
   决策权限：P1级故障处理授权

【监控分析专家】
✅ 监控专家张工 - 匹配度92%
   技术能力：系统监控 + 数据分析
   响应速度：平均2分钟内加入协作

💡 AI优化建议：团队配置已达最优，覆盖故障处理全流程`,
          type: 'text' as const,
        },
      ]
    }
    else if (toolType === 'call') {
      detailMessages = [
        {
          content: `📞 AI Agent智能通知策略分析：

🧠 推理过程：
• 影响评估：127名用户 > 100人阈值
• 业务损失：2.1万/小时 > P1级标准
• 通知策略：自主触发升级流程
• 优先级排序：业务影响 + 决策权限 + 响应能力

🎯 智能执行结果：
【通话记录1】
对象：李经理 (业务负责人)
时长：2分30秒
结果：确认影响范围，同意P1级处理
决策：立即启动应急预案

【通话记录2】
对象：王总监 (技术负责人)
时长：1分45秒
结果：技术资源调配，专家团队就位
决策：并行恢复策略批准

【通话记录3】
对象：张主管 (运维负责人)
时长：3分10秒
结果：监控数据确认，系统状态分析
决策：根因定位方向确定

💡 AI决策优化：基于通话结果，自主调整目标时间从15分钟缩短至10分钟`,
          type: 'text' as const,
        },
      ]
    }
    else if (toolType === 'analysis') {
      detailMessages = [
        {
          content: `🔧 AI Agent智能根因定位分析：

🧠 推理过程：
• 多维度数据融合：系统指标 + 日志模式 + 用户反馈
• 因果推理算法：贝叶斯网络 + 决策树分析
• 故障传播路径：服务依赖图谱分析
• 概率计算：各种可能原因的置信度评估

📊 智能诊断结果：
【根因置信度排序】
1. WebSocket连接池内存泄漏 - 置信度: 94.7%
   证据：内存使用异常增长 + OutOfMemoryError
   影响路径：连接池 → 内存溢出 → 进程崩溃

2. 网络连接异常 - 置信度: 12.3%
   证据：部分用户连接失败
   排除原因：网络监控正常，非主要因素

3. 数据库连接问题 - 置信度: 8.1%
   证据：部分查询超时
   排除原因：数据库服务正常运行

🎯 AI生成恢复策略：
• 立即策略：重启WebSocket服务 (预计恢复时间: 2分钟)
• 根本策略：修复连接池清理逻辑 (预计完成时间: 30分钟)
• 预防策略：增强内存监控 + 自动告警机制

💡 智能优化：基于根因分析，自主协调团队执行双轨并行恢复方案`,
          type: 'text' as const,
        },
      ]
    }

    sendMessagesInQueue(detailMessages)
  }
  else if (actionId.startsWith('autonomous_tool_') && actionId.endsWith('_reasoning')) {
    // 处理自主工具推理逻辑查看
    const toolType = actionId.replace('autonomous_tool_', '').replace('_reasoning', '')

    const reasoningMessages = [
      {
        content: `🧠 AI Agent推理逻辑详解 (${toolType})：

🎯 目标导向分析：
• 主目标：${agentState.value.currentGoal}
• 当前阶段：${agentState.value.currentPhase}
• 进度评估：${agentState.value.progress}%

🔍 决策树分析：
• 输入条件：故障类型、影响范围、历史数据
• 权重计算：业务影响(40%) + 技术复杂度(35%) + 时间紧迫性(25%)
• 阈值判断：自主触发条件匹配
• 输出决策：最优工具选择 + 执行参数

⚡ 实时适应机制：
• 环境感知：群内动态监控
• 反馈学习：执行结果评估
• 策略调整：动态优化决策参数
• 目标修正：基于实际进展调整预期

💡 下一步推理：基于当前执行结果，AI Agent将自主评估是否需要调整策略或调用其他工具`,
        type: 'text' as const,
      },
    ]

    sendMessagesInQueue(reasoningMessages)
  }
  else if (actionId.startsWith('tool_') && actionId.endsWith('_details')) {
    // 处理工具调用详情查看
    const toolType = actionId.replace('tool_', '').replace('_details', '')

    let detailMessages: any[] = []

    if (toolType === 'search') {
      detailMessages = [
        {
          content: '🔍 相似故障检索详细结果：\\n\\n【故障1】\\n时间: 2024-12-15 14:25\\n问题: WebSocket连接超时\\n影响: 89名用户\\n恢复时间: 16分钟\\n解决方案: 重启WebSocket服务\\n\\n【故障2】\\n时间: 2024-11-28 09:15\\n问题: 房间服务异常\\n影响: 156名用户\\n恢复时间: 22分钟\\n解决方案: 修复数据库连接池\\n\\n【故障3】\\n时间: 2024-10-10 16:40\\n问题: 连接池泄漏\\n影响: 203名用户\\n恢复时间: 15分钟\\n解决方案: 重启服务+代码修复\\n\\n💡 建议处理方案: 基于历史经验，优先检查WebSocket服务状态和连接池配置',
          type: 'text' as const,
        },
      ]
    }
    else if (toolType === 'invite') {
      detailMessages = [
        {
          content: '👥 业务人员邀请详细结果：\\n\\n【已邀请人员】\\n✅ 李经理 - 房间业务负责人\\n   - 联系方式: 138****1234\\n   - 加入状态: 已加入群聊\\n   - 加入时间: 14:33:15\\n\\n✅ 王工 - 技术架构师\\n   - 联系方式: 139****5678\\n   - 加入状态: 已加入群聊\\n   - 加入时间: 14:33:28\\n\\n⏳ 张总监 - 产品负责人\\n   - 联系方式: 137****9012\\n   - 邀请状态: 邀请中\\n   - 预计加入: 2分钟内\\n\\n💡 建议: 核心技术人员已到位，可以开始故障分析和处理',
          type: 'text' as const,
        },
      ]
    }
    else if (toolType === 'call') {
      detailMessages = [
        {
          content: '📞 电话呼叫详细记录：\\n\\n【通话信息】\\n呼叫对象: 李经理 (房间业务负责人)\\n呼叫时间: 14:32:15\\n接听时间: 14:32:18\\n通话时长: 2分30秒\\n通话质量: 良好\\n\\n【通话要点】\\n1. 确认故障影响范围: 127名用户无法进入房间\\n2. 业务影响评估: 预计损失约2.1万元/小时\\n3. 处理优先级: P1级故障，需立即处理\\n4. 协作安排: 李经理将协调业务侧配合\\n5. 沟通频率: 每10分钟同步一次进展\\n\\n💡 关键决策: 同意立即启动应急处理流程，业务侧全力配合技术恢复',
          type: 'text' as const,
        },
      ]
    }
    else if (toolType === 'analysis') {
      detailMessages = [
        {
          content: '🔧 根因定位详细分析报告：\\n\\n【系统状态分析】\\n- CPU使用率: 45% (正常)\\n- 内存使用率: 78% → 95% (异常增长)\\n- 磁盘I/O: 正常\\n- 网络连接: 正常\\n\\n【服务状态分析】\\n- WebSocket服务: 进程异常退出\\n- 端口8080: 无响应\\n- 连接池状态: 内存泄漏\\n- 错误日志: OutOfMemoryError\\n\\n【故障时间线】\\n14:25 - 内存使用开始异常增长\\n14:28 - WebSocket连接开始失败\\n14:30 - 服务进程崩溃\\n14:31 - 用户开始反馈问题\\n\\n【根本原因】\\n连接池清理机制失效，导致连接对象无法正确释放，引发内存泄漏，最终导致服务崩溃\\n\\n💡 修复建议:\\n1. 立即重启WebSocket服务 (临时恢复)\\n2. 修复连接池清理逻辑 (根本解决)\\n3. 增加内存监控告警 (预防措施)',
          type: 'text' as const,
        },
      ]
    }

    sendMessagesInQueue(detailMessages)
  }
}

// 模拟自动回复功能
function simulateAutoReply() {
  if (!selectedChat.value)
    return

  // 角色专业回复消息
  const characterMessages = {
    zhangsan: [
      '服务器状态正常',
      '我这边没有发现异常',
      '网络连接稳定',
      '正在检查日志',
      '系统运行良好',
      '监控数据显示正常',
      '我来重启一下服务',
      '已经处理完毕',
    ],
    lisi: [
      '代码逻辑没问题',
      '我检查一下数据库',
      '可能是缓存问题',
      '我来修复这个bug',
      '单元测试通过了',
      '代码审查完成',
      '已经提交修复',
      '功能开发完毕',
    ],
    wangwu: [
      '测试用例已准备好',
      '我来验证一下',
      '功能测试通过',
      '回归测试正常',
      '性能测试达标',
      '发现了一个小问题',
      '测试报告已生成',
      '质量检查完成',
    ],
    zhaoliu: [
      '用户反馈已收集',
      '我联系一下客户',
      '需求已确认',
      '优先级设为高',
      '产品规划更新了',
      '市场调研完成',
      '用户体验很好',
      '下个版本计划中',
    ],
    me: [
      '记录一下这个想法',
      '待办事项已更新',
      '这个问题需要思考',
      '备忘录已保存',
      '灵感记录完成',
      '计划安排好了',
      '笔记整理中',
      '想法很有趣',
    ],
  }

  const character = getCharacterByChatId(selectedChat.value.id)
  if (!character)
    return

  const messages = characterMessages[character.id as keyof typeof characterMessages]
  if (!messages)
    return

  const randomMessage = messages[Math.floor(Math.random() * messages.length)]

  setTimeout(() => {
    const replyMessage = {
      ...createMessage(character.id, randomMessage),
      id: (currentMessages.value.length + 1),
    }

    // 添加到当前聊天的消息列表
    chatMessages.value[selectedChat.value.id].push(replyMessage)

    // 更新聊天列表
    const chatIndex = chatList.value.findIndex(c => c.id === selectedChat.value.id)
    if (chatIndex !== -1) {
      chatList.value[chatIndex].lastMessage = randomMessage
      chatList.value[chatIndex].time = replyMessage.time
    }
  }, 1000) // 1秒后回复
}

function scrollToBottom() {
  nextTick(() => {
    // 查找 ScrollArea 内部的滚动容器
    const scrollArea = document.querySelector('.messages-container')
    if (scrollArea) {
      // ScrollArea 组件的实际滚动容器通常是其内部的 div
      const scrollContainer = scrollArea.querySelector('[data-radix-scroll-area-viewport]')
        || scrollArea.querySelector('.scroll-area-viewport')
        || scrollArea.querySelector('[data-overlayscrollbars-viewport]')
        || scrollArea

      if (scrollContainer) {
        // 使用平滑滚动
        scrollContainer.scrollTo({
          top: scrollContainer.scrollHeight,
          behavior: 'smooth',
        })
      }
    }
  })
}

function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    event.preventDefault()
    sendMessage()
  }
}

// 布局相关
const defaultLayout = [25, 75]
const navCollapsedSize = 4

function onCollapse() {
  isCollapsed.value = true
}

function onExpand() {
  isCollapsed.value = false
}

// 拉人入群功能
function inviteToGroup() {
  if (!selectedChat.value)
    return

  const newMembers = ['新用户1', '新用户2']

  // 延迟1秒后显示邀请消息
  setTimeout(() => {
    const inviteMessage = {
      id: (currentMessages.value.length + 1),
      type: 'system',
      content: `江辞辞 邀请 ${newMembers.join('、')} 加入群聊`,
      time: '',
      mentions: ['江辞辞', ...newMembers],
    }

    // 添加到当前聊天的消息列表
    chatMessages.value[selectedChat.value.id].push(inviteMessage)

    // 更新群成员数量
    selectedChat.value.memberCount += newMembers.length

    // 再延迟1秒后，新成员发送入群消息
    setTimeout(() => {
      const welcomeMessage = {
        ...createMessage('zhangsan', '欢迎新成员加入！大家一起努力解决问题。'),
        id: (currentMessages.value.length + 1),
      }
      chatMessages.value[selectedChat.value.id].push(welcomeMessage)
    }, 1000)
  }, 1000)
}

// 重置会话功能
function resetSession() {
  // 重置聊天列表，包含所有角色
  chatList.value = [
    createChatFromCharacter('jarvis', '你好，有什么可以帮助您的吗？', '20:30'),
    createChatFromCharacter('zhangsan', '服务器状态检查完毕', '20:25'),
    createChatFromCharacter('lisi', '代码审查已完成', '20:20'),
    createChatFromCharacter('wangwu', '测试用例执行中', '20:15'),
    createChatFromCharacter('zhaoliu', '需求文档已更新', '20:10'),
    createChatFromCharacter('me', '这是我的个人笔记', '20:05'),
  ]

  // 重置所有消息数据，包含所有角色的初始消息
  chatMessages.value = {
    2: [
      { ...createMessage('jarvis', '你好，有什么可以帮助您的吗？', '20:30'), id: 1 },
    ],
    3: [
      { ...createMessage('zhangsan', '你好，我是运维工程师张三，有什么服务器问题需要我协助吗？', '20:25'), id: 1 },
    ],
    4: [
      { ...createMessage('lisi', '嗨，我是开发工程师李四，有代码相关的问题可以找我。', '20:20'), id: 1 },
    ],
    5: [
      { ...createMessage('wangwu', '你好，我是测试工程师王五，负责质量保证工作。', '20:15'), id: 1 },
    ],
    6: [
      { ...createMessage('zhaoliu', '大家好，我是产品经理赵六，有产品需求问题可以联系我。', '20:10'), id: 1 },
    ],
    7: [
      { ...createMessage('me', '这里是我的个人笔记空间，可以记录一些想法和备忘。', '20:05'), id: 1 },
    ],
  }

  // 重新选择Jarvis聊天
  selectedChat.value = chatList.value[0]

  // 清空输入框
  newMessage.value = ''
}

// 从Jarvis创建群聊会话
function createGroupChatFromJarvis() {
  // 检查是否已经存在群聊会话
  const existingGroupChat = chatList.value.find(chat => chat.id === 1)
  if (existingGroupChat) {
    return // 如果已经存在，不重复创建
  }

  // 群聊创建进度消息
  const creatingMessages = [
    {
      content: '群聊创建中，正在配置协作环境...',
      type: 'text' as const,
      chatId: 2, // 发送到Jarvis聊天
    },
  ]

  sendMessagesInQueue(creatingMessages, 0) // 立即开始发送

  // 延迟1秒后创建群聊
  setTimeout(() => {
    // 创建新的群聊会话
    const groupChat = {
      id: 1,
      name: '【故障】2025-07-04 用户无法进入房间',
      avatar: '/logo/TT.png',
      lastMessage: 'Jarvis 创建了群聊',
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      unread: 1,
      isGroup: true,
      memberCount: 6, // Jarvis + 张三 + 李四 + 王五 + 赵六 + 用户自己
      status: '故障',
    }

    // 添加到聊天列表的开头
    chatList.value.unshift(groupChat)

    // 创建群聊的消息数据 - 先添加基础消息
    chatMessages.value[1] = [
      {
        id: 1,
        type: 'system',
        content: '欢迎加入群组',
        subContent: '你可以尝试以下操作，快速开始协作',
        time: '',
        actions: [
          { text: '置顶重要链接', type: 'secondary' },
          { text: '完善群信息', type: 'secondary' },
          { text: '完善群公告', type: 'secondary' },
        ],
      },
      {
        id: 2,
        type: 'system',
        content: 'Jarvis 创建群聊，并邀请 张三、李四、王五、赵六 加入群聊',
        time: '',
        mentions: ['Jarvis', '张三', '李四', '王五', '赵六'],
      },
    ]

    // 不自动开始故障处理流程，等待用户点击进入群聊

    // 群聊创建完成后的消息队列
    const completionMessages = [
      {
        content: '故障处理群聊已创建完成！',
        type: 'text' as const,
        chatId: 2, // 发送到Jarvis聊天
      },
      {
        content: '相关技术人员已加入协作，您可以前往群聊开始协作解决问题。',
        type: 'interactive_card' as const,
        chatId: 2, // 发送到Jarvis聊天
        actions: [
          {
            id: 'go_to_group',
            text: '前往群聊',
            type: 'primary',
            icon: 'MessageCircle',
          },
          {
            id: 'stay_here',
            text: '稍后查看',
            type: 'secondary',
          },
        ],
      },
    ]

    sendMessagesInQueue(completionMessages, 1000) // 1秒后开始发送
  }, 1000)
}

// 监听当前消息变化，自动滚动到底部
watch(currentMessages, () => {
  scrollToBottom()
}, { deep: true })

// 页面挂载时选择第一个聊天
onMounted(() => {
  if (chatList.value.length > 0) {
    selectChat(chatList.value[0])
  }

  // 初始滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})
</script>

<template>
  <div class="h-screen flex flex-col bg-white -m-6">
    <!-- 聊天主体 -->
    <div class="min-h-0 flex-1">
      <TooltipProvider :delay-duration="0">
        <ResizablePanelGroup
          id="chat-panel-group"
          direction="horizontal"
          class="h-full items-stretch"
        >
          <!-- 左侧聊天列表 -->
          <ResizablePanel
            id="chat-list-panel"
            :default-size="defaultLayout[0]"
            :collapsed-size="navCollapsedSize"
            collapsible
            :min-size="15"
            :max-size="35"
            :class="cn(isCollapsed && 'min-w-[50px] transition-all duration-300 ease-in-out')"
            @expand="onExpand"
            @collapse="onCollapse"
          >
            <div class="h-full flex flex-col border-r bg-background">
              <!-- 头部 -->
              <div class="h-69px flex-shrink-0 border-b p-4">
                <div v-if="!isCollapsed" class="flex items-center justify-between">
                  <div>
                    <h2 class="text-lg font-semibold">
                      对话列表
                    </h2>
                  </div>
                  <Button size="sm" variant="ghost" title="重置会话" class="gap-1" @click="resetSession">
                    <RotateCcw class="h-4 w-4" />
                    <span class="text-xs">重置会话</span>
                  </Button>
                </div>
                <div v-else class="flex justify-center">
                  <MessageCircle class="h-6 w-6" />
                </div>
              </div>

              <!-- 搜索框 -->
              <div v-if="!isCollapsed" class="flex-shrink-0 border-b p-4">
                <div class="relative">
                  <Search class="absolute left-3 top-1/2 h-4 w-4 transform text-muted-foreground -translate-y-1/2" />
                  <Input
                    v-model="searchValue"
                    placeholder="搜索聊天"
                    class="pl-9"
                  />
                </div>
              </div>

              <!-- 聊天列表 -->
              <ScrollArea class="flex-1">
                <div class="p-2 space-y-1">
                  <div
                    v-for="chat in filteredChatList"
                    :key="chat.id"
                    class="flex cursor-pointer items-center gap-3 rounded-lg p-3 transition-colors hover:bg-gray-100"
                    :class="{ 'bg-gray-50': selectedChat?.id === chat.id }"
                    @click="selectChat(chat)"
                  >
                    <div>
                      <Avatar
                        v-if="chat.isGroup && chat.name.includes('故障')"
                        class="h-12 w-12 border-2 border-red-500 ring-2 ring-red-200"
                      >
                        <AvatarFallback class="bg-red-50 text-sm text-red-500 font-medium">
                          故障
                        </AvatarFallback>
                      </Avatar>
                      <Avatar
                        v-else
                        class="h-12 w-12"
                      >
                        <AvatarImage :src="chat.avatar" :alt="chat.name" />
                        <AvatarFallback class="text-sm text-gray-600 font-medium">
                          {{ getCharacterByChatId(chat.id)?.fallbackText || chat.name.charAt(0) }}
                        </AvatarFallback>
                      </Avatar>
                    </div>

                    <div v-if="!isCollapsed" class="min-w-0 flex-1">
                      <div class="mb-1 flex items-center justify-between">
                        <p class="truncate text-sm text-gray-700 font-medium">
                          {{ chat.name }}
                        </p>
                        <span class="text-xs text-gray-500">{{ chat.time }}</span>
                      </div>
                      <div class="flex items-center justify-between">
                        <p class="truncate text-xs text-gray-600">
                          {{ chat.lastMessage }}
                        </p>
                        <!-- 未读消息数量 -->
                        <div v-if="chat.unread > 0" class="min-w-[20px] rounded-full bg-red-500 px-2 py-1 text-center text-xs text-white">
                          {{ chat.unread }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </div>
          </ResizablePanel>

          <ResizableHandle class="w-0" />

          <!-- 右侧消息区域 -->
          <ResizablePanel
            id="chat-messages-panel"
            :default-size="defaultLayout[1]"
            :min-size="50"
          >
            <div v-if="selectedChat" class="h-full flex flex-col">
              <!-- 聊天头部 -->
              <div class="flex-shrink-0 border-b bg-background/95 p-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <Avatar
                      v-if="selectedChat?.isGroup && selectedChat?.name.includes('故障')"
                      class="h-9 w-9 border-2 border-red-500 ring-2 ring-red-200"
                    >
                      <AvatarFallback class="bg-red-50 text-xs text-red-500 font-medium">
                        故障
                      </AvatarFallback>
                    </Avatar>
                    <Avatar
                      v-else
                      class="h-9 w-9"
                    >
                      <AvatarImage :src="selectedChat?.avatar" :alt="selectedChat?.name" />
                      <AvatarFallback class="text-xs text-gray-600 font-medium">
                        {{ getCharacterByChatId(selectedChat?.id)?.fallbackText || selectedChat?.name?.charAt(0) }}
                      </AvatarFallback>
                    </Avatar>

                    <div>
                      <div class="flex items-center gap-2">
                        <h3 class="text-base font-medium">
                          {{ selectedChat?.name }}
                        </h3>
                        <span class="rounded bg-green-100 px-2 py-0.5 text-xs text-green-700">
                          {{ selectedChat?.memberCount }}
                        </span>
                        <span class="rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-700">
                          {{ selectedChat?.status }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <Button size="icon" variant="ghost" title="邀请成员" @click="inviteToGroup">
                      <Users class="h-4 w-4" />
                    </Button>
                    <Button size="icon" variant="ghost">
                      <Phone class="h-4 w-4" />
                    </Button>
                    <Button size="icon" variant="ghost">
                      <Video class="h-4 w-4" />
                    </Button>
                    <Button size="icon" variant="ghost">
                      <MoreVertical class="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <!-- 消息列表 -->
              <ScrollArea class="messages-container flex-1 bg-white">
                <div class="p-4 space-y-3">
                  <div
                    v-for="message in currentMessages"
                    :key="message.id"
                  >
                    <!-- 系统消息 -->
                    <div v-if="message.type === 'system'" class="flex justify-center">
                      <div class="max-w-md text-center">
                        <!-- 欢迎消息 -->
                        <div v-if="message.actions" class="bg-white p-6">
                          <div class="mb-4">
                            <h3 class="mb-2 text-lg text-gray-900 font-medium">
                              {{ message.content }}
                            </h3>
                            <p class="text-sm text-gray-500">
                              {{ message.subContent }}
                            </p>
                          </div>

                          <!-- 操作按钮 -->
                          <div class="flex space-x-2">
                            <Button
                              v-for="action in message.actions"
                              :key="action.text"
                              :variant="action.type === 'primary' ? 'default' : 'outline'"
                              class="w-full"
                              size="sm"
                            >
                              {{ action.text }}
                            </Button>
                          </div>
                        </div>

                        <!-- 普通系统消息 -->
                        <div v-else class="inline-block rounded-full bg-blue-50 px-4 py-2 text-sm text-blue-700">
                          <template v-if="message.mentions && message.mentions.length > 0">
                            <span
                              v-for="mention in message.mentions"
                              :key="mention"
                              class="font-medium"
                            />
                            {{ message.content.replace(message.mentions.join(''), '').trim() }}
                          </template>
                          <template v-else>
                            {{ message.content }}
                          </template>
                        </div>
                      </div>
                    </div>

                    <!-- 交互卡片消息 -->
                    <div v-else-if="message.type === 'interactive_card'" class="flex items-start gap-2" :class="message.senderId === 'me' ? 'flex-row-reverse' : 'flex-row'">
                      <!-- 头像 -->
                      <Avatar class="h-8 w-8 flex-shrink-0">
                        <AvatarImage :src="message.avatar" :alt="message.senderName" />
                        <AvatarFallback class="text-xs">
                          {{ message.senderName?.charAt(0) }}
                        </AvatarFallback>
                      </Avatar>

                      <!-- 卡片内容 -->
                      <div class="max-w-[70%] min-w-0" :class="message.senderId === 'me' ? 'items-end' : 'items-start'">
                        <!-- 用户名和时间 -->
                        <div class="mb-1 flex items-center gap-2" :class="message.senderId === 'me' ? 'flex-row-reverse' : 'flex-row'">
                          <span class="text-sm text-foreground font-medium">{{ message.senderName }}</span>
                          <span v-if="message.time" class="text-xs text-muted-foreground">{{ message.time }}</span>
                        </div>

                        <!-- 交互卡片 -->
                        <div class="border rounded-lg bg-white p-4 shadow-sm">
                          <div class="mb-3 whitespace-pre-line text-sm text-gray-900 leading-relaxed">
                            {{ message.content }}
                          </div>

                          <!-- 操作按钮 -->
                          <div class="flex gap-2">
                            <Button
                              v-for="action in message.actions"
                              :key="action.id"
                              :variant="action.type === 'primary' ? 'default' : 'outline'"
                              size="sm"
                              class="gap-2"
                              @click="handleCardAction(action.id)"
                            >
                              <Users v-if="action.icon === 'Users'" class="h-4 w-4" />
                              <MessageCircle v-if="action.icon === 'MessageCircle'" class="h-4 w-4" />
                              <Phone v-if="action.icon === 'Phone'" class="h-4 w-4" />
                              {{ action.text }}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 普通用户消息 -->
                    <div v-else class="flex items-start gap-2" :class="message.senderId === 'me' ? 'flex-row-reverse' : 'flex-row'">
                      <!-- 头像 -->
                      <Avatar class="h-8 w-8 flex-shrink-0">
                        <AvatarImage :src="message.avatar" :alt="message.senderName" />
                        <AvatarFallback class="text-xs">
                          {{ message.senderName?.charAt(0) }}
                        </AvatarFallback>
                      </Avatar>

                      <!-- 消息内容 -->
                      <div class="max-w-[70%] min-w-0" :class="message.senderId === 'me' ? 'items-end' : 'items-start'">
                        <!-- 用户名和时间 -->
                        <div class="mb-1 flex items-center gap-2" :class="message.senderId === 'me' ? 'flex-row-reverse' : 'flex-row'">
                          <span class="text-sm text-foreground font-medium">{{ message.senderName }}</span>
                          <span v-if="message.time" class="text-xs text-muted-foreground">{{ message.time }}</span>
                        </div>

                        <!-- 消息气泡 -->
                        <div
                          class="border px-3 py-2 shadow-sm"
                          :class="message.senderId === 'me'
                            ? 'bg-blue-500 text-white rounded-2xl rounded-tr-lg ml-auto'
                            : 'bg-white text-gray-900 rounded-2xl rounded-tl-lg'"
                        >
                          <!-- 回复引用 -->
                          <div v-if="message.replyTo" class="mb-2 border-l-2 border-primary/30 rounded-lg bg-background/50 p-2">
                            <div class="mb-1 text-xs text-muted-foreground">
                              回复 {{ message.replyTo.senderName }}：
                            </div>
                            <div class="text-sm text-muted-foreground">
                              {{ message.replyTo.content }}
                            </div>
                          </div>

                          <!-- 主要内容 -->
                          <div class="whitespace-pre-line text-sm leading-relaxed">
                            <!-- @提及 -->
                            <span
                              v-for="mention in message.mentions"
                              :key="mention"
                              class="mr-1 font-medium"
                              :class="message.senderId === 'me' ? 'text-blue-200' : 'text-blue-600'"
                            >{{ mention }}</span>
                            <!-- 附加内容 -->
                            <span v-if="message.additionalContent">{{ message.additionalContent }}</span>
                            <!-- 普通内容 -->
                            <span v-else>{{ message.content }}</span>
                          </div>
                        </div>

                        <!-- 回复数量 -->
                        <div v-if="message.replies" class="mt-1" :class="message.senderId === 'me' ? 'text-right' : 'text-left'">
                          <Button variant="ghost" size="sm" class="h-auto p-0 text-blue-600 font-normal hover:text-blue-700">
                            <MessageCircle class="mr-1 h-3 w-3" />
                            {{ message.replies }} 条回复
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 空状态 -->
                  <div v-if="currentMessages.length === 0" class="h-32 flex flex-col items-center justify-center text-center">
                    <MessageCircle class="mb-4 h-12 w-12 text-muted-foreground/30" />
                    <p class="text-muted-foreground">
                      开始对话吧！
                    </p>
                  </div>
                </div>
              </ScrollArea>

              <!-- 消息输入框 -->
              <div class="flex-shrink-0 bg-white p-4">
                <!-- 预设消息 -->
                <div v-if="showPresetMessages" class="mb-3">
                  <div class="mb-2 text-sm text-gray-500">
                    快速发送：
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <Button
                      v-for="preset in presetMessages"
                      :key="preset.id"
                      variant="outline"
                      size="sm"
                      class="h-auto whitespace-normal px-3 py-2 text-left"
                      @click="clickPresetMessage(preset.text)"
                    >
                      <span class="mr-2">{{ preset.icon }}</span>
                      {{ preset.text }}
                    </Button>
                  </div>
                </div>

                <!-- 故障群聊快捷回复 -->
                <div v-if="showFaultGroupQuickReplies" class="mb-3">
                  <div class="mb-2 text-sm text-gray-500">
                    快捷操作：
                  </div>
                  <div class="grid grid-cols-2 gap-2">
                    <Button
                      v-for="quickReply in faultGroupQuickReplies"
                      :key="quickReply.id"
                      variant="outline"
                      size="sm"
                      class="h-auto flex flex-col items-start whitespace-normal px-3 py-2 text-left"
                      @click="clickPresetMessage(quickReply.text)"
                    >
                      <div class="mb-1 flex items-center gap-2">
                        <span>{{ quickReply.icon }}</span>
                        <span class="text-xs text-gray-500">{{ quickReply.description }}</span>
                      </div>
                      <span class="text-xs leading-tight">{{ quickReply.text }}</span>
                    </Button>
                  </div>
                </div>

                <!-- 输入区域 -->
                <div class="mb-3 flex items-end gap-3">
                  <div class="relative flex-1">
                    <Input
                      v-model="newMessage"
                      :placeholder="inputPlaceholder"
                      class="min-h-12 resize-none border-gray-200 pr-12"
                      @keydown="handleKeyPress"
                    />
                  </div>
                  <Button
                    :disabled="!newMessage.trim()"
                    class="h-12 px-4"
                    @click="sendMessage"
                  >
                    <Send class="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <!-- 未选择聊天的状态 -->
            <div v-else class="h-full flex items-center justify-center">
              <div class="text-center">
                <MessageCircle class="mx-auto mb-4 h-16 w-16 text-muted-foreground/30" />
                <h3 class="mb-2 text-lg text-muted-foreground font-medium">
                  选择一个聊天
                </h3>
                <p class="text-sm text-muted-foreground">
                  从左侧列表中选择一个对话开始聊天
                </p>
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </TooltipProvider>
    </div>
  </div>
</template>
