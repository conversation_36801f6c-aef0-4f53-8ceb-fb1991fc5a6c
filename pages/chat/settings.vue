<script setup lang="ts">
import { ArrowLeft, Bell, BellOff, Moon, Sun, Volume2, VolumeX, Shield, Trash2 } from 'lucide-vue-next'

// 页面元数据
definePageMeta({
  title: '聊天设置',
  middleware: 'auth',
})

const router = useRouter()

// 设置状态
const settings = ref({
  notifications: {
    enabled: true,
    sound: true,
    desktop: true,
    mobile: true,
  },
  appearance: {
    theme: 'system', // 'light', 'dark', 'system'
    fontSize: 'medium', // 'small', 'medium', 'large'
    bubbleStyle: 'rounded', // 'rounded', 'square'
  },
  privacy: {
    readReceipts: true,
    lastSeen: true,
    typing: true,
  },
  storage: {
    autoDownload: true,
    cacheSize: '500MB',
  }
})

function goBack() {
  router.push('/chat')
}

function toggleNotifications() {
  settings.value.notifications.enabled = !settings.value.notifications.enabled
}

function toggleSound() {
  settings.value.notifications.sound = !settings.value.notifications.sound
}

function clearCache() {
  // 模拟清除缓存
  console.log('清除缓存')
}

function exportData() {
  // 模拟导出数据
  console.log('导出聊天数据')
}

function deleteAllChats() {
  // 模拟删除所有聊天
  console.log('删除所有聊天记录')
}
</script>

<template>
  <div class="h-screen flex flex-col">
    <!-- 页面头部 -->
    <div class="flex-shrink-0 p-4 border-b bg-background">
      <div class="flex items-center gap-3">
        <Button size="icon" variant="ghost" @click="goBack">
          <ArrowLeft class="h-4 w-4" />
        </Button>
        <h1 class="text-2xl font-bold">聊天设置</h1>
      </div>
    </div>
    
    <!-- 设置内容 -->
    <ScrollArea class="flex-1">
      <div class="p-6 space-y-8">
        <!-- 通知设置 -->
        <div class="space-y-4">
          <h2 class="text-lg font-semibold flex items-center gap-2">
            <Bell class="h-5 w-5" />
            通知设置
          </h2>
          
          <div class="space-y-4 pl-7">
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium">启用通知</p>
                <p class="text-sm text-muted-foreground">接收新消息通知</p>
              </div>
              <Switch 
                :checked="settings.notifications.enabled" 
                @update:checked="toggleNotifications"
              />
            </div>
            
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium">声音提醒</p>
                <p class="text-sm text-muted-foreground">新消息时播放提示音</p>
              </div>
              <Switch 
                :checked="settings.notifications.sound" 
                @update:checked="toggleSound"
                :disabled="!settings.notifications.enabled"
              />
            </div>
            
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium">桌面通知</p>
                <p class="text-sm text-muted-foreground">在桌面显示通知</p>
              </div>
              <Switch 
                v-model:checked="settings.notifications.desktop"
                :disabled="!settings.notifications.enabled"
              />
            </div>
          </div>
        </div>
        
        <Separator />
        
        <!-- 外观设置 -->
        <div class="space-y-4">
          <h2 class="text-lg font-semibold flex items-center gap-2">
            <Sun class="h-5 w-5" />
            外观设置
          </h2>
          
          <div class="space-y-4 pl-7">
            <div class="space-y-2">
              <p class="font-medium">主题</p>
              <Select v-model="settings.appearance.theme">
                <SelectTrigger class="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">浅色</SelectItem>
                  <SelectItem value="dark">深色</SelectItem>
                  <SelectItem value="system">跟随系统</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div class="space-y-2">
              <p class="font-medium">字体大小</p>
              <Select v-model="settings.appearance.fontSize">
                <SelectTrigger class="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">小</SelectItem>
                  <SelectItem value="medium">中</SelectItem>
                  <SelectItem value="large">大</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div class="space-y-2">
              <p class="font-medium">消息气泡样式</p>
              <Select v-model="settings.appearance.bubbleStyle">
                <SelectTrigger class="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rounded">圆角</SelectItem>
                  <SelectItem value="square">方角</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        
        <Separator />
        
        <!-- 隐私设置 -->
        <div class="space-y-4">
          <h2 class="text-lg font-semibold flex items-center gap-2">
            <Shield class="h-5 w-5" />
            隐私设置
          </h2>
          
          <div class="space-y-4 pl-7">
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium">已读回执</p>
                <p class="text-sm text-muted-foreground">让对方知道你已读消息</p>
              </div>
              <Switch v-model:checked="settings.privacy.readReceipts" />
            </div>
            
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium">最后在线时间</p>
                <p class="text-sm text-muted-foreground">显示你的最后在线时间</p>
              </div>
              <Switch v-model:checked="settings.privacy.lastSeen" />
            </div>
            
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium">正在输入</p>
                <p class="text-sm text-muted-foreground">显示正在输入状态</p>
              </div>
              <Switch v-model:checked="settings.privacy.typing" />
            </div>
          </div>
        </div>
        
        <Separator />
        
        <!-- 存储设置 -->
        <div class="space-y-4">
          <h2 class="text-lg font-semibold">存储管理</h2>
          
          <div class="space-y-4 pl-7">
            <div class="flex items-center justify-between">
              <div>
                <p class="font-medium">自动下载媒体</p>
                <p class="text-sm text-muted-foreground">自动下载图片和文件</p>
              </div>
              <Switch v-model:checked="settings.storage.autoDownload" />
            </div>
            
            <div class="space-y-2">
              <p class="font-medium">缓存大小</p>
              <p class="text-sm text-muted-foreground">当前使用: {{ settings.storage.cacheSize }}</p>
              <Button variant="outline" @click="clearCache">
                清除缓存
              </Button>
            </div>
          </div>
        </div>
        
        <Separator />
        
        <!-- 数据管理 -->
        <div class="space-y-4">
          <h2 class="text-lg font-semibold flex items-center gap-2">
            <Trash2 class="h-5 w-5" />
            数据管理
          </h2>
          
          <div class="space-y-4 pl-7">
            <div class="space-y-2">
              <Button variant="outline" @click="exportData">
                导出聊天数据
              </Button>
              <p class="text-sm text-muted-foreground">导出所有聊天记录到文件</p>
            </div>
            
            <div class="space-y-2">
              <Button variant="destructive" @click="deleteAllChats">
                删除所有聊天记录
              </Button>
              <p class="text-sm text-muted-foreground">此操作不可恢复，请谨慎操作</p>
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  </div>
</template>
