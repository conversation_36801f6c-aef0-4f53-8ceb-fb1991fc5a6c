<script setup lang="ts">
import { ArrowLeft, MessageCircle, MoreVertical, Phone, Send, Video } from 'lucide-vue-next'

// 页面元数据
definePageMeta({
  title: '聊天详情',
  middleware: 'auth',
})

// 获取路由参数
const route = useRoute()
const router = useRouter()
const chatId = computed(() => Number.parseInt(route.params.id as string))

// 模拟用户数据
const users = ref({
  1: { id: 1, name: '张三', avatar: '/avatars/01.png', online: true },
  2: { id: 2, name: '李四', avatar: '/avatars/02.png', online: false },
  3: { id: 3, name: '王五', avatar: '/avatars/03.png', online: true },
  4: { id: 4, name: '赵六', avatar: '/avatars/04.png', online: false },
})

// 当前聊天用户
const currentUser = computed(() => users.value[chatId.value])

// 当前聊天的消息数据
const messages = ref<any[]>([])
const newMessage = ref('')

// 初始化消息数据
function initializeMessages() {
  const initialMessages: Record<number, any[]> = {
    1: [
      {
        id: 1,
        senderId: 1,
        senderName: '张三',
        content: '你好，最近怎么样？',
        time: '14:25',
        type: 'text',
      },
      {
        id: 2,
        senderId: 'me',
        senderName: '我',
        content: '还不错，工作比较忙',
        time: '14:26',
        type: 'text',
      },
    ],
    2: [
      {
        id: 1,
        senderId: 2,
        senderName: '李四',
        content: '你好，明天的会议准备好了吗？',
        time: '13:45',
        type: 'text',
      },
    ],
    3: [
      {
        id: 1,
        senderId: 3,
        senderName: '王五',
        content: '项目进度如何？',
        time: '12:20',
        type: 'text',
      },
    ],
    4: [],
  }

  messages.value = initialMessages[chatId.value] || []
}

// 发送消息
function sendMessage() {
  if (!newMessage.value.trim() || !currentUser.value)
    return

  const message = {
    id: messages.value.length + 1,
    senderId: 'me',
    senderName: '我',
    content: newMessage.value,
    time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    type: 'text',
  }

  messages.value.push(message)
  newMessage.value = ''

  nextTick(() => {
    scrollToBottom()
  })

  // 模拟自动回复
  simulateAutoReply()
}

// 模拟自动回复功能
function simulateAutoReply() {
  if (!currentUser.value)
    return

  const responses = [
    '好的，我明白了',
    '没问题，我会处理的',
    '收到，谢谢！',
    '我稍后回复你',
    '这个想法不错',
    '让我想想...',
    '可以的，我们继续讨论',
    '我同意你的观点',
  ]

  const randomResponse = responses[Math.floor(Math.random() * responses.length)]

  setTimeout(() => {
    const replyMessage = {
      id: messages.value.length + 1,
      senderId: currentUser.value.id,
      senderName: currentUser.value.name,
      content: randomResponse,
      time: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
      type: 'text',
    }

    messages.value.push(replyMessage)

    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  }, 1000 + Math.random() * 2000) // 1-3秒后回复
}

function scrollToBottom() {
  const messagesContainer = document.querySelector('.messages-container')
  if (messagesContainer) {
    messagesContainer.scrollTop = messagesContainer.scrollHeight
  }
}

function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

function goBack() {
  router.push('/chat')
}

// 页面挂载时初始化
onMounted(() => {
  initializeMessages()
})

// 监听路由变化
watch(() => route.params.id, () => {
  initializeMessages()
})
</script>

<template>
  <div class="h-screen flex flex-col -m-6">
    <!-- 如果用户不存在，显示错误页面 -->
    <div v-if="!currentUser" class="h-full flex items-center justify-center">
      <div class="text-center">
        <h2 class="mb-4 text-2xl text-muted-foreground font-bold">
          用户不存在
        </h2>
        <Button @click="goBack">
          返回聊天列表
        </Button>
      </div>
    </div>

    <!-- 正常聊天界面 -->
    <template v-else>
      <!-- 聊天头部 -->
      <div class="flex-shrink-0 border-b bg-background/95 p-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <!-- 返回按钮 -->
            <Button size="icon" variant="ghost" @click="goBack">
              <ArrowLeft class="h-4 w-4" />
            </Button>

            <Avatar class="h-8 w-8">
              <AvatarImage :src="currentUser.avatar" :alt="currentUser.name" />
              <AvatarFallback>{{ currentUser.name.charAt(0) }}</AvatarFallback>
            </Avatar>
            <div>
              <h3 class="font-medium">
                {{ currentUser.name }}
              </h3>
              <p class="flex items-center gap-1 text-xs text-muted-foreground" /><div
                v-if="currentUser.online"
                class="h-2 w-2 rounded-full bg-green-500"
              />
              {{ currentUser.online ? '在线' : '离线' }}
            </div>
          </div>
          <div class="flex items-center gap-2">
            <Button size="icon" variant="ghost">
              <Phone class="h-4 w-4" />
            </Button>
            <Button size="icon" variant="ghost">
              <Video class="h-4 w-4" />
            </Button>
            <Button size="icon" variant="ghost">
              <MoreVertical class="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <ScrollArea class="messages-container flex-1">
        <div class="p-4 space-y-4">
          <div
            v-for="message in messages"
            :key="message.id"
            class="flex items-end gap-2"
            :class="message.senderId === 'me' ? 'justify-end' : 'justify-start'"
          >
            <Avatar
              v-if="message.senderId !== 'me'"
              class="order-first h-6 w-6"
            >
              <AvatarImage :src="currentUser.avatar" :alt="currentUser.name" />
              <AvatarFallback class="text-xs">
                {{ currentUser.name.charAt(0) }}
              </AvatarFallback>
            </Avatar>

            <div
              class="max-w-[70%] rounded-2xl px-4 py-2 shadow-sm"
              :class="message.senderId === 'me'
                ? 'bg-primary text-primary-foreground rounded-br-md'
                : 'bg-muted rounded-bl-md'"
            >
              <p class="text-sm leading-relaxed">
                {{ message.content }}
              </p>
              <p
                class="mt-1 text-xs opacity-70"
                :class="message.senderId === 'me' ? 'text-right' : 'text-left'"
              >
                {{ message.time }}
              </p>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="messages.length === 0" class="h-32 flex flex-col items-center justify-center text-center">
            <MessageCircle class="mb-4 h-12 w-12 text-muted-foreground/30" />
            <p class="text-muted-foreground">
              开始对话吧！
            </p>
          </div>
        </div>
      </ScrollArea>

      <!-- 消息输入框 -->
      <div class="flex-shrink-0 border-t bg-background p-4">
        <div class="flex items-end gap-2">
          <Textarea
            v-model="newMessage"
            placeholder="输入消息..."
            class="max-h-[120px] min-h-[40px] resize-none"
            @keydown="handleKeyPress"
          />
          <Button :disabled="!newMessage.trim()" @click="sendMessage">
            <Send class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </template>
  </div>
</template>
