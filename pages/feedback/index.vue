<script lang="ts"></script>

<script setup lang="ts">
import { useRoute } from 'vue-router'

import Layout from '~/components/feedback/Layout.vue'
import { useTicketData } from '~/composables/useTicketData'

// 接收路由传递的props
defineProps({
  activeMenu: {
    type: String,
    default: 'all',
  },
  id: {
    type: String,
    default: '',
  },
})

const route = useRoute()
const currentActiveMenu = ref('all')

// 从 URL 参数中获取工单 ID（如果有的话）
const selectedTicketId = computed(() => {
  // 优先从路由参数中获取工单ID（用于工单详情页面）
  if (route.params.id && typeof route.params.id === 'string') {
    return route.params.id
  }
  // 其次从查询参数中获取
  return route.query.ticketId as string || ''
})

// 使用工单数据管理
const { tickets, loading, error, fetchTickets, refreshTickets }
  = useTicketData()

// 创建事件处理函数
const handleMenuClick = ((event: CustomEvent) => {
  const { url } = event.detail
  updateMenuFromPath(url)
  // 菜单点击时重新请求工单数据
  refreshTickets()
}) as EventListener

onMounted(async () => {
  updateMenuFromPath(route.path)

  // 监听自定义菜单点击事件
  window.addEventListener('menu-click', handleMenuClick)

  // 获取工单数据
  await fetchTickets()
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('menu-click', handleMenuClick)
})

// 监听路由变化，更新当前活动菜单并刷新数据
watch(
  () => route.path,
  (newPath, oldPath) => {
    updateMenuFromPath(newPath)
    // 只有当路由路径真正发生变化时才刷新数据（避免同页面内的参数变化导致重复请求）
    if (newPath !== oldPath) {
      refreshTickets()
    }
  },
)

// 根据路径更新菜单
function updateMenuFromPath(path: string) {
  if (path.includes('/feedback/ticket-detail')) {
    // 对于工单详情页，保持当前的菜单状态，不改变
    // currentActiveMenu.value 保持不变
  }
  else {
    // 对于所有工单页面，显示为 'all'
    currentActiveMenu.value = 'all'
  }
}

// 处理创建工单成功
function handleTicketCreated() {
  // 刷新工单列表
  refreshTickets()
}

// 处理刷新工单列表
function handleRefreshTickets() {
  refreshTickets()
}

// 处理工单操作后的刷新
function handleTicketActionRefresh() {
  refreshTickets()
}

// 处理当前工单的刷新
function handleRefreshCurrentTicket() {
  // 这里可以实现更精细的当前工单刷新逻辑
  // 目前先刷新整个列表
  refreshTickets()
}
</script>

<template>
  <div class="-m-4 lg:-m-6">
    <!-- 工单列表 -->
    <Layout
      :tickets="tickets"
      :loading="loading"
      :error="error"
      :nav-collapsed-size="4"
      :active-menu="currentActiveMenu"
      :selected-ticket-id="selectedTicketId || id"
      @update:active-menu="currentActiveMenu = $event"
      @ticket-created="handleTicketCreated"
      @refresh-tickets="handleRefreshTickets"
      @ticket-action-refresh="handleTicketActionRefresh"
      @refresh-current-ticket="handleRefreshCurrentTicket"
    />
  </div>
</template>
