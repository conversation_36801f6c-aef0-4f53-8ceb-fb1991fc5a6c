<script setup lang="ts">
import { Skeleton } from '@/components/ui/skeleton'
import { TooltipProvider } from '@/components/ui/tooltip'
import { useRoute } from 'vue-router'
import Layout from '~/components/feedback/Layout.vue'
import TicketDetail from '~/components/feedback/TicketDetail.vue'
import { useTicketData } from '~/composables/useTicketData'

// 获取路由参数
const route = useRoute()
const ticketId = route.params.id as string

// 检测是否为独立模式
const isStandalone = computed(() => route.query.standalone === 'true')

// 页面元数据 - 根据是否独立模式设置布局
definePageMeta({
  title: '工单详情',
  layout: false, // 总是使用自定义布局逻辑
})

// 使用工单数据管理
const { tickets, loading, error, fetchTickets, refreshTickets } = useTicketData()

// 当前工单
const currentTicket = computed(() => {
  return tickets.value.find(ticket =>
    (ticket.ticketID || ticket.id)?.toString() === ticketId,
  )
})

// 计算是否显示未找到状态
const showNotFound = computed(() => {
  // 只有在不加载且没有错误且确实没有找到工单时才显示未找到
  return !loading.value && !error.value && tickets.value.length > 0 && !currentTicket.value
})

// 计算是否显示加载状态
const showLoading = computed(() => {
  return loading.value
})

// 监听工单列表变化，确保当前工单被正确选中
watch(
  tickets,
  (newTickets) => {
    if (newTickets && newTickets.length > 0) {
      // 检查当前工单是否在列表中
      const foundTicket = newTickets.find(ticket =>
        (ticket.ticketID || ticket.id)?.toString() === ticketId,
      )

      if (foundTicket) {
        // 工单存在，确保URL正确
        const correctPath = `/feedback/ticket-detail/${foundTicket.ticketID || foundTicket.id}`
        if (window.location.pathname !== correctPath) {
          window.history.replaceState({}, '', correctPath)
          window.dispatchEvent(new CustomEvent('urlchange'))
        }
      }
    }
  },
  { immediate: true },
)

// 当前活动菜单状态
const currentActiveMenu = ref('all')

// 创建事件处理函数
const handleMenuClick = ((event: CustomEvent) => {
  const { url } = event.detail
  updateMenuFromPath(url)
  // 菜单点击时重新请求工单数据
  refreshTickets()
}) as EventListener

onMounted(async () => {
  updateMenuFromPath(route.path)

  // 监听自定义菜单点击事件
  window.addEventListener('menu-click', handleMenuClick)

  // 获取工单数据
  await fetchTickets()
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('menu-click', handleMenuClick)
})

// 监听路由变化，更新当前活动菜单并刷新数据
watch(
  () => route.path,
  (newPath, oldPath) => {
    updateMenuFromPath(newPath)
    // 只有当路由路径真正发生变化时才刷新数据（避免同页面内的参数变化导致重复请求）
    if (newPath !== oldPath) {
      refreshTickets()
    }
  },
)

// 根据路径更新菜单
function updateMenuFromPath(path: string) {
  if (path.includes('/feedback/created')) {
    currentActiveMenu.value = 'created'
  }
  else if (path.includes('/feedback/todo')) {
    currentActiveMenu.value = 'todo'
  }
  else if (path.includes('/feedback/ticket-detail')) {
    // 对于工单详情页，保持当前的菜单状态，不改变
    // currentActiveMenu.value 保持不变
  }
  else {
    // 对于所有工单页面，显示为 'all'
    currentActiveMenu.value = 'all'
  }
}

// 处理创建工单成功
function handleTicketCreated() {
  // 刷新工单列表
  refreshTickets()
}

// 处理刷新工单列表
function handleRefreshTickets() {
  refreshTickets()
}

// 处理刷新当前工单
function handleRefreshCurrentTicket() {
  // 重新获取当前工单数据
  refreshTickets()
}

// 返回主页（仅在独立模式下使用）
function goToHomePage() {
  window.location.href = '/feedback'
}
</script>

<template>
  <!-- 独立模式 -->
  <div v-if="isStandalone" class="min-h-screen bg-background">
    <!-- 工单详情内容 -->
    <div class="p-4">
      <!-- 加载状态 -->
      <div v-if="showLoading" class="mx-auto max-w-6xl">
        <div class="border rounded-lg bg-card shadow-sm">
          <!-- 工具栏骨架 -->
          <div class="p-4">
            <div class="flex items-center justify-between">
              <div class="flex gap-2">
                <Skeleton class="h-8 w-8" />
                <Skeleton class="h-8 w-8" />
                <Skeleton class="h-8 w-8" />
                <Skeleton class="h-8 w-8" />
              </div>
              <div class="flex gap-2">
                <Skeleton class="h-8 w-8" />
                <Skeleton class="h-8 w-8" />
              </div>
            </div>
          </div>

          <!-- 内容骨架 -->
          <div class="p-6 space-y-6">
            <!-- 工单头部骨架 -->
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <Skeleton class="h-8 w-48" />
                <Skeleton class="h-6 w-20" />
              </div>
              <Skeleton class="h-4 w-32" />
            </div>

            <!-- 工单内容骨架 -->
            <div class="space-y-4">
              <Skeleton class="h-4 w-full" />
              <Skeleton class="h-4 w-3/4" />
              <Skeleton class="h-4 w-1/2" />
            </div>

            <!-- 操作按钮骨架 -->
            <div class="flex gap-2">
              <Skeleton class="h-9 w-20" />
              <Skeleton class="h-9 w-20" />
              <Skeleton class="h-9 w-20" />
            </div>
          </div>
        </div>
      </div>

      <!-- 工单详情 -->
      <div v-else-if="currentTicket" class="mx-auto max-w-6xl">
        <div class="border rounded-lg bg-card shadow-sm">
          <TooltipProvider>
            <TicketDetail
              :ticket="currentTicket"
              @refresh="fetchTickets"
              @refresh-ticket="fetchTickets"
            />
          </TooltipProvider>
        </div>
      </div>

      <!-- 工单未找到 -->
      <div v-else-if="showNotFound" class="mx-auto max-w-2xl">
        <div class="flex flex-col items-center justify-center px-6 py-16">
          <div class="text-center space-y-6">
            <!-- 图标 -->
            <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-muted">
              <Icon name="i-lucide-search-x" class="size-12 text-muted-foreground" />
            </div>

            <!-- 标题和描述 -->
            <div class="space-y-2">
              <h2 class="text-2xl font-semibold tracking-tight">
                工单未找到
              </h2>
              <p class="max-w-md text-muted-foreground">
                工单 <span class="font-medium font-mono">#{{ ticketId }}</span> 不存在或已被删除，请检查工单编号是否正确。
              </p>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col justify-center gap-3 sm:flex-row">
              <Button
                variant="default"
                class="min-w-32"
                @click="goToHomePage"
              >
                <Icon name="i-lucide-home" class="mr-2 size-4" />
                返回主页
              </Button>
              <Button
                variant="outline"
                class="min-w-32"
                @click="refreshTickets"
              >
                <Icon name="i-lucide-refresh-cw" class="mr-2 size-4" />
                重新加载
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="mx-auto max-w-2xl">
        <div class="flex flex-col items-center justify-center px-6 py-16">
          <div class="text-center space-y-6">
            <!-- 图标 -->
            <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-destructive/10">
              <Icon name="i-lucide-alert-circle" class="size-12 text-destructive" />
            </div>

            <!-- 标题和描述 -->
            <div class="space-y-2">
              <h2 class="text-2xl font-semibold tracking-tight">
                加载失败
              </h2>
              <p class="max-w-md text-muted-foreground">
                {{ error }}
              </p>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col justify-center gap-3 sm:flex-row">
              <Button
                variant="default"
                class="min-w-32"
                @click="refreshTickets"
              >
                <Icon name="i-lucide-refresh-cw" class="mr-2 size-4" />
                重试
              </Button>
              <Button
                variant="outline"
                class="min-w-32"
                @click="goToHomePage"
              >
                <Icon name="i-lucide-home" class="mr-2 size-4" />
                返回主页
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 正常模式 -->
  <NuxtLayout v-else name="default">
    <div class="-m-4 lg:-m-6">
      <!-- 工单列表 -->
      <Layout
        :tickets="tickets"
        :loading="loading"
        :error="error"
        :nav-collapsed-size="4"
        :active-menu="currentActiveMenu"
        :selected-ticket-id="ticketId"
        @update:active-menu="currentActiveMenu = $event"
        @ticket-created="handleTicketCreated"
        @refresh-tickets="handleRefreshTickets"
        @ticket-action-refresh="handleRefreshTickets"
        @refresh-current-ticket="handleRefreshCurrentTicket"
      />
    </div>
  </NuxtLayout>
</template>
