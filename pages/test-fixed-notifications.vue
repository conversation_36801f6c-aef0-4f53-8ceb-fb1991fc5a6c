<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  AlertTriangle,
  CheckCircle,
  Info,
  Key,
  Loader2,
  Shield,
  ShieldX,
  XCircle,
} from 'lucide-vue-next'
import { useModal } from '~/composables/useModal'
import { useNotification } from '~/composables/useNotification'
import { handleApiCall } from '~/utils/errorUtils'
import { requestClient } from '~/utils/request'

const notification = useNotification()
const modal = useModal()

const loading = ref(false)

// 通知测试
function showSuccess() {
  notification.success('操作成功！这是使用统一通知系统的成功消息。', '成功')
}

function showError() {
  notification.error('操作失败，请检查输入数据并重试。', '错误')
}

function showWarning() {
  notification.warning('请注意，您的操作可能会影响其他用户。', '警告')
}

function showInfo() {
  notification.info('这是一条信息提示，帮助您了解当前状态。', '提示')
}

// 认证过期测试
async function showAuthExpired() {
  await modal.showAuthExpiredModal()
}

async function testTokenInvalid() {
  loading.value = true

  const { data, error } = await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: 2004,
      })
    },
    {
      showError: false, // 认证错误不显示普通通知
    },
  )

  if (error && error.isAuthError) {
    await modal.showAuthExpiredModal()
  }

  loading.value = false
}

// API测试
async function testApiSuccess() {
  loading.value = true

  await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: 20000,
        message: '使用统一通知系统的成功消息',
      })
    },
    {
      showSuccess: true,
      successMessage: 'API调用成功！',
    },
  )

  loading.value = false
}

async function testApiError() {
  loading.value = true

  await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: 1002,
      })
    },
    {
      showError: true,
    },
  )

  loading.value = false
}

async function testPermissionError() {
  loading.value = true

  await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: 2005,
      })
    },
    {
      showError: true,
    },
  )

  loading.value = false
}

definePageMeta({
  title: '修复后的通知系统测试',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">
          🎉 修复后的通知系统测试
        </h1>
        <p class="mt-2 text-muted-foreground">
          测试使用统一 useNotification 的错误处理和样式优化
        </p>
      </div>

      <!-- 修复说明 -->
      <Card>
        <CardHeader>
          <CardTitle class="text-green-600">
            ✅ 修复内容
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex items-center gap-2">
              <CheckCircle class="h-4 w-4 text-green-500" />
              <span>使用项目统一的 useNotification 系统</span>
            </div>
            <div class="flex items-center gap-2">
              <CheckCircle class="h-4 w-4 text-green-500" />
              <span>优化了登录弹窗的样式，使用 shadcn-ui 设计系统</span>
            </div>
            <div class="flex items-center gap-2">
              <CheckCircle class="h-4 w-4 text-green-500" />
              <span>统一了通知和弹窗的样式风格</span>
            </div>
            <div class="flex items-center gap-2">
              <CheckCircle class="h-4 w-4 text-green-500" />
              <span>简化了维护，所有通知使用同一套系统</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 通知测试 -->
      <Card>
        <CardHeader>
          <CardTitle>📢 通知系统测试</CardTitle>
          <CardDescription>
            测试不同类型的通知效果
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <Button variant="outline" class="h-12" @click="showSuccess">
              <CheckCircle class="mr-2 h-4 w-4 text-green-500" />
              成功通知
            </Button>
            <Button variant="outline" class="h-12" @click="showError">
              <XCircle class="mr-2 h-4 w-4 text-red-500" />
              错误通知
            </Button>
            <Button variant="outline" class="h-12" @click="showWarning">
              <AlertTriangle class="mr-2 h-4 w-4 text-yellow-500" />
              警告通知
            </Button>
            <Button variant="outline" class="h-12" @click="showInfo">
              <Info class="mr-2 h-4 w-4 text-blue-500" />
              信息通知
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 认证过期测试 -->
      <Card>
        <CardHeader>
          <CardTitle>🔐 认证过期处理</CardTitle>
          <CardDescription>
            测试优化后的认证过期对话框
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
            <Button variant="outline" class="h-12" @click="showAuthExpired">
              <Shield class="mr-2 h-4 w-4 text-orange-500" />
              显示认证过期对话框
            </Button>
            <Button :disabled="loading" variant="outline" class="h-12" @click="testTokenInvalid">
              <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
              <Key v-else class="mr-2 h-4 w-4 text-red-500" />
              模拟Token无效
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- API错误测试 -->
      <Card>
        <CardHeader>
          <CardTitle>🔗 API错误处理</CardTitle>
          <CardDescription>
            测试实际API调用的错误处理
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
            <Button :disabled="loading" variant="outline" class="h-12" @click="testApiSuccess">
              <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
              <CheckCircle v-else class="mr-2 h-4 w-4 text-green-500" />
              API成功
            </Button>
            <Button :disabled="loading" variant="outline" class="h-12" @click="testApiError">
              <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
              <XCircle v-else class="mr-2 h-4 w-4 text-red-500" />
              API错误
            </Button>
            <Button :disabled="loading" variant="outline" class="h-12" @click="testPermissionError">
              <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
              <ShieldX v-else class="mr-2 h-4 w-4 text-orange-500" />
              权限错误
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 样式对比 -->
      <Card>
        <CardHeader>
          <CardTitle>🎨 样式优化对比</CardTitle>
          <CardDescription>
            展示优化后的统一样式风格
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="border rounded-lg bg-muted/50 p-4">
              <h4 class="mb-2 font-medium">
                ✅ 优化后的特点:
              </h4>
              <ul class="text-sm text-muted-foreground space-y-1">
                <li>• 使用项目统一的 useNotification 系统</li>
                <li>• 遵循 shadcn-ui 设计规范</li>
                <li>• 支持深色/浅色主题切换</li>
                <li>• 统一的动画和交互效果</li>
                <li>• 响应式设计，适配移动端</li>
                <li>• 更好的可访问性支持</li>
              </ul>
            </div>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div class="border rounded-lg p-4">
                <h5 class="mb-2 text-sm font-medium">
                  通知样式
                </h5>
                <p class="text-xs text-muted-foreground">
                  使用统一的颜色系统、字体大小和间距，确保与整个应用的视觉一致性。
                </p>
              </div>
              <div class="border rounded-lg p-4">
                <h5 class="mb-2 text-sm font-medium">
                  对话框样式
                </h5>
                <p class="text-xs text-muted-foreground">
                  采用 shadcn-ui 的 Dialog 组件，支持键盘导航和焦点管理。
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
