<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2 } from 'lucide-vue-next'
import { useModal } from '~/composables/useModal'
import { useNotification } from '~/composables/useNotification'

import { handleApiCall } from '~/utils/errorUtils'
import { requestClient } from '~/utils/request'

const notification = useNotification()
const modal = useModal()

const loading = ref(false)

// 显示不同类型的通知
function showSuccess() {
  notification.success('操作成功！这是一个成功通知示例。')
}

function showError() {
  notification.error('插入失败，请检查数据格式', '操作失败')
}

function showWarning() {
  notification.warning('查询失败，未找到相关数据', '注意')
}

async function showTokenExpired() {
  await modal.showAuthExpiredModal()
}

// API测试
async function testApiSuccess() {
  loading.value = true

  await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: 20000,
        message: '测试成功消息',
      })
    },
    {
      showSuccess: true,
      successMessage: 'API调用成功！',
    },
  )

  loading.value = false
}

async function testApiError() {
  loading.value = true

  await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: 1002,
      })
    },
    {
      showError: true,
    },
  )

  loading.value = false
}

async function testTokenInvalid() {
  loading.value = true

  const { data: _data, error } = await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: 2004,
      })
    },
    {
      showError: false, // 认证错误不显示普通错误通知
    },
  )

  // 如果是认证错误，显示认证过期对话框
  if (error && error.isAuthError) {
    await modal.showAuthExpiredModal()
  }

  loading.value = false
}

// 测试特定错误码
async function testErrorCode(code: number) {
  loading.value = true

  const { data: _data, error } = await handleApiCall(
    async () => {
      return await requestClient.post('/api/test-error', {
        errorCode: code,
      })
    },
    {
      showError: ![2001, 2002, 2003, 2004].includes(code), // 认证错误不显示普通通知
      showSuccess: code === 20000,
    },
  )

  // 如果是认证错误，显示认证过期对话框
  if (error && error.isAuthError) {
    await modal.showAuthExpiredModal()
  }

  loading.value = false
}

definePageMeta({
  title: '错误处理修复演示',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">
          ✅ 错误处理修复演示
        </h1>
        <p class="mt-2 text-muted-foreground">
          演示修复后的错误提醒和token无效处理功能
        </p>
      </div>

      <!-- 修复说明 -->
      <Card>
        <CardHeader>
          <CardTitle class="text-green-600">
            🎉 修复内容
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>✅ 错误提醒现在正常显示（使用shadcn-ui toast组件）</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>✅ Token无效时会显示认证过期对话框并引导重新登录</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>✅ 不同类型错误有不同的显示样式（成功/错误/警告/信息）</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500" />
              <span>✅ 认证错误自动处理，其他错误显示用户友好提示</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 快速测试 -->
      <Card>
        <CardHeader>
          <CardTitle>🚀 快速测试</CardTitle>
          <CardDescription>
            点击下面的按钮测试不同的错误处理效果
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- 成功通知 -->
            <div class="space-y-2">
              <h4 class="text-green-600 font-medium">
                成功通知
              </h4>
              <Button class="w-full" variant="outline" @click="showSuccess">
                显示成功通知
              </Button>
            </div>

            <!-- 错误通知 -->
            <div class="space-y-2">
              <h4 class="text-red-600 font-medium">
                错误通知
              </h4>
              <Button class="w-full" variant="outline" @click="showError">
                显示错误通知
              </Button>
            </div>

            <!-- 警告通知 -->
            <div class="space-y-2">
              <h4 class="text-yellow-600 font-medium">
                警告通知
              </h4>
              <Button class="w-full" variant="outline" @click="showWarning">
                显示警告通知
              </Button>
            </div>

            <!-- Token过期 -->
            <div class="space-y-2">
              <h4 class="text-purple-600 font-medium">
                Token过期处理
              </h4>
              <Button class="w-full" variant="outline" @click="showTokenExpired">
                模拟Token过期
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 实际API测试 -->
      <Card>
        <CardHeader>
          <CardTitle>🔗 实际API测试</CardTitle>
          <CardDescription>
            使用真实API端点测试错误处理
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
            <Button :disabled="loading" variant="outline" @click="testApiSuccess">
              <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
              API成功调用
            </Button>
            <Button :disabled="loading" variant="outline" @click="testApiError">
              <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
              API错误调用
            </Button>
            <Button :disabled="loading" variant="outline" @click="testTokenInvalid">
              <Loader2 v-if="loading" class="mr-2 h-4 w-4 animate-spin" />
              Token无效测试
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 错误码测试 -->
      <Card>
        <CardHeader>
          <CardTitle>📋 错误码测试</CardTitle>
          <CardDescription>
            测试不同错误码的处理效果
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-2 md:grid-cols-3">
            <Button variant="outline" size="sm" @click="() => testErrorCode(1002)">
              参数错误 (1002)
            </Button>
            <Button variant="outline" size="sm" @click="() => testErrorCode(2004)">
              Token无效 (2004)
            </Button>
            <Button variant="outline" size="sm" @click="() => testErrorCode(2005)">
              无权限 (2005)
            </Button>
            <Button variant="outline" size="sm" @click="() => testErrorCode(-1)">
              系统错误 (-1)
            </Button>
            <Button variant="outline" size="sm" @click="() => testErrorCode(1006)">
              DB连接失败 (1006)
            </Button>
            <Button variant="outline" size="sm" @click="() => testErrorCode(20000)">
              请求成功 (20000)
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- 技术说明 -->
      <Card>
        <CardHeader>
          <CardTitle>🔧 技术实现</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-sm space-y-3">
            <div>
              <strong>错误通知系统:</strong> 使用项目中的shadcn-ui toast组件，支持不同类型的通知样式
            </div>
            <div>
              <strong>认证过期处理:</strong> 自动检测认证相关错误，显示用户友好的对话框并引导重新登录
            </div>
            <div>
              <strong>错误码映射:</strong> 统一的错误码映射配置，便于维护和扩展
            </div>
            <div>
              <strong>自动分类:</strong> 根据错误类型和分类自动选择合适的处理方式
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
