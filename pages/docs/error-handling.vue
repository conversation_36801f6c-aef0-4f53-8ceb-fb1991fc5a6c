<script setup lang="ts">
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

definePageMeta({
  title: '错误处理系统文档',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-8">
      <div>
        <h1 class="text-4xl font-bold">
          错误处理系统文档
        </h1>
        <p class="mt-2 text-lg text-muted-foreground">
          统一的错误码映射和处理机制
        </p>
      </div>

      <!-- 概述 -->
      <Card>
        <CardHeader>
          <CardTitle>系统概述</CardTitle>
        </CardHeader>
        <CardContent class="max-w-none prose">
          <p>
            新的错误处理系统提供了统一的错误码映射和处理机制，包含以下特性：
          </p>
          <ul>
            <li>统一的错误码映射配置</li>
            <li>自动错误类型识别和分类</li>
            <li>基于 shadcn-ui 的错误通知</li>
            <li>认证错误的自动处理</li>
            <li>便捷的工具函数和 Composables</li>
          </ul>
        </CardContent>
      </Card>

      <!-- 错误码映射 -->
      <Card>
        <CardHeader>
          <CardTitle>错误码映射</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div>
              <h4 class="text-green-600 font-semibold">
                成功状态 (0, 20000)
              </h4>
              <p class="text-sm text-muted-foreground">
                表示请求成功
              </p>
            </div>

            <div>
              <h4 class="text-blue-600 font-semibold">
                数据相关错误 (1000-1999)
              </h4>
              <div class="grid grid-cols-2 mt-2 gap-2 text-sm">
                <div>1001: 查询失败，未查询到相关数据</div>
                <div>1002: 查询失败，参数错误</div>
                <div>1003: 插入失败</div>
                <div>1004: 更新失败</div>
                <div>1005: 删除失败</div>
                <div>1006: 数据库连接失败</div>
                <div>1007: 分页参数错误</div>
                <div>1008: 定时任务创建失败</div>
                <div>1009: 参数错误</div>
              </div>
            </div>

            <div>
              <h4 class="text-yellow-600 font-semibold">
                认证相关错误 (2000-2999)
              </h4>
              <div class="grid grid-cols-2 mt-2 gap-2 text-sm">
                <div>2001: 用户名或密码错误</div>
                <div>2002: 用户未登录</div>
                <div>2003: Token为空</div>
                <div>2004: Token无效</div>
                <div>2005: 无权限编辑</div>
                <div>2006: 无权限删除</div>
                <div>2007: 无权限创建</div>
                <div>2008: 权限操作失败</div>
              </div>
            </div>

            <div>
              <h4 class="text-red-600 font-semibold">
                系统错误 (负数)
              </h4>
              <div class="grid grid-cols-2 mt-2 gap-2 text-sm">
                <div>-1: 未知错误，请联系管理员</div>
                <div>-2: 登录功能异常，请联系管理员</div>
                <div>-3: 数据异常，请联系管理员</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 使用方法 -->
      <Card>
        <CardHeader>
          <CardTitle>使用方法</CardTitle>
        </CardHeader>
        <CardContent class="space-y-6">
          <!-- 基础用法 -->
          <div>
            <h4 class="mb-2 font-semibold">
              1. 基础用法 - handleApiCall
            </h4>
            <pre class="overflow-auto rounded-md bg-muted p-4 text-sm"><code>import { handleApiCall } from '~/utils/errorUtils'

// 基础用法
const { data, error } = await handleApiCall(
  () => $fetch('/api/users'),
  {
    showError: true,        // 是否显示错误通知
    showSuccess: true,      // 是否显示成功通知
    successMessage: '获取用户列表成功'
  }
)

if (error) {
  console.log('错误类型:', error.type)
  console.log('错误分类:', error.category)
  console.log('是否认证错误:', error.isAuthError)
}</code></pre>
          </div>

          <!-- Composable 用法 -->
          <div>
            <h4 class="mb-2 font-semibold">
              2. Composable 用法 - useApiErrorHandler
            </h4>
            <pre class="overflow-auto rounded-md bg-muted p-4 text-sm"><code>import { useApiErrorHandler } from '~/utils/errorUtils'

const apiHandler = useApiErrorHandler()

// 执行 API 调用
const data = await apiHandler.execute(
  () => $fetch('/api/users'),
  { showSuccess: true }
)

// 响应式状态
console.log('加载中:', apiHandler.loading.value)
console.log('错误信息:', apiHandler.error.value)</code></pre>
          </div>

          <!-- 批量处理 -->
          <div>
            <h4 class="mb-2 font-semibold">
              3. 批量处理 - handleBatchApiCalls
            </h4>
            <pre class="overflow-auto rounded-md bg-muted p-4 text-sm"><code>import { handleBatchApiCalls } from '~/utils/errorUtils'

const apiCalls = [
  () => $fetch('/api/users'),
  () => $fetch('/api/posts'),
  () => $fetch('/api/comments')
]

const result = await handleBatchApiCalls(apiCalls, {
  showErrors: true,
  continueOnError: true
})

console.log('成功数量:', result.successCount)
console.log('失败数量:', result.errorCount)</code></pre>
          </div>

          <!-- 错误检查工具 -->
          <div>
            <h4 class="mb-2 font-semibold">
              4. 错误检查工具
            </h4>
            <pre class="overflow-auto rounded-md bg-muted p-4 text-sm"><code>import {
  shouldReAuthenticate,
  getErrorMessage,
  isErrorType,
  isErrorCategory
} from '~/utils/errorUtils'
import { useNotification } from '~/composables/useNotification'

const notification = useNotification()

try {
  await $fetch('/api/protected')
} catch (error) {
  // 检查是否需要重新认证
  if (shouldReAuthenticate(error)) {
    // 跳转到登录页
  }

  // 获取用户友好的错误消息
  const message = getErrorMessage(error)

  // 检查错误类型并显示相应通知
  if (isErrorType(error, 'warning')) {
    notification.warning(message, '警告')
  } else if (isErrorType(error, 'error')) {
    notification.error(message, '错误')
  }

  // 检查错误分类
  if (isErrorCategory(error, 'auth')) {
    // 处理认证相关错误
  }
}</code></pre>
          </div>
        </CardContent>
      </Card>

      <!-- 配置说明 -->
      <Card>
        <CardHeader>
          <CardTitle>配置说明</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div>
            <h4 class="font-semibold">
              错误码配置文件
            </h4>
            <p class="mb-2 text-sm text-muted-foreground">
              位置: <code>utils/errorCodes.ts</code>
            </p>
            <p class="text-sm">
              包含所有错误码的映射关系，可以根据实际需要进行修改和扩展。
            </p>
          </div>

          <div>
            <h4 class="font-semibold">
              错误处理器
            </h4>
            <p class="mb-2 text-sm text-muted-foreground">
              位置: <code>utils/errorHandler.ts</code>
            </p>
            <p class="text-sm">
              核心错误处理逻辑，提供错误解析、分类和格式化功能。
            </p>
          </div>

          <div>
            <h4 class="font-semibold">
              工具函数
            </h4>
            <p class="mb-2 text-sm text-muted-foreground">
              位置: <code>utils/errorUtils.ts</code>
            </p>
            <p class="text-sm">
              便捷的工具函数和 Composables，简化日常开发中的错误处理。
            </p>
          </div>

          <div>
            <h4 class="font-semibold">
              通知组件
            </h4>
            <p class="mb-2 text-sm text-muted-foreground">
              位置: <code>composables/useNotification.ts</code>
            </p>
            <p class="text-sm">
              项目统一的通知系统，提供一致的用户提示界面。
            </p>
          </div>
        </CardContent>
      </Card>

      <!-- 最佳实践 -->
      <Card>
        <CardHeader>
          <CardTitle>最佳实践</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div>
              <h4 class="font-semibold">
                1. 统一使用错误处理工具
              </h4>
              <p class="text-sm text-muted-foreground">
                建议在所有 API 调用中使用 <code>handleApiCall</code> 或 <code>useApiErrorHandler</code>，确保错误处理的一致性。
              </p>
            </div>

            <div>
              <h4 class="font-semibold">
                2. 合理配置错误显示
              </h4>
              <p class="text-sm text-muted-foreground">
                根据业务场景决定是否显示错误通知，避免过多的用户提示。
              </p>
            </div>

            <div>
              <h4 class="font-semibold">
                3. 及时更新错误码映射
              </h4>
              <p class="text-sm text-muted-foreground">
                当后端添加新的错误码时，及时更新 <code>errorCodes.ts</code> 文件中的映射关系。
              </p>
            </div>

            <div>
              <h4 class="font-semibold">
                4. 处理特殊错误场景
              </h4>
              <p class="text-sm text-muted-foreground">
                对于认证错误、权限错误等特殊场景，使用相应的检查函数进行处理。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<style scoped>
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
}

.prose li {
  margin: 0.25rem 0;
}

code {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}
</style>
