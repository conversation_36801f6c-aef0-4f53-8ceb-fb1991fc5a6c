<script setup lang="ts">
// 测试认证错误处理的页面

definePageMeta({
  title: '认证测试',
})

const { success, error } = useNotification()

function simulateAuthError() {
  localStorage.setItem('simulate-auth-error', 'true')
  success('已开启认证错误模拟', '设置成功')
}

function disableAuthError() {
  localStorage.removeItem('simulate-auth-error')
  success('已关闭认证错误模拟', '设置成功')
}

function testNotification() {
  error('这是一个测试错误通知', '测试通知')
}

function goToFeedback() {
  navigateTo('/feedback')
}
</script>

<template>
  <div class="container mx-auto p-6">
    <div class="max-w-2xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">认证错误处理测试</h1>
      
      <div class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>模拟认证错误</CardTitle>
            <CardDescription>
              测试工单列表在遇到认证错误时的处理逻辑
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex gap-4">
              <Button @click="simulateAuthError" variant="destructive">
                开启认证错误模拟
              </Button>
              <Button @click="disableAuthError" variant="outline">
                关闭认证错误模拟
              </Button>
            </div>
            
            <div class="text-sm text-muted-foreground">
              <p>开启后，访问工单页面将模拟 "Token无效" 错误</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>测试步骤</CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <ol class="list-decimal list-inside space-y-2 text-sm">
              <li>点击 "开启认证错误模拟"</li>
              <li>访问工单页面</li>
              <li>观察错误提示和自动跳转</li>
              <li>返回此页面关闭模拟</li>
            </ol>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>其他测试</CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <Button @click="testNotification" variant="outline">
              测试通知系统
            </Button>
            
            <Button @click="goToFeedback" class="ml-4">
              前往工单页面
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>
