<script setup lang="ts">
import type { ErrorHandleResult } from '~/utils/errorHandler'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useNotification } from '~/composables/useNotification'
import { ErrorCodeHandler } from '~/utils/errorHandler'

const notification = useNotification()

const lastErrorResult = ref<ErrorHandleResult | null>(null)

/**
 * 测试特定错误码
 */
function testError(code: number) {
  const mockError = {
    code,
    msg: `测试错误码 ${code}`,
  }

  const result = ErrorCodeHandler.handleApiError(mockError)
  lastErrorResult.value = result

  if (result.type === 'success') {
    notification.success(result.message, '操作成功')
  }
  else {
    // 根据错误类型显示不同的通知
    switch (result.type) {
      case 'error':
        notification.error(result.message, '操作失败')
        break
      case 'warning':
        notification.warning(result.message, '注意')
        break
      case 'info':
        notification.info(result.message, '提示')
        break
      default:
        notification.error(result.message, '操作失败')
    }
  }
}

/**
 * 测试 HTTP 错误
 */
function testHttpError(status: number) {
  const mockError = {
    response: {
      status,
      data: {
        message: `HTTP ${status} 错误测试`,
      },
    },
  }

  const result = ErrorCodeHandler.handleApiError(mockError)
  lastErrorResult.value = result

  // 根据错误类型显示不同的通知
  switch (result.type) {
    case 'error':
      notification.error(result.message, '操作失败')
      break
    case 'warning':
      notification.warning(result.message, '注意')
      break
    case 'info':
      notification.info(result.message, '提示')
      break
    default:
      notification.error(result.message, '操作失败')
  }
}

definePageMeta({
  title: '错误处理测试',
})
</script>

<template>
  <div class="mx-auto p-6 container">
    <div class="space-y-6">
      <div>
        <h1 class="text-3xl font-bold">
          错误处理测试
        </h1>
        <p class="mt-2 text-muted-foreground">
          测试不同错误码的处理效果
        </p>
      </div>

      <div class="grid grid-cols-1 gap-4 lg:grid-cols-3 md:grid-cols-2">
        <!-- 成功状态 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-green-600">
              成功状态
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full" @click="testError(0)">
              测试 code: 0
            </Button>
            <Button variant="outline" class="w-full" @click="testError(20000)">
              测试 code: 20000
            </Button>
          </CardContent>
        </Card>

        <!-- 数据相关错误 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-blue-600">
              数据错误
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full" @click="testError(1001)">
              查询失败 (1001)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(1002)">
              参数错误 (1002)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(1003)">
              插入失败 (1003)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(1009)">
              参数错误 (1009)
            </Button>
          </CardContent>
        </Card>

        <!-- 认证相关错误 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-yellow-600">
              认证错误
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full" @click="testError(2001)">
              用户名密码错误 (2001)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(2002)">
              用户未登录 (2002)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(2003)">
              Token为空 (2003)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(2004)">
              Token无效 (2004)
            </Button>
          </CardContent>
        </Card>

        <!-- 权限相关错误 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-orange-600">
              权限错误
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full" @click="testError(2005)">
              无权限编辑 (2005)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(2006)">
              无权限删除 (2006)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(2007)">
              无权限创建 (2007)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(2008)">
              权限操作失败 (2008)
            </Button>
          </CardContent>
        </Card>

        <!-- 系统错误 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-red-600">
              系统错误
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full" @click="testError(-1)">
              未知错误 (-1)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(-2)">
              登录功能异常 (-2)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(-3)">
              数据异常 (-3)
            </Button>
            <Button variant="outline" class="w-full" @click="testError(1006)">
              DB连接失败 (1006)
            </Button>
          </CardContent>
        </Card>

        <!-- HTTP 错误 -->
        <Card>
          <CardHeader>
            <CardTitle class="text-purple-600">
              HTTP 错误
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-2">
            <Button variant="outline" class="w-full" @click="testHttpError(400)">
              400 Bad Request
            </Button>
            <Button variant="outline" class="w-full" @click="testHttpError(401)">
              401 Unauthorized
            </Button>
            <Button variant="outline" class="w-full" @click="testHttpError(403)">
              403 Forbidden
            </Button>
            <Button variant="outline" class="w-full" @click="testHttpError(404)">
              404 Not Found
            </Button>
            <Button variant="outline" class="w-full" @click="testHttpError(500)">
              500 Server Error
            </Button>
          </CardContent>
        </Card>
      </div>

      <!-- 错误处理结果显示 -->
      <Card v-if="lastErrorResult">
        <CardHeader>
          <CardTitle>最后一次错误处理结果</CardTitle>
        </CardHeader>
        <CardContent>
          <pre class="overflow-auto rounded-md bg-muted p-4 text-sm">{{ JSON.stringify(lastErrorResult, null, 2) }}</pre>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
