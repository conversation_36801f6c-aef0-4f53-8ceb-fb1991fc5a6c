export default defineNuxtPlugin(async () => {
  const authStore = useAuthStore()
  const accessStore = useAccessStore()
  const route = useRoute()

  // 从localStorage恢复认证数据
  accessStore.restoreFromStorage()
  authStore.restoreFromStorage()

  // 检查是否在登录页面或其他不需要认证的页面
  const isAuthPage = route.path === '/login' || route.meta?.ignoreAccess === true

  // 如果有token但没有用户信息，且不在登录页面，尝试从API获取用户信息
  if (accessStore.accessToken && !authStore.user && !isAuthPage) {
    try {
      await authStore.fetchUserInfo()
    }
    catch (error) {
      console.warn('Failed to restore user info from API:', error)
      // 如果恢复失败，清除无效的token
      authStore.clearAuthData()
    }
  }

  // 认证服务
  const authService = {
    /**
     * 获取当前 token
     */
    getToken() {
      return accessStore.accessToken
    },

    /**
     * 检查是否已认证
     */
    isAuthenticated() {
      return accessStore.isAuthenticated && authStore.isAuthenticated
    },

    /**
     * 刷新 token
     */
    async refreshToken() {
      return await authStore.refreshToken()
    },

    /**
     * 登出
     */
    async logout() {
      await authStore.logout()
    },

    /**
     * 检查认证状态
     */
    async checkAuth() {
      return await authStore.checkAuth()
    },
  }

  // 提供给全局使用
  return {
    provide: {
      auth: authService,
    },
  }
})
