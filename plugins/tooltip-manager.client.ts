export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (import.meta.server) return

  let tooltipFixTimer: NodeJS.Timeout | null = null
  
  // 修复tooltip的核心函数
  const fixTooltips = () => {
    try {
      // 清理所有悬浮的tooltip内容
      const tooltipContents = document.querySelectorAll('[data-radix-tooltip-content]')
      tooltipContents.forEach(content => {
        const parent = content.parentElement
        if (parent && parent.tagName === 'BODY') {
          parent.removeChild(content)
        }
      })
      
      // 重置所有tooltip触发器的状态
      const triggers = document.querySelectorAll('[data-radix-tooltip-trigger]')
      triggers.forEach(trigger => {
        trigger.removeAttribute('data-state')
        trigger.removeAttribute('aria-describedby')
      })
      
      // 清理可能残留的portal容器
      const portals = document.querySelectorAll('[data-radix-portal]')
      portals.forEach(portal => {
        if (portal.children.length === 0) {
          portal.remove()
        }
      })
    } catch (error) {
      console.warn('Error fixing tooltips:', error)
    }
  }
  
  // 防抖的tooltip修复函数
  const debouncedFixTooltips = () => {
    if (tooltipFixTimer) {
      clearTimeout(tooltipFixTimer)
    }
    tooltipFixTimer = setTimeout(fixTooltips, 100)
  }
  
  // 监听各种可能导致tooltip失效的事件
  const setupEventListeners = () => {
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        debouncedFixTooltips()
      }
    })
    
    // 窗口焦点变化
    window.addEventListener('focus', debouncedFixTooltips)
    
    // 路由变化
    window.addEventListener('popstate', debouncedFixTooltips)
    
    // 自定义路由变化事件
    window.addEventListener('urlchange', debouncedFixTooltips)
    
    // DOM变化监听（用于检测动态内容变化）
    const observer = new MutationObserver((mutations) => {
      let shouldFix = false
      mutations.forEach(mutation => {
        // 检查是否有tooltip相关的DOM变化
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              if (element.querySelector?.('[data-radix-tooltip-trigger]') || 
                  element.hasAttribute?.('data-radix-tooltip-trigger')) {
                shouldFix = true
              }
            }
          })
        }
      })
      
      if (shouldFix) {
        debouncedFixTooltips()
      }
    })
    
    // 开始观察DOM变化
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })
    
    // 清理函数
    return () => {
      if (tooltipFixTimer) {
        clearTimeout(tooltipFixTimer)
      }
      observer.disconnect()
    }
  }
  
  // 页面加载完成后设置事件监听器
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupEventListeners)
  } else {
    setupEventListeners()
  }
  
  // 提供全局修复函数
  window.$fixTooltips = debouncedFixTooltips
  
  // 定期检查和修复tooltip状态（作为最后的保障）
  const healthCheckInterval = setInterval(() => {
    // 检查是否有孤立的tooltip内容
    const orphanedTooltips = document.querySelectorAll('body > [data-radix-tooltip-content]')
    if (orphanedTooltips.length > 0) {
      console.warn(`Found ${orphanedTooltips.length} orphaned tooltips, cleaning up...`)
      debouncedFixTooltips()
    }
  }, 30000) // 每30秒检查一次
  
  // 页面卸载时清理
  window.addEventListener('beforeunload', () => {
    clearInterval(healthCheckInterval)
    if (tooltipFixTimer) {
      clearTimeout(tooltipFixTimer)
    }
  })
})

// 扩展全局类型
declare global {
  interface Window {
    $fixTooltips: () => void
  }
}
